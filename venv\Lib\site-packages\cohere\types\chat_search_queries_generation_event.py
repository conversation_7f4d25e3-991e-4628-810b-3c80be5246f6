# This file was auto-generated by Fern from our API Definition.

from .chat_stream_event import ChatStreamEvent
import typing
from .chat_search_query import ChatSearchQuery
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ChatSearchQueriesGenerationEvent(ChatStreamEvent):
    search_queries: typing.List[ChatSearchQuery] = pydantic.Field()
    """
    Generated search queries, meant to be used as part of the RAG flow.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
