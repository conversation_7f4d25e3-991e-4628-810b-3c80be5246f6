"""
Reverie Context Engine
Advanced context understanding and management system
Inspired by Augment's world-leading context engine
"""

import asyncio
import json
import re
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import ast
import sqlite3

from core.logging import logger
from agent.memory import MemoryManager, MemoryType, MemoryImportance


class ContextType(Enum):
    """Types of context"""
    FILE_CONTENT = "file_content"
    PROJECT_STRUCTURE = "project_structure"
    CODE_SYMBOLS = "code_symbols"
    CONVERSATION = "conversation"
    TASK_HISTORY = "task_history"
    USER_INTENT = "user_intent"
    DOMAIN_KNOWLEDGE = "domain_knowledge"
    ERROR_CONTEXT = "error_context"


class ContextScope(Enum):
    """Context scope levels"""
    IMMEDIATE = "immediate"      # Current conversation/task
    SESSION = "session"          # Current session
    PROJECT = "project"          # Current project
    USER = "user"               # User-specific
    GLOBAL = "global"           # System-wide


@dataclass
class ContextItem:
    """Individual context item"""
    context_id: str
    context_type: ContextType
    scope: ContextScope
    content: Dict[str, Any]
    relevance_score: float
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    tags: Set[str] = None
    source_file: Optional[str] = None
    line_range: Optional[Tuple[int, int]] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = set()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "context_id": self.context_id,
            "context_type": self.context_type.value,
            "scope": self.scope.value,
            "content": self.content,
            "relevance_score": self.relevance_score,
            "created_at": self.created_at.isoformat(),
            "last_accessed": self.last_accessed.isoformat(),
            "access_count": self.access_count,
            "tags": list(self.tags),
            "source_file": self.source_file,
            "line_range": self.line_range
        }


class CodeAnalyzer:
    """Advanced code analysis for context extraction"""
    
    def __init__(self):
        self.supported_extensions = {
            '.py': self._analyze_python,
            '.js': self._analyze_javascript,
            '.ts': self._analyze_typescript,
            '.java': self._analyze_java,
            '.cpp': self._analyze_cpp,
            '.c': self._analyze_c,
            '.cs': self._analyze_csharp,
            '.go': self._analyze_go,
            '.rs': self._analyze_rust,
            '.php': self._analyze_php
        }
    
    def analyze_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyze a code file and extract context"""
        try:
            if not file_path.exists():
                return {}
            
            extension = file_path.suffix.lower()
            if extension not in self.supported_extensions:
                return self._analyze_generic(file_path)
            
            analyzer = self.supported_extensions[extension]
            return analyzer(file_path)
            
        except Exception as e:
            logger.error(f"Failed to analyze file {file_path}: {e}")
            return {}
    
    def _analyze_python(self, file_path: Path) -> Dict[str, Any]:
        """Analyze Python file"""
        try:
            content = file_path.read_text(encoding='utf-8')
            tree = ast.parse(content)
            
            context = {
                "file_type": "python",
                "classes": [],
                "functions": [],
                "imports": [],
                "variables": [],
                "docstrings": [],
                "complexity": 0
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    context["classes"].append({
                        "name": node.name,
                        "line": node.lineno,
                        "methods": [n.name for n in node.body if isinstance(n, ast.FunctionDef)],
                        "docstring": ast.get_docstring(node)
                    })
                
                elif isinstance(node, ast.FunctionDef):
                    context["functions"].append({
                        "name": node.name,
                        "line": node.lineno,
                        "args": [arg.arg for arg in node.args.args],
                        "docstring": ast.get_docstring(node),
                        "is_async": isinstance(node, ast.AsyncFunctionDef)
                    })
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            context["imports"].append({
                                "module": alias.name,
                                "alias": alias.asname,
                                "line": node.lineno
                            })
                    else:
                        for alias in node.names:
                            context["imports"].append({
                                "module": node.module,
                                "name": alias.name,
                                "alias": alias.asname,
                                "line": node.lineno
                            })
                
                elif isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            context["variables"].append({
                                "name": target.id,
                                "line": node.lineno
                            })
            
            # Calculate complexity
            context["complexity"] = len(context["classes"]) + len(context["functions"])
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to analyze Python file: {e}")
            return self._analyze_generic(file_path)
    
    def _analyze_javascript(self, file_path: Path) -> Dict[str, Any]:
        """Analyze JavaScript file"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            context = {
                "file_type": "javascript",
                "functions": [],
                "classes": [],
                "imports": [],
                "exports": [],
                "variables": []
            }
            
            # Simple regex-based analysis (could be enhanced with proper JS parser)
            
            # Find functions
            function_pattern = r'(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:async\s+)?function|\b(\w+)\s*\([^)]*\)\s*=>)'
            for match in re.finditer(function_pattern, content):
                func_name = match.group(1) or match.group(2) or match.group(3)
                if func_name:
                    line_num = content[:match.start()].count('\n') + 1
                    context["functions"].append({
                        "name": func_name,
                        "line": line_num
                    })
            
            # Find classes
            class_pattern = r'class\s+(\w+)'
            for match in re.finditer(class_pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                context["classes"].append({
                    "name": match.group(1),
                    "line": line_num
                })
            
            # Find imports
            import_pattern = r'import\s+.*?from\s+[\'"]([^\'"]+)[\'"]'
            for match in re.finditer(import_pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                context["imports"].append({
                    "module": match.group(1),
                    "line": line_num
                })
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to analyze JavaScript file: {e}")
            return self._analyze_generic(file_path)
    
    def _analyze_typescript(self, file_path: Path) -> Dict[str, Any]:
        """Analyze TypeScript file"""
        # Similar to JavaScript but with type information
        return self._analyze_javascript(file_path)
    
    def _analyze_java(self, file_path: Path) -> Dict[str, Any]:
        """Analyze Java file"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            context = {
                "file_type": "java",
                "classes": [],
                "methods": [],
                "imports": [],
                "package": None
            }
            
            # Find package
            package_match = re.search(r'package\s+([\w.]+);', content)
            if package_match:
                context["package"] = package_match.group(1)
            
            # Find classes
            class_pattern = r'(?:public\s+)?(?:abstract\s+)?class\s+(\w+)'
            for match in re.finditer(class_pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                context["classes"].append({
                    "name": match.group(1),
                    "line": line_num
                })
            
            # Find methods
            method_pattern = r'(?:public|private|protected)?\s*(?:static\s+)?(?:\w+\s+)*(\w+)\s*\([^)]*\)\s*{'
            for match in re.finditer(method_pattern, content):
                line_num = content[:match.start()].count('\n') + 1
                context["methods"].append({
                    "name": match.group(1),
                    "line": line_num
                })
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to analyze Java file: {e}")
            return self._analyze_generic(file_path)
    
    def _analyze_cpp(self, file_path: Path) -> Dict[str, Any]:
        """Analyze C++ file"""
        return self._analyze_generic(file_path)
    
    def _analyze_c(self, file_path: Path) -> Dict[str, Any]:
        """Analyze C file"""
        return self._analyze_generic(file_path)
    
    def _analyze_csharp(self, file_path: Path) -> Dict[str, Any]:
        """Analyze C# file"""
        return self._analyze_generic(file_path)
    
    def _analyze_go(self, file_path: Path) -> Dict[str, Any]:
        """Analyze Go file"""
        return self._analyze_generic(file_path)
    
    def _analyze_rust(self, file_path: Path) -> Dict[str, Any]:
        """Analyze Rust file"""
        return self._analyze_generic(file_path)
    
    def _analyze_php(self, file_path: Path) -> Dict[str, Any]:
        """Analyze PHP file"""
        return self._analyze_generic(file_path)
    
    def _analyze_generic(self, file_path: Path) -> Dict[str, Any]:
        """Generic file analysis"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            return {
                "file_type": "generic",
                "line_count": len(content.splitlines()),
                "char_count": len(content),
                "word_count": len(content.split()),
                "extension": file_path.suffix
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze generic file: {e}")
            return {}


class ReverieContextEngine:
    """Advanced context engine for Reverie AI assistant"""
    
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
        self.code_analyzer = CodeAnalyzer()
        self.context_cache: Dict[str, ContextItem] = {}
        self.project_context: Dict[str, Any] = {}
        self.conversation_context: List[Dict[str, Any]] = []
        
        # Context database
        self.db_path = Path("data/context.db")
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_context_db()
        
        logger.info("Reverie Context Engine initialized")
    
    def _init_context_db(self):
        """Initialize context database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS context_items (
                    context_id TEXT PRIMARY KEY,
                    context_type TEXT NOT NULL,
                    scope TEXT NOT NULL,
                    content TEXT NOT NULL,
                    relevance_score REAL NOT NULL,
                    created_at TEXT NOT NULL,
                    last_accessed TEXT NOT NULL,
                    access_count INTEGER DEFAULT 0,
                    tags TEXT,
                    source_file TEXT,
                    line_range TEXT
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_context_type ON context_items(context_type)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_scope ON context_items(scope)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_relevance ON context_items(relevance_score)
            """)
            
            conn.commit()
    
    async def analyze_project_structure(self, project_path: Path) -> Dict[str, Any]:
        """Analyze entire project structure for context"""
        try:
            project_context = {
                "project_path": str(project_path),
                "files": {},
                "structure": {},
                "languages": set(),
                "frameworks": set(),
                "dependencies": {},
                "entry_points": [],
                "config_files": []
            }
            
            # Walk through project files
            for file_path in project_path.rglob("*"):
                if file_path.is_file() and not self._should_ignore_file(file_path):
                    relative_path = file_path.relative_to(project_path)
                    
                    # Analyze file
                    file_analysis = self.code_analyzer.analyze_file(file_path)
                    if file_analysis:
                        project_context["files"][str(relative_path)] = file_analysis
                        
                        # Track languages
                        if "file_type" in file_analysis:
                            project_context["languages"].add(file_analysis["file_type"])
                    
                    # Identify special files
                    if file_path.name in ["package.json", "requirements.txt", "Cargo.toml", "go.mod"]:
                        project_context["config_files"].append(str(relative_path))
                    
                    if file_path.name in ["main.py", "index.js", "main.go", "main.cpp"]:
                        project_context["entry_points"].append(str(relative_path))
            
            # Convert sets to lists for JSON serialization
            project_context["languages"] = list(project_context["languages"])
            project_context["frameworks"] = list(project_context["frameworks"])
            
            # Store in context
            await self._store_context_item(
                context_type=ContextType.PROJECT_STRUCTURE,
                scope=ContextScope.PROJECT,
                content=project_context,
                relevance_score=1.0,
                tags={"project", "structure", "analysis"}
            )
            
            self.project_context = project_context
            return project_context
            
        except Exception as e:
            logger.error(f"Failed to analyze project structure: {e}")
            return {}
    
    def _should_ignore_file(self, file_path: Path) -> bool:
        """Check if file should be ignored"""
        ignore_patterns = [
            ".git", "__pycache__", "node_modules", ".venv", "venv",
            ".pytest_cache", ".mypy_cache", "dist", "build",
            ".DS_Store", "Thumbs.db"
        ]
        
        ignore_extensions = [
            ".pyc", ".pyo", ".pyd", ".so", ".dll", ".dylib",
            ".exe", ".bin", ".obj", ".o", ".a", ".lib"
        ]
        
        # Check if any parent directory matches ignore patterns
        for part in file_path.parts:
            if part in ignore_patterns:
                return True
        
        # Check file extension
        if file_path.suffix in ignore_extensions:
            return True
        
        # Check file size (ignore very large files)
        try:
            if file_path.stat().st_size > 1024 * 1024:  # 1MB
                return True
        except:
            pass
        
        return False
    
    async def extract_file_context(self, file_path: Path, query: str = None) -> List[ContextItem]:
        """Extract relevant context from a specific file"""
        try:
            if not file_path.exists():
                return []
            
            # Analyze file
            file_analysis = self.code_analyzer.analyze_file(file_path)
            if not file_analysis:
                return []
            
            context_items = []
            
            # Create context item for entire file
            file_context = ContextItem(
                context_id=self._generate_context_id(str(file_path)),
                context_type=ContextType.FILE_CONTENT,
                scope=ContextScope.PROJECT,
                content={
                    "file_path": str(file_path),
                    "analysis": file_analysis,
                    "content": file_path.read_text(encoding='utf-8')[:10000]  # First 10k chars
                },
                relevance_score=0.8,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                source_file=str(file_path),
                tags={"file", "code", file_analysis.get("file_type", "unknown")}
            )
            
            context_items.append(file_context)
            
            # Create context items for code symbols
            if "functions" in file_analysis:
                for func in file_analysis["functions"]:
                    func_context = ContextItem(
                        context_id=self._generate_context_id(f"{file_path}:{func['name']}"),
                        context_type=ContextType.CODE_SYMBOLS,
                        scope=ContextScope.PROJECT,
                        content={
                            "symbol_type": "function",
                            "name": func["name"],
                            "file_path": str(file_path),
                            "line": func["line"],
                            "details": func
                        },
                        relevance_score=0.9 if query and query.lower() in func["name"].lower() else 0.6,
                        created_at=datetime.now(),
                        last_accessed=datetime.now(),
                        source_file=str(file_path),
                        line_range=(func["line"], func["line"]),
                        tags={"function", "symbol", func["name"]}
                    )
                    context_items.append(func_context)
            
            if "classes" in file_analysis:
                for cls in file_analysis["classes"]:
                    cls_context = ContextItem(
                        context_id=self._generate_context_id(f"{file_path}:{cls['name']}"),
                        context_type=ContextType.CODE_SYMBOLS,
                        scope=ContextScope.PROJECT,
                        content={
                            "symbol_type": "class",
                            "name": cls["name"],
                            "file_path": str(file_path),
                            "line": cls["line"],
                            "details": cls
                        },
                        relevance_score=0.9 if query and query.lower() in cls["name"].lower() else 0.7,
                        created_at=datetime.now(),
                        last_accessed=datetime.now(),
                        source_file=str(file_path),
                        line_range=(cls["line"], cls["line"]),
                        tags={"class", "symbol", cls["name"]}
                    )
                    context_items.append(cls_context)
            
            # Store context items
            for item in context_items:
                await self._store_context_item_obj(item)
            
            return context_items
            
        except Exception as e:
            logger.error(f"Failed to extract file context: {e}")
            return []
    
    async def get_relevant_context(self, query: str, context_types: List[ContextType] = None,
                                  max_items: int = 20) -> List[ContextItem]:
        """Get relevant context for a query"""
        try:
            # Build search query
            sql_query = """
                SELECT * FROM context_items 
                WHERE content LIKE ? OR tags LIKE ?
            """
            params = [f"%{query}%", f"%{query}%"]
            
            if context_types:
                type_placeholders = ",".join("?" * len(context_types))
                sql_query += f" AND context_type IN ({type_placeholders})"
                params.extend([ct.value for ct in context_types])
            
            sql_query += " ORDER BY relevance_score DESC, last_accessed DESC LIMIT ?"
            params.append(max_items)
            
            context_items = []
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(sql_query, params)
                for row in cursor.fetchall():
                    item = self._row_to_context_item(row)
                    
                    # Update access info
                    item.last_accessed = datetime.now()
                    item.access_count += 1
                    
                    # Calculate dynamic relevance
                    item.relevance_score = self._calculate_relevance(item, query)
                    
                    context_items.append(item)
                    
                    # Update in database
                    conn.execute("""
                        UPDATE context_items 
                        SET last_accessed = ?, access_count = ?, relevance_score = ?
                        WHERE context_id = ?
                    """, (item.last_accessed.isoformat(), item.access_count, 
                         item.relevance_score, item.context_id))
                
                conn.commit()
            
            # Sort by relevance
            context_items.sort(key=lambda x: x.relevance_score, reverse=True)
            
            return context_items
            
        except Exception as e:
            logger.error(f"Failed to get relevant context: {e}")
            return []
    
    def _calculate_relevance(self, context_item: ContextItem, query: str) -> float:
        """Calculate dynamic relevance score"""
        score = context_item.relevance_score
        
        # Boost score based on query matches
        query_lower = query.lower()
        content_str = json.dumps(context_item.content).lower()
        
        # Exact matches
        if query_lower in content_str:
            score += 0.3
        
        # Tag matches
        for tag in context_item.tags:
            if query_lower in tag.lower():
                score += 0.2
        
        # Recent access boost
        hours_since_access = (datetime.now() - context_item.last_accessed).total_seconds() / 3600
        if hours_since_access < 24:
            score += 0.1 * (24 - hours_since_access) / 24
        
        # Access frequency boost
        if context_item.access_count > 5:
            score += 0.1
        
        return min(score, 1.0)
    
    async def _store_context_item(self, context_type: ContextType, scope: ContextScope,
                                 content: Dict[str, Any], relevance_score: float,
                                 tags: Set[str] = None, source_file: str = None,
                                 line_range: Tuple[int, int] = None) -> str:
        """Store a context item"""
        
        context_id = self._generate_context_id(json.dumps(content, sort_keys=True))
        
        item = ContextItem(
            context_id=context_id,
            context_type=context_type,
            scope=scope,
            content=content,
            relevance_score=relevance_score,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            tags=tags or set(),
            source_file=source_file,
            line_range=line_range
        )
        
        await self._store_context_item_obj(item)
        return context_id
    
    async def _store_context_item_obj(self, item: ContextItem):
        """Store a context item object"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO context_items 
                (context_id, context_type, scope, content, relevance_score, 
                 created_at, last_accessed, access_count, tags, source_file, line_range)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                item.context_id,
                item.context_type.value,
                item.scope.value,
                json.dumps(item.content),
                item.relevance_score,
                item.created_at.isoformat(),
                item.last_accessed.isoformat(),
                item.access_count,
                json.dumps(list(item.tags)),
                item.source_file,
                json.dumps(item.line_range) if item.line_range else None
            ))
            conn.commit()
        
        # Add to cache
        self.context_cache[item.context_id] = item
    
    def _row_to_context_item(self, row) -> ContextItem:
        """Convert database row to ContextItem"""
        return ContextItem(
            context_id=row[0],
            context_type=ContextType(row[1]),
            scope=ContextScope(row[2]),
            content=json.loads(row[3]),
            relevance_score=row[4],
            created_at=datetime.fromisoformat(row[5]),
            last_accessed=datetime.fromisoformat(row[6]),
            access_count=row[7],
            tags=set(json.loads(row[8])) if row[8] else set(),
            source_file=row[9],
            line_range=tuple(json.loads(row[10])) if row[10] else None
        )
    
    def _generate_context_id(self, content: str) -> str:
        """Generate unique context ID"""
        return hashlib.md5(content.encode()).hexdigest()
    
    async def add_conversation_context(self, message: str, role: str, metadata: Dict[str, Any] = None, provider: str = None):
        """Add conversation message to context with provider information"""
        context_item = {
            "role": role,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {},
            "provider": provider or "unknown"
        }

        self.conversation_context.append(context_item)

        # Keep only last 50 messages
        if len(self.conversation_context) > 50:
            self.conversation_context = self.conversation_context[-50:]

        # Store in context database
        await self._store_context_item(
            context_type=ContextType.CONVERSATION,
            scope=ContextScope.SESSION,
            content=context_item,
            relevance_score=0.5,
            tags={"conversation", role, provider or "unknown"}
        )

    async def get_conversation_context_for_provider(self, provider: str, max_messages: int = 10) -> List[Dict[str, Any]]:
        """Get conversation context optimized for specific provider"""

        # Get recent messages
        recent_messages = self.conversation_context[-max_messages:] if self.conversation_context else []

        # Format messages based on provider requirements
        formatted_messages = []

        for msg in recent_messages:
            if provider == "openai":
                formatted_messages.append({
                    "role": msg["role"],
                    "content": msg["message"]
                })
            elif provider == "claude":
                # Claude has specific requirements for system messages
                if msg["role"] == "system":
                    # System messages are handled separately in Claude
                    continue
                formatted_messages.append({
                    "role": msg["role"],
                    "content": msg["message"]
                })
            elif provider == "zhipu":
                # Zhipu follows OpenAI format
                formatted_messages.append({
                    "role": msg["role"],
                    "content": msg["message"]
                })
            else:
                # Default format for local server
                formatted_messages.append({
                    "role": msg["role"],
                    "content": msg["message"],
                    "timestamp": msg["timestamp"]
                })

        return formatted_messages

    async def enhance_context_with_provider_capabilities(self, provider: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance context based on provider capabilities"""

        enhanced_context = context.copy()

        if provider == "openai":
            # OpenAI supports function calling
            enhanced_context["supports_functions"] = True
            enhanced_context["max_tokens"] = 4096
            enhanced_context["supports_vision"] = "gpt-4-vision" in context.get("model", "")

        elif provider == "claude":
            # Claude has different capabilities
            enhanced_context["supports_functions"] = True
            enhanced_context["max_tokens"] = 4096
            enhanced_context["supports_vision"] = "claude-3" in context.get("model", "")
            enhanced_context["system_message_separate"] = True

        elif provider == "zhipu":
            # Zhipu AI capabilities
            enhanced_context["supports_functions"] = False  # Limited function support
            enhanced_context["max_tokens"] = 4096
            enhanced_context["supports_vision"] = "glm-4v" in context.get("model", "")

        else:
            # Local server capabilities
            enhanced_context["supports_functions"] = False
            enhanced_context["max_tokens"] = context.get("max_tokens", 4096)
            enhanced_context["supports_vision"] = False

        return enhanced_context
    
    async def get_conversation_context(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation context"""
        return self.conversation_context[-limit:]
    
    async def cleanup_old_context(self, days: int = 30):
        """Clean up old context items"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT context_id FROM context_items 
                WHERE last_accessed < ? AND scope != ?
            """, (cutoff_date.isoformat(), ContextScope.GLOBAL.value))
            
            old_ids = [row[0] for row in cursor.fetchall()]
            
            if old_ids:
                conn.execute("""
                    DELETE FROM context_items 
                    WHERE context_id IN ({})
                """.format(','.join('?' * len(old_ids))), old_ids)
                
                conn.commit()
                
                # Remove from cache
                for context_id in old_ids:
                    self.context_cache.pop(context_id, None)
                
                logger.info(f"Cleaned up {len(old_ids)} old context items")
    
    async def get_context_summary(self) -> Dict[str, Any]:
        """Get summary of current context"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT context_type, scope, COUNT(*) as count
                FROM context_items 
                GROUP BY context_type, scope
            """)
            
            summary = {
                "total_items": 0,
                "by_type": {},
                "by_scope": {},
                "project_context": self.project_context,
                "conversation_length": len(self.conversation_context)
            }
            
            for row in cursor.fetchall():
                context_type, scope, count = row
                summary["total_items"] += count
                
                if context_type not in summary["by_type"]:
                    summary["by_type"][context_type] = 0
                summary["by_type"][context_type] += count
                
                if scope not in summary["by_scope"]:
                    summary["by_scope"][scope] = 0
                summary["by_scope"][scope] += count
            
            return summary

    # Enhanced Semantic Search and Codebase Understanding

    async def semantic_search(self, query: str, context_types: List[ContextType] = None,
                             max_results: int = 20, min_relevance: float = 0.3) -> List[ContextItem]:
        """Advanced semantic search across context items"""
        try:
            # Normalize query
            query_terms = self._extract_search_terms(query)

            # Get candidate context items
            candidates = await self._get_search_candidates(context_types)

            # Score and rank candidates
            scored_items = []
            for item in candidates:
                relevance_score = self._calculate_semantic_relevance(query, query_terms, item)
                if relevance_score >= min_relevance:
                    item.relevance_score = relevance_score
                    item.last_accessed = datetime.now()
                    item.access_count += 1
                    scored_items.append(item)

            # Sort by relevance and return top results
            scored_items.sort(key=lambda x: x.relevance_score, reverse=True)
            results = scored_items[:max_results]

            # Update access statistics
            await self._update_access_statistics(results)

            logger.info(f"Semantic search for '{query}' returned {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            return []

    def _extract_search_terms(self, query: str) -> Dict[str, Any]:
        """Extract and categorize search terms from query"""
        terms = {
            "keywords": [],
            "code_patterns": [],
            "file_patterns": [],
            "function_names": [],
            "class_names": [],
            "variable_names": []
        }

        # Extract basic keywords
        words = re.findall(r'\b\w+\b', query.lower())
        terms["keywords"] = words

        # Extract code patterns
        code_patterns = re.findall(r'`([^`]+)`', query)
        terms["code_patterns"] = code_patterns

        # Extract file patterns
        file_patterns = re.findall(r'\b\w+\.\w+\b', query)
        terms["file_patterns"] = file_patterns

        # Extract function-like patterns
        function_patterns = re.findall(r'\b(\w+)\s*\(', query)
        terms["function_names"] = function_patterns

        # Extract class-like patterns (CamelCase)
        class_patterns = re.findall(r'\b[A-Z][a-zA-Z0-9]*\b', query)
        terms["class_names"] = class_patterns

        return terms

    async def _get_search_candidates(self, context_types: List[ContextType] = None) -> List[ContextItem]:
        """Get candidate context items for search"""
        candidates = []

        # Search in database
        with sqlite3.connect(self.db_path) as conn:
            if context_types:
                type_placeholders = ','.join('?' * len(context_types))
                query = f"""
                    SELECT * FROM context_items
                    WHERE context_type IN ({type_placeholders})
                    ORDER BY last_accessed DESC
                    LIMIT 1000
                """
                cursor = conn.execute(query, [ct.value for ct in context_types])
            else:
                cursor = conn.execute("""
                    SELECT * FROM context_items
                    ORDER BY last_accessed DESC
                    LIMIT 1000
                """)

            for row in cursor.fetchall():
                try:
                    item = self._row_to_context_item(row)
                    candidates.append(item)
                except Exception as e:
                    logger.warning(f"Failed to parse context item: {e}")

        # Add cached items
        for item in self.context_cache.values():
            if not context_types or item.context_type in context_types:
                candidates.append(item)

        # Remove duplicates
        seen_ids = set()
        unique_candidates = []
        for item in candidates:
            if item.context_id not in seen_ids:
                seen_ids.add(item.context_id)
                unique_candidates.append(item)

        return unique_candidates

    def _calculate_semantic_relevance(self, query: str, query_terms: Dict[str, Any],
                                    context_item: ContextItem) -> float:
        """Calculate semantic relevance score between query and context item"""
        score = 0.0

        # Convert content to searchable text
        content_text = self._extract_searchable_text(context_item.content)
        content_lower = content_text.lower()

        # Keyword matching
        keyword_score = 0.0
        for keyword in query_terms["keywords"]:
            if keyword in content_lower:
                keyword_score += 1.0
                # Boost for exact matches
                if f" {keyword} " in content_lower:
                    keyword_score += 0.5

        if query_terms["keywords"]:
            keyword_score = keyword_score / len(query_terms["keywords"])

        # Code pattern matching
        code_score = 0.0
        for pattern in query_terms["code_patterns"]:
            if pattern.lower() in content_lower:
                code_score += 2.0  # Higher weight for code patterns

        # File pattern matching
        file_score = 0.0
        if context_item.source_file:
            file_name = Path(context_item.source_file).name.lower()
            for pattern in query_terms["file_patterns"]:
                if pattern.lower() in file_name:
                    file_score += 1.5

        # Function name matching
        function_score = 0.0
        for func_name in query_terms["function_names"]:
            if func_name.lower() in content_lower:
                function_score += 1.5
                # Extra boost if it's a function definition
                if f"def {func_name}" in content_lower or f"function {func_name}" in content_lower:
                    function_score += 1.0

        # Class name matching
        class_score = 0.0
        for class_name in query_terms["class_names"]:
            if class_name.lower() in content_lower:
                class_score += 1.5
                # Extra boost if it's a class definition
                if f"class {class_name}" in content_lower:
                    class_score += 1.0

        # Context type relevance
        type_score = 0.0
        if "function" in query.lower() and context_item.context_type == ContextType.CODE_SYMBOLS:
            type_score += 0.5
        elif "file" in query.lower() and context_item.context_type == ContextType.FILE_CONTENT:
            type_score += 0.5
        elif "project" in query.lower() and context_item.context_type == ContextType.PROJECT_STRUCTURE:
            type_score += 0.5

        # Tag matching
        tag_score = 0.0
        for keyword in query_terms["keywords"]:
            if keyword in context_item.tags:
                tag_score += 0.5

        # Recency boost
        recency_score = 0.0
        days_old = (datetime.now() - context_item.last_accessed).days
        if days_old < 1:
            recency_score = 0.3
        elif days_old < 7:
            recency_score = 0.2
        elif days_old < 30:
            recency_score = 0.1

        # Access frequency boost
        frequency_score = min(context_item.access_count * 0.1, 0.5)

        # Combine scores
        total_score = (
            keyword_score * 0.3 +
            code_score * 0.2 +
            file_score * 0.15 +
            function_score * 0.15 +
            class_score * 0.1 +
            type_score * 0.05 +
            tag_score * 0.03 +
            recency_score * 0.01 +
            frequency_score * 0.01
        )

        return min(total_score, 1.0)  # Cap at 1.0

    def _extract_searchable_text(self, content: Dict[str, Any]) -> str:
        """Extract searchable text from content dictionary"""
        text_parts = []

        def extract_text(obj, depth=0):
            if depth > 5:  # Prevent infinite recursion
                return

            if isinstance(obj, str):
                text_parts.append(obj)
            elif isinstance(obj, dict):
                for key, value in obj.items():
                    text_parts.append(str(key))
                    extract_text(value, depth + 1)
            elif isinstance(obj, list):
                for item in obj:
                    extract_text(item, depth + 1)
            else:
                text_parts.append(str(obj))

        extract_text(content)
        return " ".join(text_parts)

    def _row_to_context_item(self, row) -> ContextItem:
        """Convert database row to ContextItem"""
        return ContextItem(
            context_id=row[0],
            context_type=ContextType(row[1]),
            scope=ContextScope(row[2]),
            content=json.loads(row[3]),
            relevance_score=row[4],
            created_at=datetime.fromisoformat(row[5]),
            last_accessed=datetime.fromisoformat(row[6]),
            access_count=row[7],
            tags=set(json.loads(row[8])) if row[8] else set(),
            source_file=row[9],
            line_range=tuple(json.loads(row[10])) if row[10] else None
        )

    async def _update_access_statistics(self, context_items: List[ContextItem]):
        """Update access statistics for context items"""
        if not context_items:
            return

        try:
            with sqlite3.connect(self.db_path) as conn:
                for item in context_items:
                    conn.execute("""
                        UPDATE context_items
                        SET last_accessed = ?, access_count = ?
                        WHERE context_id = ?
                    """, (item.last_accessed.isoformat(), item.access_count, item.context_id))

                conn.commit()
        except Exception as e:
            logger.error(f"Failed to update access statistics: {e}")

    async def analyze_codebase_patterns(self, project_path: Path) -> Dict[str, Any]:
        """Analyze codebase for patterns and relationships"""
        try:
            patterns = {
                "architectural_patterns": [],
                "design_patterns": [],
                "code_smells": [],
                "dependencies": {},
                "complexity_metrics": {},
                "test_coverage": {},
                "documentation_coverage": {}
            }

            # Analyze architectural patterns
            patterns["architectural_patterns"] = await self._detect_architectural_patterns(project_path)

            # Analyze design patterns
            patterns["design_patterns"] = await self._detect_design_patterns(project_path)

            # Analyze code quality
            patterns["code_smells"] = await self._detect_code_smells(project_path)

            # Analyze dependencies
            patterns["dependencies"] = await self._analyze_dependencies(project_path)

            # Calculate complexity metrics
            patterns["complexity_metrics"] = await self._calculate_complexity_metrics(project_path)

            # Store pattern analysis in context
            await self._store_context_item(
                context_type=ContextType.DOMAIN_KNOWLEDGE,
                scope=ContextScope.PROJECT,
                content=patterns,
                relevance_score=0.9,
                tags={"patterns", "analysis", "codebase"}
            )

            return patterns

        except Exception as e:
            logger.error(f"Failed to analyze codebase patterns: {e}")
            return {}

    async def _detect_architectural_patterns(self, project_path: Path) -> List[Dict[str, Any]]:
        """Detect architectural patterns in the codebase"""
        patterns = []

        try:
            # Check for MVC pattern
            has_models = any(project_path.rglob("*model*"))
            has_views = any(project_path.rglob("*view*"))
            has_controllers = any(project_path.rglob("*controller*"))

            if has_models and has_views and has_controllers:
                patterns.append({
                    "pattern": "MVC",
                    "confidence": 0.8,
                    "evidence": ["models directory", "views directory", "controllers directory"]
                })

            # Check for microservices pattern
            service_dirs = [d for d in project_path.iterdir()
                          if d.is_dir() and "service" in d.name.lower()]
            if len(service_dirs) > 2:
                patterns.append({
                    "pattern": "Microservices",
                    "confidence": 0.7,
                    "evidence": [f"{len(service_dirs)} service directories"]
                })

            # Check for layered architecture
            has_data_layer = any(project_path.rglob("*data*")) or any(project_path.rglob("*repository*"))
            has_business_layer = any(project_path.rglob("*business*")) or any(project_path.rglob("*service*"))
            has_presentation_layer = any(project_path.rglob("*ui*")) or any(project_path.rglob("*web*"))

            if has_data_layer and has_business_layer and has_presentation_layer:
                patterns.append({
                    "pattern": "Layered Architecture",
                    "confidence": 0.6,
                    "evidence": ["data layer", "business layer", "presentation layer"]
                })

        except Exception as e:
            logger.error(f"Failed to detect architectural patterns: {e}")

        return patterns

    async def _detect_design_patterns(self, project_path: Path) -> List[Dict[str, Any]]:
        """Detect design patterns in the codebase"""
        patterns = []

        try:
            # Analyze Python files for design patterns
            for py_file in project_path.rglob("*.py"):
                if self._should_ignore_file(py_file):
                    continue

                try:
                    content = py_file.read_text(encoding='utf-8')

                    # Singleton pattern
                    if "class" in content and "__new__" in content and "_instance" in content:
                        patterns.append({
                            "pattern": "Singleton",
                            "file": str(py_file),
                            "confidence": 0.7
                        })

                    # Factory pattern
                    if "Factory" in content or ("create" in content and "class" in content):
                        patterns.append({
                            "pattern": "Factory",
                            "file": str(py_file),
                            "confidence": 0.6
                        })

                    # Observer pattern
                    if ("observer" in content.lower() or
                        ("notify" in content and "subscribe" in content)):
                        patterns.append({
                            "pattern": "Observer",
                            "file": str(py_file),
                            "confidence": 0.7
                        })

                except Exception:
                    continue

        except Exception as e:
            logger.error(f"Failed to detect design patterns: {e}")

        return patterns

    async def _detect_code_smells(self, project_path: Path) -> List[Dict[str, Any]]:
        """Detect code smells in the codebase"""
        smells = []

        try:
            for py_file in project_path.rglob("*.py"):
                if self._should_ignore_file(py_file):
                    continue

                try:
                    content = py_file.read_text(encoding='utf-8')
                    lines = content.split('\n')

                    # Long method smell
                    in_function = False
                    function_lines = 0
                    function_name = ""

                    for i, line in enumerate(lines):
                        if line.strip().startswith('def '):
                            if in_function and function_lines > 50:
                                smells.append({
                                    "smell": "Long Method",
                                    "file": str(py_file),
                                    "function": function_name,
                                    "lines": function_lines,
                                    "severity": "medium"
                                })

                            in_function = True
                            function_lines = 1
                            function_name = line.strip().split('(')[0].replace('def ', '')
                        elif in_function:
                            if line.strip() and not line.startswith(' '):
                                # End of function
                                if function_lines > 50:
                                    smells.append({
                                        "smell": "Long Method",
                                        "file": str(py_file),
                                        "function": function_name,
                                        "lines": function_lines,
                                        "severity": "medium"
                                    })
                                in_function = False
                            else:
                                function_lines += 1

                    # Large class smell
                    if len(lines) > 500:
                        smells.append({
                            "smell": "Large Class",
                            "file": str(py_file),
                            "lines": len(lines),
                            "severity": "high"
                        })

                    # Duplicate code smell (simple check)
                    line_counts = {}
                    for line in lines:
                        stripped = line.strip()
                        if len(stripped) > 20 and not stripped.startswith('#'):
                            line_counts[stripped] = line_counts.get(stripped, 0) + 1

                    for line, count in line_counts.items():
                        if count > 3:
                            smells.append({
                                "smell": "Duplicate Code",
                                "file": str(py_file),
                                "line": line[:50] + "...",
                                "occurrences": count,
                                "severity": "medium"
                            })

                except Exception:
                    continue

        except Exception as e:
            logger.error(f"Failed to detect code smells: {e}")

        return smells

    async def _analyze_dependencies(self, project_path: Path) -> Dict[str, Any]:
        """Analyze project dependencies"""
        dependencies = {
            "internal": {},
            "external": {},
            "circular": []
        }

        try:
            # Analyze Python imports
            for py_file in project_path.rglob("*.py"):
                if self._should_ignore_file(py_file):
                    continue

                try:
                    content = py_file.read_text(encoding='utf-8')
                    tree = ast.parse(content)

                    file_deps = []
                    for node in ast.walk(tree):
                        if isinstance(node, ast.Import):
                            for alias in node.names:
                                file_deps.append(alias.name)
                        elif isinstance(node, ast.ImportFrom):
                            if node.module:
                                file_deps.append(node.module)

                    dependencies["internal"][str(py_file)] = file_deps

                except Exception:
                    continue

            # TODO: Detect circular dependencies
            # This would require more sophisticated analysis

        except Exception as e:
            logger.error(f"Failed to analyze dependencies: {e}")

        return dependencies

    async def _calculate_complexity_metrics(self, project_path: Path) -> Dict[str, Any]:
        """Calculate complexity metrics for the codebase"""
        metrics = {
            "total_files": 0,
            "total_lines": 0,
            "average_file_size": 0,
            "cyclomatic_complexity": {},
            "maintainability_index": {}
        }

        try:
            file_sizes = []

            for py_file in project_path.rglob("*.py"):
                if self._should_ignore_file(py_file):
                    continue

                try:
                    content = py_file.read_text(encoding='utf-8')
                    lines = len(content.split('\n'))

                    metrics["total_files"] += 1
                    metrics["total_lines"] += lines
                    file_sizes.append(lines)

                    # Simple cyclomatic complexity calculation
                    complexity = content.count('if ') + content.count('elif ') + content.count('for ') + \
                               content.count('while ') + content.count('except ') + content.count('and ') + \
                               content.count('or ')

                    metrics["cyclomatic_complexity"][str(py_file)] = complexity

                except Exception:
                    continue

            if file_sizes:
                metrics["average_file_size"] = sum(file_sizes) / len(file_sizes)

        except Exception as e:
            logger.error(f"Failed to calculate complexity metrics: {e}")

        return metrics
