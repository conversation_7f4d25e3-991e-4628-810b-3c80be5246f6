"""Unicode Properties from Unicode version 13.0.0 (autogen)."""
from __future__ import annotations

unicode_bidi_paired_bracket_type: dict[str, str] = {
    "^c": "\x00-\x28\x2a-\x5c\x5c\x5c\x5e-\x5c\x7c\x5c\x7e-\u0f3a\u0f3c\u0f3e-\u169b\u169d-\u2045\u2047-\u207d\u207f-\u208d\u208f-\u2308\u230a\u230c-\u2329\u232b-\u2768\u276a\u276c\u276e\u2770\u2772\u2774\u2776-\u27c5\u27c7-\u27e6\u27e8\u27ea\u27ec\u27ee\u27f0-\u2983\u2985\u2987\u2989\u298b\u298d\u298f\u2991\u2993\u2995\u2997\u2999-\u29d8\u29da\u29dc-\u29fc\u29fe-\u2e22\u2e24\u2e26\u2e28\u2e2a-\u3008\u300a\u300c\u300e\u3010\u3012-\u3014\u3016\u3018\u301a\u301c-\ufe59\ufe5b\ufe5d\ufe5f-\uff08\uff0a-\uff3c\uff3e-\uff5c\uff5e-\uff5f\uff61-\uff62\uff64-\U0010ffff",
    "^n": "\x28-\x29\x5c\x5b\x5c\x5d\x7b\x7d\u0f3a-\u0f3d\u169b-\u169c\u2045-\u2046\u207d-\u207e\u208d-\u208e\u2308-\u230b\u2329-\u232a\u2768-\u2775\u27c5-\u27c6\u27e6-\u27ef\u2983-\u2998\u29d8-\u29db\u29fc-\u29fd\u2e22-\u2e29\u3008-\u3011\u3014-\u301b\ufe59-\ufe5e\uff08-\uff09\uff3b\uff3d\uff5b\uff5d\uff5f-\uff60\uff62-\uff63",
    "^o": "\x00-\x27\x29-\x5a\x5c\x5c-\x7a\x5c\x7c-\u0f39\u0f3b\u0f3d-\u169a\u169c-\u2044\u2046-\u207c\u207e-\u208c\u208e-\u2307\u2309\u230b-\u2328\u232a-\u2767\u2769\u276b\u276d\u276f\u2771\u2773\u2775-\u27c4\u27c6-\u27e5\u27e7\u27e9\u27eb\u27ed\u27ef-\u2982\u2984\u2986\u2988\u298a\u298c\u298e\u2990\u2992\u2994\u2996\u2998-\u29d7\u29d9\u29db-\u29fb\u29fd-\u2e21\u2e23\u2e25\u2e27\u2e29-\u3007\u3009\u300b\u300d\u300f\u3011-\u3013\u3015\u3017\u3019\u301b-\ufe58\ufe5a\ufe5c\ufe5e-\uff07\uff09-\uff3a\uff3c-\uff5a\uff5c-\uff5e\uff60-\uff61\uff63-\U0010ffff",
    "c": "\x29\x5c\x5d\x7d\u0f3b\u0f3d\u169c\u2046\u207e\u208e\u2309\u230b\u232a\u2769\u276b\u276d\u276f\u2771\u2773\u2775\u27c6\u27e7\u27e9\u27eb\u27ed\u27ef\u2984\u2986\u2988\u298a\u298c\u298e\u2990\u2992\u2994\u2996\u2998\u29d9\u29db\u29fd\u2e23\u2e25\u2e27\u2e29\u3009\u300b\u300d\u300f\u3011\u3015\u3017\u3019\u301b\ufe5a\ufe5c\ufe5e\uff09\uff3d\uff5d\uff60\uff63",
    "n": "\x00-\x27\x2a-\x5a\x5c\x5c\x5c\x5e-\x7a\x5c\x7c\x5c\x7e-\u0f39\u0f3e-\u169a\u169d-\u2044\u2047-\u207c\u207f-\u208c\u208f-\u2307\u230c-\u2328\u232b-\u2767\u2776-\u27c4\u27c7-\u27e5\u27f0-\u2982\u2999-\u29d7\u29dc-\u29fb\u29fe-\u2e21\u2e2a-\u3007\u3012-\u3013\u301c-\ufe58\ufe5f-\uff07\uff0a-\uff3a\uff3c\uff3e-\uff5a\uff5c\uff5e\uff61\uff64-\U0010ffff",
    "o": "\x28\x5c\x5b\x7b\u0f3a\u0f3c\u169b\u2045\u207d\u208d\u2308\u230a\u2329\u2768\u276a\u276c\u276e\u2770\u2772\u2774\u27c5\u27e6\u27e8\u27ea\u27ec\u27ee\u2983\u2985\u2987\u2989\u298b\u298d\u298f\u2991\u2993\u2995\u2997\u29d8\u29da\u29fc\u2e22\u2e24\u2e26\u2e28\u3008\u300a\u300c\u300e\u3010\u3014\u3016\u3018\u301a\ufe59\ufe5b\ufe5d\uff08\uff3b\uff5b\uff5f\uff62"
}
ascii_bidi_paired_bracket_type: dict[str, str] = {
    "^c": "\x00-\x28\x2a-\x5c\x5c\x5c\x5e-\x5c\x7c\x5c\x7e-\U0010ffff",
    "^n": "\x28-\x29\x5c\x5b\x5c\x5d\x7b\x7d",
    "^o": "\x00-\x27\x29-\x5a\x5c\x5c-\x7a\x5c\x7c-\U0010ffff",
    "c": "\x29\x5c\x5d\x7d",
    "n": "\x00-\x27\x2a-\x5a\x5c\x5c\x5c\x5e-\x7a\x5c\x7c\x5c\x7e-\U0010ffff",
    "o": "\x28\x5c\x5b\x7b"
}
