"""
Memory and Context Management for Rilance Agent
Implements long-term memory, context management, and knowledge retrieval
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from enum import Enum
from pathlib import Path
import sqlite3
import asyncio
from dataclasses import dataclass, asdict

from core.logging import logger


class MemoryType(Enum):
    """Types of memory entries"""
    TASK_RESULT = "task_result"
    USER_PREFERENCE = "user_preference"
    LEARNED_PATTERN = "learned_pattern"
    CONTEXT_INFO = "context_info"
    TOOL_USAGE = "tool_usage"
    ERROR_PATTERN = "error_pattern"
    SUCCESS_PATTERN = "success_pattern"


class MemoryImportance(Enum):
    """Memory importance levels"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class MemoryEntry:
    """Individual memory entry"""
    memory_id: str
    memory_type: MemoryType
    content: Dict[str, Any]
    importance: MemoryImportance
    user_id: str
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    tags: Set[str] = None
    expires_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = set()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "memory_id": self.memory_id,
            "memory_type": self.memory_type.value,
            "content": self.content,
            "importance": self.importance.value,
            "user_id": self.user_id,
            "created_at": self.created_at.isoformat(),
            "last_accessed": self.last_accessed.isoformat(),
            "access_count": self.access_count,
            "tags": list(self.tags),
            "expires_at": self.expires_at.isoformat() if self.expires_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryEntry':
        """Create from dictionary"""
        return cls(
            memory_id=data["memory_id"],
            memory_type=MemoryType(data["memory_type"]),
            content=data["content"],
            importance=MemoryImportance(data["importance"]),
            user_id=data["user_id"],
            created_at=datetime.fromisoformat(data["created_at"]),
            last_accessed=datetime.fromisoformat(data["last_accessed"]),
            access_count=data.get("access_count", 0),
            tags=set(data.get("tags", [])),
            expires_at=datetime.fromisoformat(data["expires_at"]) if data.get("expires_at") else None
        )


class ContextManager:
    """Manages conversation and task context"""
    
    def __init__(self):
        self.active_contexts: Dict[str, Dict[str, Any]] = {}
        self.context_history: Dict[str, List[Dict[str, Any]]] = {}
        
    def create_context(self, context_id: str, user_id: str, context_type: str = "conversation") -> Dict[str, Any]:
        """Create a new context"""
        context = {
            "context_id": context_id,
            "user_id": user_id,
            "context_type": context_type,
            "created_at": datetime.now(),
            "last_updated": datetime.now(),
            "variables": {},
            "history": [],
            "metadata": {}
        }
        
        self.active_contexts[context_id] = context
        
        if user_id not in self.context_history:
            self.context_history[user_id] = []
        
        return context
    
    def update_context(self, context_id: str, key: str, value: Any):
        """Update context variable"""
        if context_id in self.active_contexts:
            context = self.active_contexts[context_id]
            context["variables"][key] = value
            context["last_updated"] = datetime.now()
            
            # Add to history
            context["history"].append({
                "timestamp": datetime.now(),
                "action": "update",
                "key": key,
                "value": value
            })
    
    def get_context(self, context_id: str) -> Optional[Dict[str, Any]]:
        """Get context by ID"""
        return self.active_contexts.get(context_id)
    
    def get_user_contexts(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all contexts for a user"""
        return [ctx for ctx in self.active_contexts.values() if ctx["user_id"] == user_id]
    
    def merge_contexts(self, primary_context_id: str, secondary_context_id: str):
        """Merge two contexts"""
        primary = self.active_contexts.get(primary_context_id)
        secondary = self.active_contexts.get(secondary_context_id)
        
        if primary and secondary:
            # Merge variables
            primary["variables"].update(secondary["variables"])
            
            # Merge history
            primary["history"].extend(secondary["history"])
            
            # Update timestamp
            primary["last_updated"] = datetime.now()
            
            # Remove secondary context
            del self.active_contexts[secondary_context_id]


class MemoryManager:
    """Advanced memory management system for the Agent"""
    
    def __init__(self, db_path: str = "data/agent_memory.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.context_manager = ContextManager()
        self.memory_cache: Dict[str, MemoryEntry] = {}
        self.user_profiles: Dict[str, Dict[str, Any]] = {}
        
        # Initialize database
        self._init_database()
        
        # Load recent memories into cache
        asyncio.create_task(self._load_recent_memories())
        
        logger.info(f"Memory Manager initialized with database: {self.db_path}")
    
    def _init_database(self):
        """Initialize SQLite database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS memories (
                    memory_id TEXT PRIMARY KEY,
                    memory_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    importance INTEGER NOT NULL,
                    user_id TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    last_accessed TEXT NOT NULL,
                    access_count INTEGER DEFAULT 0,
                    tags TEXT,
                    expires_at TEXT
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_user_id ON memories(user_id)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_memory_type ON memories(memory_type)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_importance ON memories(importance)
            """)
            
            conn.commit()
    
    async def _load_recent_memories(self):
        """Load recent memories into cache"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT * FROM memories 
                    WHERE last_accessed > ? 
                    ORDER BY importance DESC, last_accessed DESC 
                    LIMIT 1000
                """, (datetime.now() - timedelta(days=7)).isoformat())
                
                for row in cursor.fetchall():
                    memory = self._row_to_memory(row)
                    self.memory_cache[memory.memory_id] = memory
            
            logger.info(f"Loaded {len(self.memory_cache)} recent memories into cache")
            
        except Exception as e:
            logger.error(f"Failed to load recent memories: {e}")
    
    def _row_to_memory(self, row) -> MemoryEntry:
        """Convert database row to MemoryEntry"""
        return MemoryEntry(
            memory_id=row[0],
            memory_type=MemoryType(row[1]),
            content=json.loads(row[2]),
            importance=MemoryImportance(row[3]),
            user_id=row[4],
            created_at=datetime.fromisoformat(row[5]),
            last_accessed=datetime.fromisoformat(row[6]),
            access_count=row[7],
            tags=set(json.loads(row[8])) if row[8] else set(),
            expires_at=datetime.fromisoformat(row[9]) if row[9] else None
        )
    
    async def store_memory(self, memory_type: MemoryType, content: Dict[str, Any], 
                          user_id: str, importance: MemoryImportance = MemoryImportance.MEDIUM,
                          tags: Set[str] = None, expires_in_days: Optional[int] = None) -> str:
        """Store a new memory"""
        
        memory_id = str(uuid.uuid4())
        now = datetime.now()
        expires_at = now + timedelta(days=expires_in_days) if expires_in_days else None
        
        memory = MemoryEntry(
            memory_id=memory_id,
            memory_type=memory_type,
            content=content,
            importance=importance,
            user_id=user_id,
            created_at=now,
            last_accessed=now,
            tags=tags or set(),
            expires_at=expires_at
        )
        
        # Store in database
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO memories 
                (memory_id, memory_type, content, importance, user_id, created_at, last_accessed, tags, expires_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                memory.memory_id,
                memory.memory_type.value,
                json.dumps(memory.content),
                memory.importance.value,
                memory.user_id,
                memory.created_at.isoformat(),
                memory.last_accessed.isoformat(),
                json.dumps(list(memory.tags)),
                memory.expires_at.isoformat() if memory.expires_at else None
            ))
            conn.commit()
        
        # Add to cache
        self.memory_cache[memory_id] = memory
        
        logger.info(f"Stored memory: {memory_id} for user {user_id}")
        return memory_id
    
    async def retrieve_memories(self, user_id: str, memory_type: Optional[MemoryType] = None,
                               tags: Optional[Set[str]] = None, limit: int = 50) -> List[MemoryEntry]:
        """Retrieve memories with filtering"""
        
        # Build query
        query = "SELECT * FROM memories WHERE user_id = ?"
        params = [user_id]
        
        if memory_type:
            query += " AND memory_type = ?"
            params.append(memory_type.value)
        
        # Handle tag filtering (simplified)
        if tags:
            tag_conditions = []
            for tag in tags:
                tag_conditions.append("tags LIKE ?")
                params.append(f"%{tag}%")
            
            if tag_conditions:
                query += " AND (" + " OR ".join(tag_conditions) + ")"
        
        # Add ordering and limit
        query += " ORDER BY importance DESC, last_accessed DESC LIMIT ?"
        params.append(limit)
        
        memories = []
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(query, params)
            for row in cursor.fetchall():
                memory = self._row_to_memory(row)
                
                # Update access info
                memory.last_accessed = datetime.now()
                memory.access_count += 1
                
                # Update in database
                conn.execute("""
                    UPDATE memories 
                    SET last_accessed = ?, access_count = ? 
                    WHERE memory_id = ?
                """, (memory.last_accessed.isoformat(), memory.access_count, memory.memory_id))
                
                memories.append(memory)
                
                # Update cache
                self.memory_cache[memory.memory_id] = memory
            
            conn.commit()
        
        return memories
    
    async def search_memories(self, user_id: str, query: str, limit: int = 20) -> List[MemoryEntry]:
        """Search memories by content"""
        
        # Simple text search (could be enhanced with vector search)
        memories = []
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT * FROM memories 
                WHERE user_id = ? AND (
                    content LIKE ? OR 
                    tags LIKE ?
                )
                ORDER BY importance DESC, last_accessed DESC 
                LIMIT ?
            """, (user_id, f"%{query}%", f"%{query}%", limit))
            
            for row in cursor.fetchall():
                memory = self._row_to_memory(row)
                memories.append(memory)
        
        return memories
    
    async def update_memory(self, memory_id: str, content: Optional[Dict[str, Any]] = None,
                           importance: Optional[MemoryImportance] = None,
                           tags: Optional[Set[str]] = None):
        """Update existing memory"""
        
        if memory_id in self.memory_cache:
            memory = self.memory_cache[memory_id]
            
            if content is not None:
                memory.content = content
            if importance is not None:
                memory.importance = importance
            if tags is not None:
                memory.tags = tags
            
            memory.last_accessed = datetime.now()
            
            # Update database
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE memories 
                    SET content = ?, importance = ?, tags = ?, last_accessed = ?
                    WHERE memory_id = ?
                """, (
                    json.dumps(memory.content),
                    memory.importance.value,
                    json.dumps(list(memory.tags)),
                    memory.last_accessed.isoformat(),
                    memory_id
                ))
                conn.commit()
    
    async def cleanup_expired_memories(self):
        """Remove expired memories"""
        now = datetime.now()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT memory_id FROM memories 
                WHERE expires_at IS NOT NULL AND expires_at < ?
            """, (now.isoformat(),))
            
            expired_ids = [row[0] for row in cursor.fetchall()]
            
            if expired_ids:
                conn.execute("""
                    DELETE FROM memories 
                    WHERE memory_id IN ({})
                """.format(','.join('?' * len(expired_ids))), expired_ids)
                
                conn.commit()
                
                # Remove from cache
                for memory_id in expired_ids:
                    self.memory_cache.pop(memory_id, None)
                
                logger.info(f"Cleaned up {len(expired_ids)} expired memories")
    
    async def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get user profile from memories"""
        if user_id not in self.user_profiles:
            # Build profile from memories
            preferences = await self.retrieve_memories(
                user_id, 
                memory_type=MemoryType.USER_PREFERENCE,
                limit=100
            )
            
            profile = {
                "user_id": user_id,
                "preferences": {},
                "patterns": {},
                "statistics": {
                    "total_memories": len(self.memory_cache),
                    "task_completions": 0,
                    "tool_usage": {}
                }
            }
            
            # Extract preferences
            for memory in preferences:
                profile["preferences"].update(memory.content)
            
            self.user_profiles[user_id] = profile
        
        return self.user_profiles[user_id]
    
    async def learn_from_task(self, task_id: str, task_description: str, 
                             result: str, user_id: str, success: bool):
        """Learn patterns from task execution"""
        
        # Store task result
        await self.store_memory(
            memory_type=MemoryType.TASK_RESULT,
            content={
                "task_id": task_id,
                "description": task_description,
                "result": result,
                "success": success,
                "timestamp": datetime.now().isoformat()
            },
            user_id=user_id,
            importance=MemoryImportance.HIGH if success else MemoryImportance.MEDIUM,
            tags={"task", "result", "success" if success else "failure"}
        )
        
        # Learn patterns
        if success:
            await self.store_memory(
                memory_type=MemoryType.SUCCESS_PATTERN,
                content={
                    "task_type": self._categorize_task(task_description),
                    "description": task_description,
                    "result": result
                },
                user_id=user_id,
                importance=MemoryImportance.MEDIUM,
                tags={"pattern", "success"}
            )
    
    def _categorize_task(self, description: str) -> str:
        """Simple task categorization"""
        description_lower = description.lower()
        
        if any(word in description_lower for word in ["file", "read", "write"]):
            return "file_operations"
        elif any(word in description_lower for word in ["search", "find", "lookup"]):
            return "search"
        elif any(word in description_lower for word in ["analyze", "review", "examine"]):
            return "analysis"
        elif any(word in description_lower for word in ["create", "generate", "build"]):
            return "creation"
        else:
            return "general"
    
    async def get_relevant_context(self, user_id: str, current_task: str) -> Dict[str, Any]:
        """Get relevant context for current task"""
        
        # Search for similar tasks
        similar_memories = await self.search_memories(user_id, current_task, limit=10)
        
        # Get recent context
        recent_memories = await self.retrieve_memories(
            user_id, 
            limit=20
        )
        
        # Get user preferences
        user_profile = await self.get_user_profile(user_id)
        
        return {
            "similar_tasks": [m.to_dict() for m in similar_memories],
            "recent_context": [m.to_dict() for m in recent_memories[-5:]],
            "user_preferences": user_profile.get("preferences", {}),
            "learned_patterns": user_profile.get("patterns", {})
        }
