2025-07-05 00:41:52.346 | ERROR    | models.manager:_download_with_huggingface_hub:1014 | Download error: ('Connection broken: IncompleteRead(3573144667 bytes read, 1394070693 more expected)', IncompleteRead(3573144667 bytes read, 1394070693 more expected)) | {}
2025-07-05 00:41:52.471 | ERROR    | models.manager:_download_with_git:946 | Git command error: Cmd('git') failed due to: exit code(128)
  cmdline: git clone -v --depth=1 --single-branch --progress -- https://huggingface.co/Menlo/Jan-nano-128k H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k | {}
2025-07-05 01:00:11.846 | ERROR    | models.manager:_download_with_http:1080 | ❌ No files could be downloaded via HTTP | {}
2025-07-05 01:00:11.847 | ERROR    | models.manager:download_model:190 | Failed to download model Jan-nano-128k: All download methods failed. Please check your internet connection and try again. | {}
2025-07-05 01:58:34.650 | ERROR    | api.endpoints.models:get_current_model:80 | Failed to get current model: No module named 'server.models' | {}
2025-07-13 08:23:48.007 | ERROR    | models.manager:unload_model:924 | ❌ Error during model unload: CUDA error: an illegal memory access was encountered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.
 | {}
2025-07-22 11:45:50.592 | ERROR    | models.manager:_download_with_huggingface_hub:1584 | Download error: Repo id must be in the form 'repo_name' or 'namespace/repo_name': 'Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true'. Use `repo_type` argument if needed. | {}
2025-07-22 11:45:50.677 | ERROR    | models.manager:_download_with_git:1516 | Git command error: Cmd('git') failed due to: exit code(128)
  cmdline: git clone -v --depth=1 --single-branch --progress -- https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true | {}
2025-07-22 11:45:50.687 | ERROR    | models.manager:_download_with_http:1654 | HTTP download failed: [WinError 123] The filename, directory name, or volume label syntax is incorrect: 'H:\\RIL\\Rilance Code Studio\\Rilance Code Studio Augment\\server\\models\\llm\\lucy_128k-Q8_0.gguf?download=true' | {}
2025-07-22 11:45:50.687 | ERROR    | models.manager:download_model:276 | Failed to download model lucy_128k-Q8_0.gguf?download=true: All download methods failed. Please check your internet connection and try again. | {}
2025-07-22 11:55:17.297 | ERROR    | models.manager:_download_with_huggingface_hub:1584 | Download error: Repo id must be in the form 'repo_name' or 'namespace/repo_name': 'Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true'. Use `repo_type` argument if needed. | {}
2025-07-22 11:55:17.383 | ERROR    | models.manager:_download_with_git:1516 | Git command error: Cmd('git') failed due to: exit code(128)
  cmdline: git clone -v --depth=1 --single-branch --progress -- https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true | {}
2025-07-22 11:55:17.388 | ERROR    | models.manager:_download_with_http:1654 | HTTP download failed: [WinError 123] The filename, directory name, or volume label syntax is incorrect: 'H:\\RIL\\Rilance Code Studio\\Rilance Code Studio Augment\\server\\models\\llm\\lucy_128k-Q8_0.gguf?download=true' | {}
2025-07-22 11:55:17.388 | ERROR    | models.manager:download_model:276 | Failed to download model lucy_128k-Q8_0.gguf?download=true: All download methods failed. Please check your internet connection and try again. | {}
2025-07-22 11:56:08.970 | ERROR    | models.manager:_download_with_huggingface_hub:1584 | Download error: Repo id must be in the form 'repo_name' or 'namespace/repo_name': 'Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf'. Use `repo_type` argument if needed. | {}
2025-07-22 11:56:10.146 | ERROR    | models.manager:_download_with_git:1516 | Git command error: Cmd('git') failed due to: exit code(128)
  cmdline: git clone -v --depth=1 --single-branch --progress -- https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf | {}
2025-07-22 12:09:03.023 | ERROR    | models.manager:_download_single_gguf_file:1756 | ❌ Single GGUF file download failed: Cannot connect to host huggingface.co:443 ssl:default [Connect call failed ('*************', 443)] | {}
2025-07-22 20:23:08.645 | ERROR    | models.manager:_download_with_huggingface_hub:1620 | Download error: Repo id must be in the form 'repo_name' or 'namespace/repo_name': 'lucy/128k-Q8/0.gguf?download=true'. Use `repo_type` argument if needed. | {}
2025-07-22 20:23:08.722 | ERROR    | models.manager:_download_with_git:1552 | Git command error: Cmd('git') failed due to: exit code(128)
  cmdline: git clone -v --depth=1 --single-branch --progress -- https://hf-mirror.com/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true | {}
2025-07-22 20:23:08.732 | ERROR    | models.manager:_download_with_http:1690 | HTTP download failed: [WinError 123] The filename, directory name, or volume label syntax is incorrect: 'H:\\RIL\\Rilance Code Studio\\Rilance Code Studio Augment\\server\\models\\llm\\lucy_128k-Q8_0.gguf?download=true' | {}
2025-07-22 20:23:08.732 | ERROR    | models.manager:download_model:312 | Failed to download model lucy_128k-Q8_0.gguf?download=true: All download methods failed. Please check your internet connection and try again. | {}
2025-07-22 20:30:17.420 | ERROR    | models.manager:_download_single_gguf_file:1756 | ❌ Single GGUF file download failed:  | {}
