"""
System API endpoints for server management and information
"""

import os
import sys
import psutil
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from core.config import settings
from core.logging import logger

router = APIRouter()


class SystemInfo(BaseModel):
    """System information model"""
    python_version: str
    platform: str
    cpu_count: int
    memory_total: int
    memory_available: int
    disk_usage: Dict[str, Any]
    gpu_info: Dict[str, Any]


class ServerStatus(BaseModel):
    """Server status model"""
    status: str
    uptime: float
    version: str
    config: Dict[str, Any]


@router.get("/info", response_model=SystemInfo)
async def get_system_info():
    """Get system information"""
    try:
        # Get memory info
        memory = psutil.virtual_memory()
        
        # Get disk usage
        disk = psutil.disk_usage('/')
        disk_info = {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percent": (disk.used / disk.total) * 100
        }
        
        # Get GPU info
        gpu_info = {"available": False, "devices": []}
        try:
            import torch
            if torch.cuda.is_available():
                gpu_info["available"] = True
                gpu_info["device_count"] = torch.cuda.device_count()
                gpu_info["devices"] = []
                
                for i in range(torch.cuda.device_count()):
                    gpu_info["devices"].append({
                        "id": i,
                        "name": torch.cuda.get_device_name(i),
                        "memory_total": torch.cuda.get_device_properties(i).total_memory,
                        "memory_allocated": torch.cuda.memory_allocated(i),
                        "memory_cached": torch.cuda.memory_reserved(i)
                    })
        except ImportError:
            pass
        
        return SystemInfo(
            python_version=sys.version,
            platform=sys.platform,
            cpu_count=psutil.cpu_count(),
            memory_total=memory.total,
            memory_available=memory.available,
            disk_usage=disk_info,
            gpu_info=gpu_info
        )
        
    except Exception as e:
        logger.error(f"Failed to get system info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=ServerStatus)
async def get_server_status():
    """Get server status"""
    try:
        # Calculate uptime (simplified)
        import time
        uptime = time.time() - psutil.boot_time()
        
        # Get configuration (sanitized)
        config_info = {
            "server": {
                "host": settings.server.host,
                "port": settings.server.port,
                "debug": settings.server.debug
            },
            "models": {
                "default_model": settings.models.default_model,
                "max_context_length": settings.models.max_context_length,
                "temperature": settings.models.temperature
            },
            "development_mode": settings.development_mode
        }
        
        return ServerStatus(
            status="running",
            uptime=uptime,
            version="1.0.0",
            config=config_info
        )
        
    except Exception as e:
        logger.error(f"Failed to get server status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": psutil.time.time(),
        "version": "1.0.0"
    }


@router.get("/logs")
async def get_recent_logs(lines: int = 100):
    """Get recent log entries"""
    try:
        log_file = settings.logs_path / "rilance.log"
        
        if not log_file.exists():
            return {"logs": [], "message": "Log file not found"}
        
        # Read last N lines
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        return {
            "logs": [line.strip() for line in recent_lines],
            "total_lines": len(all_lines),
            "returned_lines": len(recent_lines)
        }
        
    except Exception as e:
        logger.error(f"Failed to get logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config")
async def get_configuration():
    """Get server configuration (sanitized)"""
    try:
        config = {
            "server": {
                "host": settings.server.host,
                "port": settings.server.port,
                "debug": settings.server.debug,
                "log_level": settings.server.log_level
            },
            "models": {
                "default_model": settings.models.default_model,
                "model_cache_dir": settings.models.model_cache_dir,
                "max_context_length": settings.models.max_context_length,
                "temperature": settings.models.temperature,
                "top_p": settings.models.top_p,
                "max_tokens": settings.models.max_tokens
            },
            "ai": {
                "chat_max_history": settings.ai.chat_max_history,
                "chat_stream_response": settings.ai.chat_stream_response,
                "agent_max_iterations": settings.ai.agent_max_iterations,
                "agent_timeout": settings.ai.agent_timeout
            },
            "tools": {
                "web_search_engine": settings.tools.web_search_engine,
                "web_search_max_results": settings.tools.web_search_max_results,
                "file_allowed_extensions": settings.tools.file_allowed_extensions,
                "command_allowed": settings.tools.command_allowed
            },
            "development_mode": settings.development_mode
        }
        
        return config
        
    except Exception as e:
        logger.error(f"Failed to get configuration: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reload-config")
async def reload_configuration():
    """Reload configuration from files"""
    try:
        # This would require reloading the settings
        # For now, just return a message
        return {
            "message": "Configuration reload requested",
            "note": "Server restart may be required for some changes to take effect"
        }
        
    except Exception as e:
        logger.error(f"Failed to reload configuration: {e}")
        raise HTTPException(status_code=500, detail=str(e))
