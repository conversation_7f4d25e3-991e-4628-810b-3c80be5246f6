"""
WebSocket endpoints for real-time communication
Provides real-time updates for Agent task execution
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Set, Any, Optional
from enum import Enum

from fastapi import WebSocket, WebSocketDisconnect, APIRouter, Depends
from fastapi.websockets import WebSocketState

from core.logging import logger
from agent.engine import AgentEngine
from models.manager import ModelManager


class MessageType(Enum):
    """WebSocket message types"""
    TASK_CREATED = "task_created"
    TASK_UPDATED = "task_updated"
    STEP_STARTED = "step_started"
    STEP_COMPLETED = "step_completed"
    TOOL_CALLED = "tool_called"
    AGENT_STATUS = "agent_status"
    ERROR = "error"
    HEARTBEAT = "heartbeat"


class ConnectionManager:
    """Manages WebSocket connections"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
    async def connect(self, websocket: WebSocket, client_id: str = None) -> str:
        """Accept a new WebSocket connection"""
        await websocket.accept()
        
        if not client_id:
            client_id = str(uuid.uuid4())
        
        self.active_connections[client_id] = websocket
        self.connection_metadata[client_id] = {
            "connected_at": datetime.now(),
            "last_heartbeat": datetime.now(),
            "user_agent": websocket.headers.get("user-agent", "Unknown")
        }
        
        logger.info(f"WebSocket client connected: {client_id}")
        return client_id
    
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        
        if client_id in self.connection_metadata:
            del self.connection_metadata[client_id]
        
        logger.info(f"WebSocket client disconnected: {client_id}")
    
    async def send_personal_message(self, message: Dict[str, Any], client_id: str):
        """Send a message to a specific client"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_text(json.dumps(message))
                else:
                    self.disconnect(client_id)
            except Exception as e:
                logger.error(f"Failed to send message to {client_id}: {e}")
                self.disconnect(client_id)
    
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast a message to all connected clients"""
        disconnected_clients = []
        
        for client_id, websocket in self.active_connections.items():
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_text(json.dumps(message))
                else:
                    disconnected_clients.append(client_id)
            except Exception as e:
                logger.error(f"Failed to broadcast to {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    def get_connection_count(self) -> int:
        """Get the number of active connections"""
        return len(self.active_connections)
    
    def update_heartbeat(self, client_id: str):
        """Update last heartbeat time for a client"""
        if client_id in self.connection_metadata:
            self.connection_metadata[client_id]["last_heartbeat"] = datetime.now()


# Global connection manager
manager = ConnectionManager()

# WebSocket router
router = APIRouter()


def get_agent_engine() -> AgentEngine:
    """Get agent engine instance"""
    # This would typically be injected or retrieved from app state
    model_manager = ModelManager()
    return AgentEngine(model_manager)


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Main WebSocket endpoint for real-time communication"""
    client_id = None
    
    try:
        # Accept connection
        client_id = await manager.connect(websocket)
        
        # Send welcome message
        welcome_message = {
            "type": "connection_established",
            "data": {
                "client_id": client_id,
                "server_time": datetime.now().isoformat(),
                "message": "Connected to Rilance Agent Server"
            },
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(welcome_message, client_id)
        
        # Listen for messages
        while True:
            try:
                # Receive message
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle message
                await handle_websocket_message(message, client_id)
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError as e:
                error_message = {
                    "type": MessageType.ERROR.value,
                    "data": {"error": f"Invalid JSON: {str(e)}"},
                    "timestamp": datetime.now().isoformat()
                }
                await manager.send_personal_message(error_message, client_id)
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                error_message = {
                    "type": MessageType.ERROR.value,
                    "data": {"error": f"Message handling error: {str(e)}"},
                    "timestamp": datetime.now().isoformat()
                }
                await manager.send_personal_message(error_message, client_id)
    
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
    
    finally:
        if client_id:
            manager.disconnect(client_id)


async def handle_websocket_message(message: Dict[str, Any], client_id: str):
    """Handle incoming WebSocket message"""
    
    message_type = message.get("type")
    data = message.get("data", {})
    
    if message_type == MessageType.HEARTBEAT.value:
        # Handle heartbeat
        manager.update_heartbeat(client_id)
        
        # Send heartbeat response
        response = {
            "type": MessageType.HEARTBEAT.value,
            "data": {
                "server_time": datetime.now().isoformat(),
                "client_id": client_id
            },
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(response, client_id)
    
    elif message_type == "subscribe_to_tasks":
        # Handle task subscription
        user_id = data.get("user_id", "default")
        
        # Send current agent status
        agent_engine = get_agent_engine()
        tasks = agent_engine.list_tasks(user_id)
        
        status_message = {
            "type": MessageType.AGENT_STATUS.value,
            "data": {
                "active_tasks": len([t for t in tasks if t.status.value == "in_progress"]),
                "queued_tasks": len([t for t in tasks if t.status.value == "pending"]),
                "completed_tasks": len([t for t in tasks if t.status.value == "completed"]),
                "failed_tasks": len([t for t in tasks if t.status.value == "failed"]),
                "total_connections": manager.get_connection_count()
            },
            "timestamp": datetime.now().isoformat()
        }
        await manager.send_personal_message(status_message, client_id)
    
    else:
        logger.warning(f"Unknown message type: {message_type}")


# Agent event broadcasting functions
async def broadcast_task_created(task_data: Dict[str, Any]):
    """Broadcast task created event"""
    message = {
        "type": MessageType.TASK_CREATED.value,
        "data": task_data,
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast(message)


async def broadcast_task_updated(task_data: Dict[str, Any]):
    """Broadcast task updated event"""
    message = {
        "type": MessageType.TASK_UPDATED.value,
        "data": task_data,
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast(message)


async def broadcast_step_started(task_id: str, step_data: Dict[str, Any]):
    """Broadcast step started event"""
    message = {
        "type": MessageType.STEP_STARTED.value,
        "data": {
            "task_id": task_id,
            "step_data": step_data
        },
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast(message)


async def broadcast_step_completed(task_id: str, step_data: Dict[str, Any]):
    """Broadcast step completed event"""
    message = {
        "type": MessageType.STEP_COMPLETED.value,
        "data": {
            "task_id": task_id,
            "step_data": step_data
        },
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast(message)


async def broadcast_tool_called(task_id: str, tool_name: str, result: Dict[str, Any]):
    """Broadcast tool called event"""
    message = {
        "type": MessageType.TOOL_CALLED.value,
        "data": {
            "task_id": task_id,
            "tool_name": tool_name,
            "result": result
        },
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast(message)


async def broadcast_agent_status(status_data: Dict[str, Any]):
    """Broadcast agent status update"""
    message = {
        "type": MessageType.AGENT_STATUS.value,
        "data": status_data,
        "timestamp": datetime.now().isoformat()
    }
    await manager.broadcast(message)


# Health check endpoint for WebSocket
@router.get("/ws/health")
async def websocket_health():
    """WebSocket health check"""
    return {
        "status": "healthy",
        "active_connections": manager.get_connection_count(),
        "timestamp": datetime.now().isoformat()
    }


# Connection info endpoint
@router.get("/ws/connections")
async def get_connection_info():
    """Get information about active connections"""
    connections_info = []
    
    for client_id, metadata in manager.connection_metadata.items():
        connections_info.append({
            "client_id": client_id,
            "connected_at": metadata["connected_at"].isoformat(),
            "last_heartbeat": metadata["last_heartbeat"].isoformat(),
            "user_agent": metadata["user_agent"]
        })
    
    return {
        "total_connections": len(connections_info),
        "connections": connections_info,
        "timestamp": datetime.now().isoformat()
    }
