flash_attn-2.7.4.post1.dist-info/AUTHORS,sha256=879BRIJqYoQbf5rrxQV_ddotMqZSpXPtxnJQ7JSjd6c,29
flash_attn-2.7.4.post1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
flash_attn-2.7.4.post1.dist-info/LICENSE,sha256=w2TTjI7oBwFZrWj1aourSRe40CBUjaM6AeA2UFyOSt0,1587
flash_attn-2.7.4.post1.dist-info/METADATA,sha256=BKHKNNTOzJLv3WLAUJczRhYV11oE0JFIdn4lt5niq9A,22961
flash_attn-2.7.4.post1.dist-info/RECORD,,
flash_attn-2.7.4.post1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
flash_attn-2.7.4.post1.dist-info/WHEEL,sha256=UZYoTfvcH9CL8oQkujTAI06MiAXK9kd09pSK3OpCC7k,101
flash_attn-2.7.4.post1.dist-info/direct_url.json,sha256=lJoUu2HxT4A7lk23AWtJIcPHZW0hGhc24hw3WiZLZ7k,371
flash_attn-2.7.4.post1.dist-info/top_level.txt,sha256=CAfUeAM8RDLF5nDjghmaJPNA5bOIUbYHfMXK0rvVjcw,36
flash_attn/__init__.py,sha256=yCA4FLawbXl4mVG4TsLwPv65JLZPGfJ6nHFwmEwb3OU,302
flash_attn/__pycache__/__init__.cpython-310.pyc,,
flash_attn/__pycache__/bert_padding.cpython-310.pyc,,
flash_attn/__pycache__/flash_attn_interface.cpython-310.pyc,,
flash_attn/__pycache__/flash_attn_triton.cpython-310.pyc,,
flash_attn/__pycache__/flash_attn_triton_og.cpython-310.pyc,,
flash_attn/__pycache__/flash_blocksparse_attention.cpython-310.pyc,,
flash_attn/__pycache__/flash_blocksparse_attn_interface.cpython-310.pyc,,
flash_attn/__pycache__/fused_softmax.cpython-310.pyc,,
flash_attn/bert_padding.py,sha256=dhN4U8f_JqPU2Qzo4fHNkYqThe3TGalR1eg4GEqlWe8,10148
flash_attn/flash_attn_interface.py,sha256=AbOGfbsJxECCHGNuxb4jjyixm3UiyaKD3rYtkrifd_I,62392
flash_attn/flash_attn_triton.py,sha256=W2NIPsie2qdcCfRcOcEqks8swWQFYgVOigfoOyFHq_4,42272
flash_attn/flash_attn_triton_amd/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
flash_attn/flash_attn_triton_amd/__pycache__/__init__.cpython-310.pyc,,
flash_attn/flash_attn_triton_amd/__pycache__/bench.cpython-310.pyc,,
flash_attn/flash_attn_triton_amd/__pycache__/bwd_prefill.cpython-310.pyc,,
flash_attn/flash_attn_triton_amd/__pycache__/bwd_ref.cpython-310.pyc,,
flash_attn/flash_attn_triton_amd/__pycache__/fwd_decode.cpython-310.pyc,,
flash_attn/flash_attn_triton_amd/__pycache__/fwd_prefill.cpython-310.pyc,,
flash_attn/flash_attn_triton_amd/__pycache__/fwd_ref.cpython-310.pyc,,
flash_attn/flash_attn_triton_amd/__pycache__/interface_fa.cpython-310.pyc,,
flash_attn/flash_attn_triton_amd/__pycache__/interface_torch.cpython-310.pyc,,
flash_attn/flash_attn_triton_amd/__pycache__/test.cpython-310.pyc,,
flash_attn/flash_attn_triton_amd/__pycache__/utils.cpython-310.pyc,,
flash_attn/flash_attn_triton_amd/bench.py,sha256=UKDUpBapU9hlIVXB7ZyynDCak2EpUaHTWnGlkHmw1qA,10127
flash_attn/flash_attn_triton_amd/bwd_prefill.py,sha256=PR4-d5Nk2Rn_iiwmcB0uqZzA57-D78t-24PDNHqfoug,20960
flash_attn/flash_attn_triton_amd/bwd_ref.py,sha256=nMsbyl8JFOWZx5ZtdUsbTyKWrFLbI32gHYVmWf3kx78,10300
flash_attn/flash_attn_triton_amd/fwd_decode.py,sha256=pd75r0qeyn1rHie2Y0nYs0ndOMwK8Xm5pDhdTpT4Y-M,24138
flash_attn/flash_attn_triton_amd/fwd_prefill.py,sha256=-PoamI4_4Is34pIWw4fryHmJ8C-2GDlXG2tmcj6rOEs,33638
flash_attn/flash_attn_triton_amd/fwd_ref.py,sha256=Ltr7Jj5VsrQzm97VskyxcoKPJzT5FWprASrn6ShTfJo,11680
flash_attn/flash_attn_triton_amd/interface_fa.py,sha256=OXUuUieT0YEaxQgsEPrAZj7Krr2ADlZJEA9ISqGOl3w,16827
flash_attn/flash_attn_triton_amd/interface_torch.py,sha256=f5rC3AhOsCWkuiVe2SZ5ebo7Y2Gpu-OXQSZppB8ih3M,3405
flash_attn/flash_attn_triton_amd/test.py,sha256=yoy0lB4ZH9vssWu_iMR2T7YayZU3m3y_W_cvOdIb2Ng,31556
flash_attn/flash_attn_triton_amd/utils.py,sha256=cbCB0YK1pVZphlrQH35lhOUeglVTeNYVUrn0VhKpLHE,12521
flash_attn/flash_attn_triton_og.py,sha256=LZm4Jlz0ECHtuOWEnpe69_iqUrNfNqGYT6AI5D4X5Qk,11693
flash_attn/flash_blocksparse_attention.py,sha256=BvOsy6cS105Iijgmi0DgXl7-1PjUZoDxcMDztXvlyiA,7669
flash_attn/flash_blocksparse_attn_interface.py,sha256=3z54--DCBdcS7cqF0oiC1Ux53Ye8o-TwbdSgdGJSea0,7465
flash_attn/fused_softmax.py,sha256=-ZMBHj_1CjfOOZwsP9D1w1CZstUyRUVONUhz_rD5cAE,7994
flash_attn/layers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
flash_attn/layers/__pycache__/__init__.cpython-310.pyc,,
flash_attn/layers/__pycache__/patch_embed.cpython-310.pyc,,
flash_attn/layers/__pycache__/rotary.cpython-310.pyc,,
flash_attn/layers/patch_embed.py,sha256=_2b237fpHQa2Q6lggjVHKllo1I7ofNju7ZugWlZieqQ,2203
flash_attn/layers/rotary.py,sha256=_gzEmWqc3mlieLG9NND06igqdNkLwJ5321R_AmmUJKU,21767
flash_attn/losses/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
flash_attn/losses/__pycache__/__init__.cpython-310.pyc,,
flash_attn/losses/__pycache__/cross_entropy.cpython-310.pyc,,
flash_attn/losses/cross_entropy.py,sha256=iOQYdYubepYe7CBenfgvFvdqAi_1kU1WGJmMauldT-I,3282
flash_attn/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
flash_attn/models/__pycache__/__init__.cpython-310.pyc,,
flash_attn/models/__pycache__/baichuan.cpython-310.pyc,,
flash_attn/models/__pycache__/bert.cpython-310.pyc,,
flash_attn/models/__pycache__/bigcode.cpython-310.pyc,,
flash_attn/models/__pycache__/btlm.cpython-310.pyc,,
flash_attn/models/__pycache__/falcon.cpython-310.pyc,,
flash_attn/models/__pycache__/gpt.cpython-310.pyc,,
flash_attn/models/__pycache__/gpt_neox.cpython-310.pyc,,
flash_attn/models/__pycache__/gptj.cpython-310.pyc,,
flash_attn/models/__pycache__/llama.cpython-310.pyc,,
flash_attn/models/__pycache__/opt.cpython-310.pyc,,
flash_attn/models/__pycache__/vit.cpython-310.pyc,,
flash_attn/models/baichuan.py,sha256=qAyur0jJBkdzNlNwFLgHgDpZwgBVngDXcmVFDTV2gVA,5881
flash_attn/models/bert.py,sha256=BCImImz4Zeg6bCH60LLoWlHig5Ha5SDYAOqd3HYHEbo,33996
flash_attn/models/bigcode.py,sha256=D16KsDAurcLs6Spw2WUpzY6L03r-dv9735tTT-Y98js,9616
flash_attn/models/btlm.py,sha256=ojSk0O5mezLfbUJC5b-es3ol__rEdpNUM2Hlu63tEfc,4733
flash_attn/models/falcon.py,sha256=Z8eFr6U7BaAOX0cElGRX9W-nZdgRVRI9NtSf5A3kT6Q,6176
flash_attn/models/gpt.py,sha256=3up3lDM0LrUtEZUpyou6klZrvLOWabTXgq7pgAVpcMM,48749
flash_attn/models/gpt_neox.py,sha256=JnfIppqL6neut6OrcOwZy6QueRsD34T9d5afxoP0isM,5283
flash_attn/models/gptj.py,sha256=mrJcKwuYQk6mGsFsV2-HA3Db79KP0LCbyIb3vnS9jbc,4545
flash_attn/models/llama.py,sha256=Gu-fr9ltOFxKgUTCDHpgwNpTpFeA_d6MGzYT3jyG394,17003
flash_attn/models/opt.py,sha256=w6LSHfxDBPC1d_CIyNUDAU1KifeynDh4WuipbYkUhDA,5280
flash_attn/models/vit.py,sha256=L9AE7hiJo_HVHfMtNEK2JbkTnwQyhDy3PCih4NSJQio,14447
flash_attn/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
flash_attn/modules/__pycache__/__init__.cpython-310.pyc,,
flash_attn/modules/__pycache__/block.cpython-310.pyc,,
flash_attn/modules/__pycache__/embedding.cpython-310.pyc,,
flash_attn/modules/__pycache__/mha.cpython-310.pyc,,
flash_attn/modules/__pycache__/mlp.cpython-310.pyc,,
flash_attn/modules/block.py,sha256=91FIlNAF8rBQlZOHWzlUwGsD-7jzQUl70ukwhmxukoY,17746
flash_attn/modules/embedding.py,sha256=3U2vTsd7aQXfD2wiDLGvEKhfVyw3kC7dtfxbnh1P6BY,8909
flash_attn/modules/mha.py,sha256=zcvt45Xal19vK2qdw2HSYqCRCagK3SGhBMzJL3X4Jnk,44317
flash_attn/modules/mlp.py,sha256=ThtP6EiTA78xc1paV1OkHi4_c-em6VttFECxghi1d9Y,6224
flash_attn/ops/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
flash_attn/ops/__pycache__/__init__.cpython-310.pyc,,
flash_attn/ops/__pycache__/activations.cpython-310.pyc,,
flash_attn/ops/__pycache__/fused_dense.cpython-310.pyc,,
flash_attn/ops/__pycache__/layer_norm.cpython-310.pyc,,
flash_attn/ops/__pycache__/rms_norm.cpython-310.pyc,,
flash_attn/ops/activations.py,sha256=jNBYjUjasYQCnG7oYea4xSfsrVdsevpVlWl_FoI6cFg,4074
flash_attn/ops/fused_dense.py,sha256=IQL8zidCCObh1B_2tLPoPhErYWSTfjIyTnwc7xafa8c,28595
flash_attn/ops/layer_norm.py,sha256=re3-HG7fv-qeZtehElszHG5sQKgH17GmSvX1bJmsPcw,23243
flash_attn/ops/rms_norm.py,sha256=0YbzNABBn31R_7asugdJCFUzXZjvohk1XYkkPKeZ0_U,4162
flash_attn/ops/triton/__init__.py,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
flash_attn/ops/triton/__pycache__/__init__.cpython-310.pyc,,
flash_attn/ops/triton/__pycache__/cross_entropy.cpython-310.pyc,,
flash_attn/ops/triton/__pycache__/k_activations.cpython-310.pyc,,
flash_attn/ops/triton/__pycache__/layer_norm.cpython-310.pyc,,
flash_attn/ops/triton/__pycache__/linear.cpython-310.pyc,,
flash_attn/ops/triton/__pycache__/mlp.cpython-310.pyc,,
flash_attn/ops/triton/__pycache__/rotary.cpython-310.pyc,,
flash_attn/ops/triton/cross_entropy.py,sha256=7zcuiJPAUiRkEgwK6HDhBJdb_9PTNYWYnM_OgexdguI,13174
flash_attn/ops/triton/k_activations.py,sha256=EXip4m6AwLI4f3Go1b3WRLg32RepR70SU_uBZCl_4co,4196
flash_attn/ops/triton/layer_norm.py,sha256=96Y-j80UFVsAl-nZWJqU1t93CRhlF4Ejb13Nw47eLkQ,36827
flash_attn/ops/triton/linear.py,sha256=hyB5-xYqH0cKtPGq26bqzCZJxAPAkN6M8QorwcVFzKs,21435
flash_attn/ops/triton/mlp.py,sha256=9U7E7QT9og5J7kQkSSxFMuBM5FUM-V18n1wOsaNfQL0,6217
flash_attn/ops/triton/rotary.py,sha256=IvcaAJ7YSIybbNY_tCdGUvp4vFPhpKbdKidJ1rjYJRw,8810
flash_attn/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
flash_attn/utils/__pycache__/__init__.cpython-310.pyc,,
flash_attn/utils/__pycache__/benchmark.cpython-310.pyc,,
flash_attn/utils/__pycache__/distributed.cpython-310.pyc,,
flash_attn/utils/__pycache__/generation.cpython-310.pyc,,
flash_attn/utils/__pycache__/pretrained.cpython-310.pyc,,
flash_attn/utils/benchmark.py,sha256=jU-SDghUtkLVtDNnetTAOWFnK1tsi5um1ua3rPoQBtk,7637
flash_attn/utils/distributed.py,sha256=1KKwHrmoAjlGA2OD3O5ntO5LmoT34Xq3X9xkxX8X1Wg,5969
flash_attn/utils/generation.py,sha256=LEg7sbmdeqjazwbi2dfEay9_VpMSF5TRBAzvKYSX1wM,31434
flash_attn/utils/pretrained.py,sha256=z3aA3mwarpBSRVpXZX8y8bxNFqwzGeON0k14TvZDYIU,3325
flash_attn_2_cuda.cp310-win_amd64.pyd,sha256=c4s8uKVW8SIHI9rt7WwSbR3VyLB7ydP_c4Lj5u0PUK8,579445760
hopper/__init__.py,sha256=22zBiH8iySGSYncY8yxg_gGhKSY5OwYVdbKPzyRuiTI,26
hopper/__pycache__/__init__.cpython-310.pyc,,
hopper/__pycache__/benchmark_attn.cpython-310.pyc,,
hopper/__pycache__/benchmark_flash_attention_fp8.cpython-310.pyc,,
hopper/__pycache__/benchmark_split_kv.cpython-310.pyc,,
hopper/__pycache__/flash_attn_interface.cpython-310.pyc,,
hopper/__pycache__/generate_kernels.cpython-310.pyc,,
hopper/__pycache__/padding.cpython-310.pyc,,
hopper/__pycache__/setup.cpython-310.pyc,,
hopper/__pycache__/test_attn_kvcache.cpython-310.pyc,,
hopper/__pycache__/test_flash_attn.cpython-310.pyc,,
hopper/__pycache__/test_kvcache.cpython-310.pyc,,
hopper/__pycache__/test_util.cpython-310.pyc,,
hopper/benchmark_attn.py,sha256=LN0WnQEf7R-dr-8ydH_P7A8WgQxLc-6NU65_Rwfabu8,22146
hopper/benchmark_flash_attention_fp8.py,sha256=3CAzWjvYDtSq75NRadC-h0pA8a4KdVyLDTVfkIadwRg,13624
hopper/benchmark_split_kv.py,sha256=68Y_0Yqp8iKDTrx3O_rRki_3LK5xj-OG2cBud6z9UpU,13507
hopper/flash_attn_interface.py,sha256=PTRwMqI0Jm_uJ16A4CNXhsxwr4pgeGb2xBzqHRqx9WQ,27656
hopper/generate_kernels.py,sha256=TU0DN5shskfJLLTJXDgzKr1Dv88GiWxb5So4wUHOAbo,9099
hopper/padding.py,sha256=1Lh0bbv_YXGXrnm8pLYoE1TdsGNR4GP4hhpS6GTBD70,2734
hopper/setup.py,sha256=nDId3INUOPJfgr3ZHS7v12QdOOLS5FQdD2IZcjiu3N4,29142
hopper/test_attn_kvcache.py,sha256=Z-02HedwNRU_FtovCFM1BdrLKbktRw-gyK_TYupkJj8,20025
hopper/test_flash_attn.py,sha256=e5D0bLiCVWJWAJn3sy58hoMWL9L2KopxXeYPerlb-Ec,46053
hopper/test_kvcache.py,sha256=_TwjSD25vMLdth5htEh3KcZqSyy8LrWKR6I7ylrAtBA,7846
hopper/test_util.py,sha256=MpS7NSH-x6SeRMPO1uDg0SoQSlVLm3X1bhy1zrND5v4,11602
