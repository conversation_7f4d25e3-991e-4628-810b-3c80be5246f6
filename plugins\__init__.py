"""
Reverie Code Studio Plugin System
Comprehensive plugin architecture for extensible AI IDE functionality
"""

from .base import BasePlugin, PluginMetadata, PluginCategory, PluginStatus, PluginConfig
from .manager import PluginManager
from .loader import <PERSON>luginLoader
from .registry import PluginRegistry
from .exceptions import (
    PluginException, PluginLoadError, PluginConfigError,
    PluginNotFoundError, PluginDependencyError, PluginActivationError
)

__all__ = [
    "BasePlugin",
    "PluginMetadata",
    "PluginCategory",
    "PluginStatus",
    "PluginConfig",
    "PluginManager",
    "PluginLoader",
    "PluginRegistry",
    "PluginException",
    "PluginLoadError",
    "PluginConfigError",
    "PluginNotFoundError",
    "PluginDependencyError",
    "PluginActivationError"
]

__version__ = "1.0.0"
