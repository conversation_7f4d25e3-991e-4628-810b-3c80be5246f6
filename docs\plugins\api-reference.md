# Plugin API Reference

This document provides detailed API reference for the Reverie Code Studio Plugin System.

## Base Classes

### BasePlugin

The abstract base class that all plugins must inherit from.

```python
class BasePlugin(ABC):
    def __init__(self, metadata: PluginMetadata, config: PluginConfig)
```

#### Methods

##### `async initialize(self) -> bool`
Initialize the plugin. Called when the plugin is first loaded.

**Returns:** `bool` - True if initialization successful, False otherwise

##### `async activate(self) -> bool`
Activate the plugin. Called when the plugin should become active.

**Returns:** `bool` - True if activation successful, False otherwise

##### `async deactivate(self) -> bool`
Deactivate the plugin. Called when the plugin should stop providing functionality.

**Returns:** `bool` - True if deactivation successful, False otherwise

##### `async cleanup(self) -> bool`
Clean up plugin resources. Called when the plugin is being unloaded.

**Returns:** `bool` - True if cleanup successful, False otherwise

##### `register_hook(self, event: str, callback: Callable)`
Register a callback for a specific event.

**Parameters:**
- `event` (str): Event name to listen for
- `callback` (Callable): Function to call when event occurs

##### `async emit_hook(self, event: str, *args, **kwargs)`
Emit an event to all registered hooks.

**Parameters:**
- `event` (str): Event name to emit
- `*args`: Positional arguments to pass to callbacks
- `**kwargs`: Keyword arguments to pass to callbacks

##### `set_context(self, key: str, value: Any)`
Set a context value for the plugin.

**Parameters:**
- `key` (str): Context key
- `value` (Any): Context value

##### `get_context(self, key: str, default: Any = None) -> Any`
Get a context value.

**Parameters:**
- `key` (str): Context key
- `default` (Any): Default value if key not found

**Returns:** Context value or default

##### `get_config_value(self, key: str, default: Any = None) -> Any`
Get a configuration value.

**Parameters:**
- `key` (str): Configuration key
- `default` (Any): Default value if key not found

**Returns:** Configuration value or default

##### `is_enabled(self) -> bool`
Check if plugin is enabled.

**Returns:** `bool` - True if plugin is enabled

##### `get_plugin_dir(self) -> Optional[Path]`
Get the plugin directory path.

**Returns:** `Optional[Path]` - Plugin directory path

#### Properties

- `metadata: PluginMetadata` - Plugin metadata
- `config: PluginConfig` - Plugin configuration
- `status: PluginStatus` - Current plugin status
- `logger: Logger` - Plugin logger instance

### ToolPlugin

Base class for plugins that provide tools to the AI agent.

```python
class ToolPlugin(BasePlugin):
```

#### Methods

##### `get_tools(self) -> Dict[str, Callable]`
Return a dictionary of tools provided by this plugin.

**Returns:** `Dict[str, Callable]` - Tool name to function mapping

##### `get_tool_descriptions(self) -> Dict[str, str]`
Return descriptions for the tools provided by this plugin.

**Returns:** `Dict[str, str]` - Tool name to description mapping

### AnalysisPlugin

Base class for code analysis plugins.

```python
class AnalysisPlugin(BasePlugin):
```

#### Methods

##### `async analyze_code(self, code: str, language: str, context: Dict[str, Any]) -> Dict[str, Any]`
Analyze code and return insights.

**Parameters:**
- `code` (str): The code to analyze
- `language` (str): Programming language
- `context` (Dict[str, Any]): Additional context information

**Returns:** `Dict[str, Any]` - Analysis results

### IntegrationPlugin

Base class for external service integration plugins.

```python
class IntegrationPlugin(BasePlugin):
```

#### Methods

##### `async connect(self) -> bool`
Connect to the external service.

**Returns:** `bool` - True if connection successful

##### `async disconnect(self) -> bool`
Disconnect from the external service.

**Returns:** `bool` - True if disconnection successful

## Data Models

### PluginMetadata

Plugin metadata and configuration.

```python
@dataclass
class PluginMetadata:
    name: str
    version: str
    description: str
    author: str
    category: PluginCategory
    homepage: Optional[str] = None
    repository: Optional[str] = None
    license: Optional[str] = None
    keywords: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    python_requires: Optional[str] = None
    api_version: str = "1.0.0"
    config_schema: Optional[Dict[str, Any]] = None
    default_config: Dict[str, Any] = field(default_factory=dict)
    plugin_path: Optional[Path] = None
    loaded_at: Optional[datetime] = None
    status: PluginStatus = PluginStatus.UNLOADED
```

### PluginConfig

Plugin configuration model.

```python
class PluginConfig(BaseModel):
    enabled: bool = True
    config: Dict[str, Any] = Field(default_factory=dict)
    priority: int = 100  # Lower numbers = higher priority
    auto_load: bool = True
```

### PluginCategory

Plugin categories enumeration.

```python
class PluginCategory(str, Enum):
    CODE_ANALYSIS = "code_analysis"
    FILE_OPERATIONS = "file_operations"
    AI_INTEGRATIONS = "ai_integrations"
    WEB_TOOLS = "web_tools"
    DEVELOPMENT_TOOLS = "development_tools"
    UI_EXTENSIONS = "ui_extensions"
    DATA_PROCESSING = "data_processing"
    SYSTEM_INTEGRATION = "system_integration"
    CUSTOM = "custom"
```

### PluginStatus

Plugin lifecycle status enumeration.

```python
class PluginStatus(str, Enum):
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    DISABLED = "disabled"
```

## Plugin Manager

### PluginManager

Central plugin management system.

```python
class PluginManager:
    def __init__(self, plugin_dirs: Optional[List[Path]] = None)
```

#### Methods

##### `async initialize(self)`
Initialize the plugin manager.

##### `async discover_plugins(self)`
Discover and register new plugins.

##### `async load_plugin(self, plugin_name: str) -> BasePlugin`
Load a plugin by name.

**Parameters:**
- `plugin_name` (str): Name of the plugin to load

**Returns:** `BasePlugin` - Loaded plugin instance

##### `async unload_plugin(self, plugin_name: str)`
Unload a plugin.

**Parameters:**
- `plugin_name` (str): Name of the plugin to unload

##### `async activate_plugin(self, plugin_name: str)`
Activate a loaded plugin.

**Parameters:**
- `plugin_name` (str): Name of the plugin to activate

##### `async deactivate_plugin(self, plugin_name: str)`
Deactivate an active plugin.

**Parameters:**
- `plugin_name` (str): Name of the plugin to deactivate

##### `async reload_plugin(self, plugin_name: str)`
Reload a plugin (unload and load again).

**Parameters:**
- `plugin_name` (str): Name of the plugin to reload

##### `get_plugin(self, plugin_name: str) -> Optional[BasePlugin]`
Get a loaded plugin by name.

**Parameters:**
- `plugin_name` (str): Name of the plugin

**Returns:** `Optional[BasePlugin]` - Plugin instance or None

##### `get_active_plugin(self, plugin_name: str) -> Optional[BasePlugin]`
Get an active plugin by name.

**Parameters:**
- `plugin_name` (str): Name of the plugin

**Returns:** `Optional[BasePlugin]` - Plugin instance or None

##### `list_loaded_plugins(self) -> List[str]`
Get list of loaded plugin names.

**Returns:** `List[str]` - List of plugin names

##### `list_active_plugins(self) -> List[str]`
Get list of active plugin names.

**Returns:** `List[str]` - List of plugin names

##### `get_plugins_by_category(self, category: PluginCategory) -> List[BasePlugin]`
Get active plugins by category.

**Parameters:**
- `category` (PluginCategory): Plugin category

**Returns:** `List[BasePlugin]` - List of plugins in the category

##### `async emit_hook(self, event: str, *args, **kwargs)`
Emit a hook event to all registered handlers.

**Parameters:**
- `event` (str): Event name
- `*args`: Positional arguments
- `**kwargs`: Keyword arguments

## REST API Endpoints

### Plugin Management

#### `GET /api/v1/plugins/`
List all plugins with optional filtering.

**Query Parameters:**
- `category` (optional): Filter by plugin category
- `status` (optional): Filter by plugin status
- `enabled_only` (optional): Only return enabled plugins

**Response:** List of PluginInfo objects

#### `GET /api/v1/plugins/{plugin_name}`
Get detailed information about a specific plugin.

**Parameters:**
- `plugin_name` (str): Name of the plugin

**Response:** PluginInfo object

#### `POST /api/v1/plugins/discover`
Discover new plugins in the plugin directories.

**Response:** PluginDiscoveryResponse object

#### `POST /api/v1/plugins/{plugin_name}/load`
Load a plugin.

**Parameters:**
- `plugin_name` (str): Name of the plugin to load

#### `POST /api/v1/plugins/{plugin_name}/unload`
Unload a plugin.

**Parameters:**
- `plugin_name` (str): Name of the plugin to unload

#### `POST /api/v1/plugins/{plugin_name}/activate`
Activate a loaded plugin.

**Parameters:**
- `plugin_name` (str): Name of the plugin to activate

#### `POST /api/v1/plugins/{plugin_name}/deactivate`
Deactivate an active plugin.

**Parameters:**
- `plugin_name` (str): Name of the plugin to deactivate

#### `POST /api/v1/plugins/{plugin_name}/reload`
Reload a plugin.

**Parameters:**
- `plugin_name` (str): Name of the plugin to reload

#### `GET /api/v1/plugins/{plugin_name}/config`
Get plugin configuration.

**Parameters:**
- `plugin_name` (str): Name of the plugin

**Response:** PluginConfigRequest object

#### `PUT /api/v1/plugins/{plugin_name}/config`
Update plugin configuration.

**Parameters:**
- `plugin_name` (str): Name of the plugin

**Request Body:** PluginConfigRequest object

#### `GET /api/v1/plugins/categories`
List all available plugin categories.

**Response:** List of category names

#### `GET /api/v1/plugins/status`
Get overall plugin system status.

**Response:** Plugin system status object

## Exceptions

### PluginException
Base exception for plugin system errors.

### PluginLoadError
Raised when a plugin fails to load.

### PluginConfigError
Raised when plugin configuration is invalid.

### PluginDependencyError
Raised when plugin dependencies are not met.

### PluginVersionError
Raised when plugin version is incompatible.

### PluginActivationError
Raised when plugin activation fails.

### PluginDeactivationError
Raised when plugin deactivation fails.

### PluginNotFoundError
Raised when a requested plugin is not found.

### PluginAlreadyLoadedError
Raised when attempting to load an already loaded plugin.

## Events and Hooks

### System Events

- `plugin_loaded` - Emitted when a plugin is loaded
- `plugin_unloaded` - Emitted when a plugin is unloaded
- `plugin_activated` - Emitted when a plugin is activated
- `plugin_deactivated` - Emitted when a plugin is deactivated
- `plugin_error` - Emitted when a plugin encounters an error

### File Events

- `file_opened` - Emitted when a file is opened
- `file_saved` - Emitted when a file is saved
- `file_closed` - Emitted when a file is closed
- `file_created` - Emitted when a file is created
- `file_deleted` - Emitted when a file is deleted

### Editor Events

- `editor_selection_changed` - Emitted when editor selection changes
- `editor_content_changed` - Emitted when editor content changes
- `editor_cursor_moved` - Emitted when cursor position changes

### AI Events

- `ai_request_started` - Emitted when AI request starts
- `ai_request_completed` - Emitted when AI request completes
- `ai_response_received` - Emitted when AI response is received
