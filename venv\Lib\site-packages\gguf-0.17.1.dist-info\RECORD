../../Scripts/gguf-convert-endian.exe,sha256=fRNvHW2L8NJ5_eSAWD-4ZuZ8blERUOc85r1FHBrdnLo,108454
../../Scripts/gguf-dump.exe,sha256=uRVBQw4iNJo4x4Tucjc-WDRanhwx_v1c4OmRZu777MY,108444
../../Scripts/gguf-editor-gui.exe,sha256=mjO_O2Tq2Lu6JbyJqGosGXVDHFHE3hW_XyjSo3Lx7ZE,108450
../../Scripts/gguf-new-metadata.exe,sha256=qA7MA3KHkzxNO3kgaHXP17g9li6ragrL9wf7ksesC8s,108452
../../Scripts/gguf-set-metadata.exe,sha256=aTIXDWwre6zcAGjBdZ8L9n_7UsC8ILIk4TwFiHqjsyM,108452
gguf-0.17.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
gguf-0.17.1.dist-info/LICENSE,sha256=73jH5mWeNMeYGU8NNE6AfHIt5wy8oTWe9UdyZh4Ryjg,1072
gguf-0.17.1.dist-info/METADATA,sha256=pa8_ce5ufoWKHq1zzh97He53EZjFtRFONFDWtl7mkAI,4348
gguf-0.17.1.dist-info/RECORD,,
gguf-0.17.1.dist-info/WHEEL,sha256=b4K_helf-jlQoXBBETfwnf4B04YC67LOev0jo4fX5m8,88
gguf-0.17.1.dist-info/entry_points.txt,sha256=TozYSFmMxpOaKE3brn9nWp-QkgM_sZ3a_uFDOXUCYig,273
gguf/__init__.py,sha256=PM_AEEzX6ojGAodDt78_LIm19HRCXeA6IXpgcjINfC8,219
gguf/__pycache__/__init__.cpython-310.pyc,,
gguf/__pycache__/constants.cpython-310.pyc,,
gguf/__pycache__/gguf.cpython-310.pyc,,
gguf/__pycache__/gguf_reader.cpython-310.pyc,,
gguf/__pycache__/gguf_writer.cpython-310.pyc,,
gguf/__pycache__/lazy.cpython-310.pyc,,
gguf/__pycache__/metadata.cpython-310.pyc,,
gguf/__pycache__/quants.cpython-310.pyc,,
gguf/__pycache__/tensor_mapping.cpython-310.pyc,,
gguf/__pycache__/utility.cpython-310.pyc,,
gguf/__pycache__/vocab.cpython-310.pyc,,
gguf/constants.py,sha256=-92nfNa8XjXbHsMV9Hf0iGHwcmoXzH2JJ73rAv73NPE,89508
gguf/gguf.py,sha256=8MDu7a0JEXhLUv_tjhYqDrWubVNc41cFvBYZbkZZenI,478
gguf/gguf_reader.py,sha256=6uI4vaLeRC2MJV-uUjgsRoUZ-Rdszi8mE_bEPwnk6QE,14828
gguf/gguf_writer.py,sha256=UJXqFa3T2EOLfPZ6ej3vuyV7OBfKXXaszvHKeHIWmSI,43935
gguf/lazy.py,sha256=Axy_plbHoC34nMeK_ORbbxEyEFm-Fz8BxFIeqeipSYA,9214
gguf/metadata.py,sha256=v0kxoYfCk_yXZSnzlSO9AhQ6z_HOUpzs0c6ZV4BRSbA,33301
gguf/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
gguf/quants.py,sha256=BLtCCqhHBtabaYaAp7EckyYOR5idmoKWhu_Hy3glotk,60771
gguf/scripts/__pycache__/gguf_convert_endian.cpython-310.pyc,,
gguf/scripts/__pycache__/gguf_dump.cpython-310.pyc,,
gguf/scripts/__pycache__/gguf_editor_gui.cpython-310.pyc,,
gguf/scripts/__pycache__/gguf_hash.cpython-310.pyc,,
gguf/scripts/__pycache__/gguf_new_metadata.cpython-310.pyc,,
gguf/scripts/__pycache__/gguf_set_metadata.cpython-310.pyc,,
gguf/scripts/gguf_convert_endian.py,sha256=yzl_MAQ3jyn_9MmOWV1CksHqlohd7DmrG7REwas0rlo,7365
gguf/scripts/gguf_dump.py,sha256=zDgZSSQLyO3S4YJsSUzdebDfwmdqQPN7_VtFZ5BkGAk,21785
gguf/scripts/gguf_editor_gui.py,sha256=frdErSIB90N-sAvqUpbLfdDsaUGMMOWQ-0iumwzzm_M,64398
gguf/scripts/gguf_hash.py,sha256=nyd8kzjRKnOFek5UaD19pNXeAVMXUfFEASZ8konkGX8,3725
gguf/scripts/gguf_new_metadata.py,sha256=U_v5FgbH292x7bsi2dG4rbQcWc14nmAtZEWdLnbkIZs,9767
gguf/scripts/gguf_set_metadata.py,sha256=yGEqcQlCimd-pVl23V7u1giJNN3vfvASRqW8em5YWzs,4145
gguf/tensor_mapping.py,sha256=6pUDYgly0-yErLV8HSZFSnqyFKAp7SieditMEHMsYGI,55999
gguf/utility.py,sha256=80rZ3MdGZ6bX0_yFvLoPTTOlxga8THcihF0q38y5h6M,10808
gguf/vocab.py,sha256=Gr43idKqc4yBHQh20HfeW9oegYWLpM-L0ZKCVxPj4Lc,20788
