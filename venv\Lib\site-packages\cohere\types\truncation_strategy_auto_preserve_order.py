# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import typing
import pydantic


class TruncationStrategyAutoPreserveOrder(UncheckedBaseModel):
    """
    If the prompt exceeds the context length, this truncation strategy will continuously omit the oldest tool call and tool result pairs until the prompt fits. If the prompt does not fit with only the last tool call and tool result pair, an error will be returned.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
