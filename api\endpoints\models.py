"""
Model management API endpoints
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel
from models.manager import ModelManager, ModelBackend, ModelInfo
from core.logging import logger
from core.exceptions import ModelException

router = APIRouter()


class ModelLoadRequest(BaseModel):
    """Request model for loading a model"""
    name: str
    backend: ModelBackend = ModelBackend.TRANSFORMERS
    force_download: bool = False
    device: Optional[str] = None  # "auto", "cpu", "gpu"/"cuda", "mps"


class ModelDownloadRequest(BaseModel):
    """Request model for downloading a model"""
    name_or_url: str
    force: bool = False


class ModelResponse(BaseModel):
    """Response model for model information"""
    name: str
    backend: str
    model_path: str
    context_length: int
    loaded: bool
    load_time: Optional[float] = None


def get_model_manager():
    """Dependency to get model manager from app state"""
    # This is a placeholder - in the actual implementation,
    # we'll get this from the FastAPI app state
    # For now, we'll create a global instance
    global _model_manager
    if '_model_manager' not in globals():
        from models.manager import ModelManager
        _model_manager = ModelManager()
    return _model_manager


@router.get("/", response_model=List[str])
async def list_models():
    """List all available cached models"""
    try:
        model_manager = get_model_manager()
        models = model_manager.list_cached_models()
        return models
    except Exception as e:
        logger.error(f"Failed to list models: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/current", response_model=Optional[ModelResponse])
async def get_current_model():
    """Get information about the currently loaded model"""
    try:
        model_manager = get_model_manager()
        model_info = model_manager.get_model_info()
        
        if model_info:
            return ModelResponse(
                name=model_info.name,
                backend=model_info.backend.value,
                model_path=model_info.model_path,
                context_length=model_info.context_length,
                loaded=model_info.loaded,
                load_time=model_info.load_time
            )
        return None
    except Exception as e:
        logger.error(f"Failed to get current model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/load", response_model=ModelResponse)
async def load_model(request: ModelLoadRequest, background_tasks: BackgroundTasks):
    """Load a model"""
    try:
        model_manager = get_model_manager()
        
        logger.info(f"Loading model: {request.name} with backend: {request.backend}")
        
        model_info = await model_manager.load_model(
            request.name,
            request.backend,
            request.force_download,
            request.device
        )
        
        return ModelResponse(
            name=model_info.name,
            backend=model_info.backend.value,
            model_path=model_info.model_path,
            context_length=model_info.context_length,
            loaded=model_info.loaded,
            load_time=model_info.load_time
        )
        
    except ModelException as e:
        logger.error(f"Model loading failed: {e}")
        raise HTTPException(status_code=503, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error loading model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/download")
async def download_model(request: ModelDownloadRequest):
    """Download a model from HuggingFace"""
    try:
        model_manager = get_model_manager()
        
        logger.info(f"Downloading model: {request.name_or_url}")
        
        model_path = await model_manager.download_model(
            request.name_or_url,
            request.force
        )
        
        return {
            "message": "Model downloaded successfully",
            "model_path": model_path
        }
        
    except ModelException as e:
        logger.error(f"Model download failed: {e}")
        raise HTTPException(status_code=503, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error downloading model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/unload")
async def unload_model():
    """Unload the current model"""
    try:
        model_manager = get_model_manager()
        await model_manager.unload_model()
        
        return {"message": "Model unloaded successfully"}
        
    except Exception as e:
        logger.error(f"Failed to unload model: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status")
async def get_model_status():
    """Get model loading status"""
    try:
        model_manager = get_model_manager()
        
        return {
            "loaded": model_manager.is_model_loaded(),
            "device": model_manager.device,
            "cached_models": model_manager.list_cached_models()
        }
        
    except Exception as e:
        logger.error(f"Failed to get model status: {e}")
        raise HTTPException(status_code=500, detail=str(e))
