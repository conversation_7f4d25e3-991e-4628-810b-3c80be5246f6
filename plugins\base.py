"""
Base Plugin Classes and Interfaces for Reverie Code Studio
Defines the core plugin architecture and contracts
"""

import asyncio
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

from pydantic import BaseModel, Field


class PluginCategory(str, Enum):
    """Plugin categories for organization and filtering"""
    CODE_ANALYSIS = "code_analysis"
    FILE_OPERATIONS = "file_operations"
    AI_INTEGRATIONS = "ai_integrations"
    WEB_TOOLS = "web_tools"
    DEVELOPMENT_TOOLS = "development_tools"
    UI_EXTENSIONS = "ui_extensions"
    DATA_PROCESSING = "data_processing"
    SYSTEM_INTEGRATION = "system_integration"
    CUSTOM = "custom"


class PluginStatus(str, Enum):
    """Plugin lifecycle status"""
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class PluginMetadata:
    """Plugin metadata and configuration"""
    name: str
    version: str
    description: str
    author: str
    category: PluginCategory
    
    # Optional metadata
    homepage: Optional[str] = None
    repository: Optional[str] = None
    license: Optional[str] = None
    keywords: List[str] = field(default_factory=list)
    
    # Dependencies and requirements
    dependencies: List[str] = field(default_factory=list)
    python_requires: Optional[str] = None
    api_version: str = "1.0.0"
    
    # Plugin configuration
    config_schema: Optional[Dict[str, Any]] = None
    default_config: Dict[str, Any] = field(default_factory=dict)
    
    # Runtime information
    plugin_path: Optional[Path] = None
    loaded_at: Optional[datetime] = None
    status: PluginStatus = PluginStatus.UNLOADED


class PluginConfig(BaseModel):
    """Plugin configuration model"""
    enabled: bool = True
    config: Dict[str, Any] = Field(default_factory=dict)
    priority: int = 100  # Lower numbers = higher priority
    auto_load: bool = True


class BasePlugin(ABC):
    """
    Base class for all Reverie Code Studio plugins
    
    Plugins must inherit from this class and implement the required methods.
    The plugin system provides a rich set of hooks and utilities for extending
    the IDE's functionality.
    """
    
    def __init__(self, metadata: PluginMetadata, config: PluginConfig):
        self.metadata = metadata
        self.config = config
        self.status = PluginStatus.UNLOADED
        self.logger = None  # Will be set by plugin manager
        self._hooks: Dict[str, List[Callable]] = {}
        self._context: Dict[str, Any] = {}
        
    @abstractmethod
    async def initialize(self) -> bool:
        """
        Initialize the plugin
        
        Called when the plugin is first loaded. Perform any setup operations here.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def activate(self) -> bool:
        """
        Activate the plugin
        
        Called when the plugin should become active and start providing functionality.
        
        Returns:
            bool: True if activation successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def deactivate(self) -> bool:
        """
        Deactivate the plugin
        
        Called when the plugin should stop providing functionality but remain loaded.
        
        Returns:
            bool: True if deactivation successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> bool:
        """
        Clean up plugin resources
        
        Called when the plugin is being unloaded. Clean up any resources here.
        
        Returns:
            bool: True if cleanup successful, False otherwise
        """
        pass
    
    # Hook system for extensibility
    def register_hook(self, event: str, callback: Callable):
        """Register a callback for a specific event"""
        if event not in self._hooks:
            self._hooks[event] = []
        self._hooks[event].append(callback)
    
    async def emit_hook(self, event: str, *args, **kwargs):
        """Emit an event to all registered hooks"""
        if event in self._hooks:
            for callback in self._hooks[event]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(*args, **kwargs)
                    else:
                        callback(*args, **kwargs)
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Hook {event} callback failed: {e}")
    
    # Context management
    def set_context(self, key: str, value: Any):
        """Set a context value"""
        self._context[key] = value
    
    def get_context(self, key: str, default: Any = None) -> Any:
        """Get a context value"""
        return self._context.get(key, default)
    
    # Utility methods
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """Get a configuration value"""
        return self.config.config.get(key, default)
    
    def is_enabled(self) -> bool:
        """Check if plugin is enabled"""
        return self.config.enabled
    
    def get_plugin_dir(self) -> Optional[Path]:
        """Get the plugin directory path"""
        return self.metadata.plugin_path
    
    def __str__(self) -> str:
        return f"{self.metadata.name} v{self.metadata.version}"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}: {self.metadata.name}>"


class ToolPlugin(BasePlugin):
    """
    Base class for tool plugins that provide new tools to the AI agent
    """
    
    @abstractmethod
    def get_tools(self) -> Dict[str, Callable]:
        """
        Return a dictionary of tools provided by this plugin
        
        Returns:
            Dict[str, Callable]: Tool name -> tool function mapping
        """
        pass
    
    @abstractmethod
    def get_tool_descriptions(self) -> Dict[str, str]:
        """
        Return descriptions for the tools provided by this plugin
        
        Returns:
            Dict[str, str]: Tool name -> description mapping
        """
        pass


class UIPlugin(BasePlugin):
    """
    Base class for UI extension plugins
    """
    
    @abstractmethod
    def get_ui_components(self) -> Dict[str, Any]:
        """
        Return UI components provided by this plugin
        
        Returns:
            Dict[str, Any]: Component name -> component mapping
        """
        pass


class AnalysisPlugin(BasePlugin):
    """
    Base class for code analysis plugins
    """
    
    @abstractmethod
    async def analyze_code(self, code: str, language: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze code and return insights
        
        Args:
            code: The code to analyze
            language: Programming language
            context: Additional context information
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        pass


class IntegrationPlugin(BasePlugin):
    """
    Base class for external service integration plugins
    """
    
    @abstractmethod
    async def connect(self) -> bool:
        """
        Connect to the external service
        
        Returns:
            bool: True if connection successful
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """
        Disconnect from the external service
        
        Returns:
            bool: True if disconnection successful
        """
        pass
