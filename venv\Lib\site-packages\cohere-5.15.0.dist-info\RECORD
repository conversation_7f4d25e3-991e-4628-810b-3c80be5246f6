cohere-5.15.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
cohere-5.15.0.dist-info/LICENSE,sha256=RNrMyrG7SWRbPG3vPAySqqG7yi4Eia85iywiloIEcBU,1062
cohere-5.15.0.dist-info/METADATA,sha256=69K6gYqirbtb_OtfVOYWnPB_F4mDo0x3LkaTipHw7Lo,3439
cohere-5.15.0.dist-info/RECORD,,
cohere-5.15.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cohere-5.15.0.dist-info/WHEEL,sha256=Zb28QaM1gQi8f4VCBhsUklF61CTlNYfs9YAZn-TOGFk,88
cohere/__init__.py,sha256=3mNUZmBNU560SgUmO_ZIbVj5TTvUn1bf-g5NFHkfpeA,16305
cohere/__pycache__/__init__.cpython-310.pyc,,
cohere/__pycache__/aws_client.cpython-310.pyc,,
cohere/__pycache__/base_client.cpython-310.pyc,,
cohere/__pycache__/bedrock_client.cpython-310.pyc,,
cohere/__pycache__/client.cpython-310.pyc,,
cohere/__pycache__/client_v2.cpython-310.pyc,,
cohere/__pycache__/config.cpython-310.pyc,,
cohere/__pycache__/environment.cpython-310.pyc,,
cohere/__pycache__/overrides.cpython-310.pyc,,
cohere/__pycache__/sagemaker_client.cpython-310.pyc,,
cohere/__pycache__/utils.cpython-310.pyc,,
cohere/__pycache__/version.cpython-310.pyc,,
cohere/aws_client.py,sha256=DXWCn1dXkFl1MmCjMgEMNYxJB8m-89xBmxwr4DgqtIQ,10127
cohere/base_client.py,sha256=yhthGmHVoCDfRLz86QM1dn31n3W5HcjVD3cKhtxIg7g,284443
cohere/bedrock_client.py,sha256=5j_h_Ur7cWSEvkIaWTxSyL0QPlJzi71D2vROCVaS8C8,1755
cohere/client.py,sha256=ghQPgpNjWM9fMQri2EpPVlBfityjvfHREvpxLUDST3M,23698
cohere/client_v2.py,sha256=i8OPKK367VaJEQELgdRObNRt3kV4nCGiytt87J7FWo4,2344
cohere/config.py,sha256=LXkR_mbbgDlXuVwrc6MqiRl7pOMydtel4EeewNpHC88,22
cohere/connectors/__init__.py,sha256=FTtvy8EDg9nNNg9WCatVgKTRYV8-_v1roeGPAKoa_pw,65
cohere/connectors/__pycache__/__init__.cpython-310.pyc,,
cohere/connectors/__pycache__/client.cpython-310.pyc,,
cohere/connectors/client.py,sha256=ouVpcNGbycJ0Lkkpqs-PMmjhifhcGhyj6xkSp0LkbSQ,91500
cohere/core/__init__.py,sha256=OKbX2aCZXgHCDUsCouqv-OiX32xA6eFFCKIUH9M5Vzk,1591
cohere/core/__pycache__/__init__.cpython-310.pyc,,
cohere/core/__pycache__/api_error.cpython-310.pyc,,
cohere/core/__pycache__/client_wrapper.cpython-310.pyc,,
cohere/core/__pycache__/datetime_utils.cpython-310.pyc,,
cohere/core/__pycache__/file.cpython-310.pyc,,
cohere/core/__pycache__/http_client.cpython-310.pyc,,
cohere/core/__pycache__/jsonable_encoder.cpython-310.pyc,,
cohere/core/__pycache__/pydantic_utilities.cpython-310.pyc,,
cohere/core/__pycache__/query_encoder.cpython-310.pyc,,
cohere/core/__pycache__/remove_none_from_dict.cpython-310.pyc,,
cohere/core/__pycache__/request_options.cpython-310.pyc,,
cohere/core/__pycache__/serialization.cpython-310.pyc,,
cohere/core/__pycache__/unchecked_base_model.cpython-310.pyc,,
cohere/core/api_error.py,sha256=RE8LELok2QCjABadECTvtDp7qejA1VmINCh6TbqPwSE,426
cohere/core/client_wrapper.py,sha256=OupGc7f96vAyzq4_HL1S3ElY3mGaCvBlzvMfqB7omOQ,2547
cohere/core/datetime_utils.py,sha256=nBys2IsYrhPdszxGKCNRPSOCwa-5DWOHG95FB8G9PKo,1047
cohere/core/file.py,sha256=d4NNbX8XvXP32z8KpK2Xovv33nFfruIrpz0QWxlgpZk,2663
cohere/core/http_client.py,sha256=Z77OIxIbL4OAB2IDqjRq_sYa5yNYAWfmdhdCSSvh6Y4,19552
cohere/core/jsonable_encoder.py,sha256=qaF1gtgH-kQZb4kJskETwcCsOPUof-NnYVdszHkb-dM,3656
cohere/core/pydantic_utilities.py,sha256=UibVGGYmBDsV834x8CtckRDrTIL4lYJPMrcq9yvf7RM,11973
cohere/core/query_encoder.py,sha256=ekulqNd0j8TgD7ox-Qbz7liqX8-KP9blvT9DsRCenYM,2144
cohere/core/remove_none_from_dict.py,sha256=EU9SGgYidWq7SexuJbNs4-PZ-5Bl3Vppd864mS6vQZw,342
cohere/core/request_options.py,sha256=h0QUNCFVdCW_7GclVySCAY2w4NhtXVBUCmHgmzaxpcg,1681
cohere/core/serialization.py,sha256=D9h_t-RQON3-CHWs1C4ESY9B-Yd5d-l5lnTLb_X896g,9601
cohere/core/unchecked_base_model.py,sha256=zliEPgLnK9yQ1dZ0mHP6agQ7H0bTZk8AvX6VC1r9oPQ,10754
cohere/datasets/__init__.py,sha256=-A46GSAS-NoaqRvIOXg3pcGP8oG6QDTZNFMV5-IhXMU,419
cohere/datasets/__pycache__/__init__.cpython-310.pyc,,
cohere/datasets/__pycache__/client.cpython-310.pyc,,
cohere/datasets/client.py,sha256=VaeaWI-uXDv6WZ_W1RYEYVyHTAtpJl-I3i-UHnYYeBI,76905
cohere/datasets/types/__init__.py,sha256=gERAikz_r2H_xd_MhltTYT0dW2CiaFAOyqsXniQ80EY,578
cohere/datasets/types/__pycache__/__init__.cpython-310.pyc,,
cohere/datasets/types/__pycache__/datasets_create_response.cpython-310.pyc,,
cohere/datasets/types/__pycache__/datasets_create_response_dataset_parts_item.cpython-310.pyc,,
cohere/datasets/types/__pycache__/datasets_get_response.cpython-310.pyc,,
cohere/datasets/types/__pycache__/datasets_get_usage_response.cpython-310.pyc,,
cohere/datasets/types/__pycache__/datasets_list_response.cpython-310.pyc,,
cohere/datasets/types/datasets_create_response.py,sha256=rXq_Z5xuMO3AzH0DZZb_ei6dJJ26qxOoz9dlaovotCo,611
cohere/datasets/types/datasets_create_response_dataset_parts_item.py,sha256=M-HF8xa4oflDpAkYMcd1BVbot1Brnc5y6nbpOAJaXiw,1005
cohere/datasets/types/datasets_get_response.py,sha256=u8Ghy3kM6lni6xqboNOrs6eg8yJ7vWF4KGLnbUJ66Zg,571
cohere/datasets/types/datasets_get_usage_response.py,sha256=rdJ0u1Z2H928FBhPr9zTi3QIjOyFpEssukvDNiChZIw,666
cohere/datasets/types/datasets_list_response.py,sha256=cc51PnAzJttnvq3nTIUTAMxFcK3fnrP_w76b_DosZ6Q,610
cohere/embed_jobs/__init__.py,sha256=yd0PyZIAv0vl7sEPD7qweqZ_oF5yZjBmvpf0Jo6q03I,159
cohere/embed_jobs/__pycache__/__init__.cpython-310.pyc,,
cohere/embed_jobs/__pycache__/client.cpython-310.pyc,,
cohere/embed_jobs/client.py,sha256=IlpR0KpBCaC-ih6_8mzTfZtfo--M78_8EJz2MIu-zb4,59313
cohere/embed_jobs/types/__init__.py,sha256=wxwVYaHqr1cAwgxP0I3y9bJ0K3Itd7mlQQpCsD5Hoek,187
cohere/embed_jobs/types/__pycache__/__init__.cpython-310.pyc,,
cohere/embed_jobs/types/__pycache__/create_embed_job_request_truncate.cpython-310.pyc,,
cohere/embed_jobs/types/create_embed_job_request_truncate.py,sha256=KP9V8Jyn2_bsO-1w4XQ-CBPz4qqX9NCoKoYzah95ekQ,169
cohere/environment.py,sha256=CMhfszuC6jOD46XjdhYPXmvxrli5d7-1W9rdbB4iN3I,157
cohere/errors/__init__.py,sha256=I0ytkKMVHh7QP43Fv87dEp7eAK4ZPL90A_W63mtzyEo,1052
cohere/errors/__pycache__/__init__.cpython-310.pyc,,
cohere/errors/__pycache__/bad_request_error.cpython-310.pyc,,
cohere/errors/__pycache__/client_closed_request_error.cpython-310.pyc,,
cohere/errors/__pycache__/forbidden_error.cpython-310.pyc,,
cohere/errors/__pycache__/gateway_timeout_error.cpython-310.pyc,,
cohere/errors/__pycache__/internal_server_error.cpython-310.pyc,,
cohere/errors/__pycache__/invalid_token_error.cpython-310.pyc,,
cohere/errors/__pycache__/not_found_error.cpython-310.pyc,,
cohere/errors/__pycache__/not_implemented_error.cpython-310.pyc,,
cohere/errors/__pycache__/service_unavailable_error.cpython-310.pyc,,
cohere/errors/__pycache__/too_many_requests_error.cpython-310.pyc,,
cohere/errors/__pycache__/unauthorized_error.cpython-310.pyc,,
cohere/errors/__pycache__/unprocessable_entity_error.cpython-310.pyc,,
cohere/errors/bad_request_error.py,sha256=_EbO8mWqN9kFZPvIap8qa1lL_EWkRcsZe1HKV9GDWJY,264
cohere/errors/client_closed_request_error.py,sha256=OW7dhZSVjFcWYiqg4Yj-UQqLrRrHbQNmuj9G7zZMKLw,273
cohere/errors/forbidden_error.py,sha256=QO1kKlhClAPES6zsEK7g9pglWnxn3KWaOCAawWOg6Aw,263
cohere/errors/gateway_timeout_error.py,sha256=_IF4DDZKV027xaeKi0gDQzeK77HuoFReNGx2cxp1X5k,268
cohere/errors/internal_server_error.py,sha256=8USCagXyJJ1MOm9snpcXIUt6eNXvrd_aq7Gfcu1vlOI,268
cohere/errors/invalid_token_error.py,sha256=FprPw4jcmQF1bFS36lOWGRQ5TJurcm2O5WjB08RLWtQ,266
cohere/errors/not_found_error.py,sha256=tBVCeBC8n3C811WHRj_n-hs3h8MqwR5gp0vLiobk7W8,262
cohere/errors/not_implemented_error.py,sha256=H-Um7QJl_Um8LROqNo4QX9g5gi3mDTz3Q59qyI5f-6Q,268
cohere/errors/service_unavailable_error.py,sha256=aiWJkLwL3ZF8DUhnHA7DncgPR-gAA9Omj86XByHSVlg,272
cohere/errors/too_many_requests_error.py,sha256=8nFkKVRqHHehkZZuQaxQtul-jK1vxQxgxZ0RpAjgBzk,269
cohere/errors/unauthorized_error.py,sha256=1ewNCqSG1P-uogB5yCNwreq4Bf3VRor0woSOXS4NjPU,266
cohere/errors/unprocessable_entity_error.py,sha256=vd8X3DZR5hSzdWlO3yczFT6aGOvU_3LKp9P-UwSQNr8,273
cohere/finetuning/__init__.py,sha256=ZKGr0kpk3nwyT8IrCOJjgKQYw_A-cdJ7X4WzMSFRGjs,1013
cohere/finetuning/__pycache__/__init__.cpython-310.pyc,,
cohere/finetuning/__pycache__/client.cpython-310.pyc,,
cohere/finetuning/client.py,sha256=0HjgZs41xBw4FWdJ7K2mzBiedPCXJHIgsJ4Ia2vt4ZA,69387
cohere/finetuning/finetuning/__init__.py,sha256=50NrOy8mdVaJV4psW_cccvPedLEvjidubOo8_dbvBK4,965
cohere/finetuning/finetuning/__pycache__/__init__.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__init__.py,sha256=RJl9EmHpSUFxw_B3WFtmKmJXcSAFswYmGozI43agnY8,1438
cohere/finetuning/finetuning/types/__pycache__/__init__.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/base_model.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/base_type.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/create_finetuned_model_response.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/delete_finetuned_model_response.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/event.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/finetuned_model.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/get_finetuned_model_response.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/hyperparameters.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/list_events_response.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/list_finetuned_models_response.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/list_training_step_metrics_response.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/lora_target_modules.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/settings.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/status.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/strategy.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/training_step_metrics.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/update_finetuned_model_response.cpython-310.pyc,,
cohere/finetuning/finetuning/types/__pycache__/wandb_config.cpython-310.pyc,,
cohere/finetuning/finetuning/types/base_model.py,sha256=3gthRMbLnD6QX96eEQMvPL4zg5EbBcXzWHjzLm1tWc4,1086
cohere/finetuning/finetuning/types/base_type.py,sha256=Uxgv2LGIktCXDK4rsqQWAIhl3oG7GkdDFZa2qMNt1pk,305
cohere/finetuning/finetuning/types/create_finetuned_model_response.py,sha256=ldtOknziBjIjr6Ukv_a7LKTLI-DOgZVCmqFOBWCxGyY,783
cohere/finetuning/finetuning/types/delete_finetuned_model_response.py,sha256=GjNpRdC1NAlPxplKt7CmtrstTp6KtMsG8r7HCsUQZCA,157
cohere/finetuning/finetuning/types/event.py,sha256=ERa6Doad0nX0lBGku0EPOzzSuP9AwQwccNfkmun3mPA,1024
cohere/finetuning/finetuning/types/finetuned_model.py,sha256=pj0ZzWeWV976_2PovWqZ5JjCtBJBMUPgvz17uedcs9s,1972
cohere/finetuning/finetuning/types/get_finetuned_model_response.py,sha256=GPy-TgebX4Tc22XtTXYyr7ECbShRqnfiu_H2xas3vzI,779
cohere/finetuning/finetuning/types/hyperparameters.py,sha256=7VrAzvivVMBhAS-5hw9M1klrGG5ziwxjFPk-YokUUfQ,2008
cohere/finetuning/finetuning/types/list_events_response.py,sha256=BMdHj1ny9d3_gLbk_xKk6pQNFDjNvw83YWFecLva6Is,1091
cohere/finetuning/finetuning/types/list_finetuned_models_response.py,sha256=WMYMjw3WQ53phNEM_xidevVDVu18JIQTzPa2D9WJaq4,1134
cohere/finetuning/finetuning/types/list_training_step_metrics_response.py,sha256=zdQFsk8QwPHH2KOhx9UPsvRsgotCmqKcF-R_EDI2Rno,1068
cohere/finetuning/finetuning/types/lora_target_modules.py,sha256=ai6LQtp7DSyvHBlJsdPryDbGaygD3WztMnw2Mkc7LUY,312
cohere/finetuning/finetuning/types/settings.py,sha256=Szt0mkSdfSV5ZHc7pVsbBmitd5C5OymCiE2tM47MsLI,1366
cohere/finetuning/finetuning/types/status.py,sha256=m9UeyT56xh3bE3VdGfzjbY2YybdfgOusjIkX6imZvxQ,402
cohere/finetuning/finetuning/types/strategy.py,sha256=-yCe8aLg5bwXsPd02MjiN4Za-uyPVt_AT6h-8yzqkQM,193
cohere/finetuning/finetuning/types/training_step_metrics.py,sha256=ZY1Q-fLd3FGXvxZFnd-vIsvOTgaKeFyTurtwAjFFPEQ,1014
cohere/finetuning/finetuning/types/update_finetuned_model_response.py,sha256=s-eA8j5qtuxW-DQsn3dfHkb0j-sOHgZuq36A99P6Ce0,785
cohere/finetuning/finetuning/types/wandb_config.py,sha256=TVMu-nepm39HCfqBsL2HfdvRfzyNG6etO9YKjSjY0a4,909
cohere/manually_maintained/__pycache__/cache.cpython-310.pyc,,
cohere/manually_maintained/__pycache__/lazy_aws_deps.cpython-310.pyc,,
cohere/manually_maintained/__pycache__/tokenizers.cpython-310.pyc,,
cohere/manually_maintained/cache.py,sha256=fu33e6anRa62oIlmOZoh_Lexu4fYq0l-KF5P8pD4ssY,811
cohere/manually_maintained/cohere_aws/__init__.py,sha256=YGytjCZUtdsCX7rIumA2LVv-MudhTsKAhqmgVl5jBVo,81
cohere/manually_maintained/cohere_aws/__pycache__/__init__.cpython-310.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/chat.cpython-310.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/classification.cpython-310.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/client.cpython-310.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/embeddings.cpython-310.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/error.cpython-310.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/generation.cpython-310.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/mode.cpython-310.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/rerank.cpython-310.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/response.cpython-310.pyc,,
cohere/manually_maintained/cohere_aws/__pycache__/summary.cpython-310.pyc,,
cohere/manually_maintained/cohere_aws/chat.py,sha256=yo1W4DsaI6BlhD8sMZ2B5FOadTnvse_Vz2lxr4RYWE8,11351
cohere/manually_maintained/cohere_aws/classification.py,sha256=Ag7M9sshichQPXSXcaqASTj_F1BWJOSCs-RZRxb_3OQ,2461
cohere/manually_maintained/cohere_aws/client.py,sha256=9ZoP8iyuE1aWQ8NMIu-D5kJNzruW_2VECsJ6d602Z7w,43799
cohere/manually_maintained/cohere_aws/embeddings.py,sha256=FAA8bSnvGAcoovfc6kAa1LfE7wYKS3xz9k3m0FZPWy0,607
cohere/manually_maintained/cohere_aws/error.py,sha256=rmn_cVKMt0Fn0oCuI7ZBC9DYYZJUWVvm_t3m2By3zIE,591
cohere/manually_maintained/cohere_aws/generation.py,sha256=yp6gW1OtKrNEnHZm9xRSF8BhI4BelnppXiJz_p5t60U,3611
cohere/manually_maintained/cohere_aws/mode.py,sha256=18-SzAY9dnT2FEorjdWzUVpticXVCshPXCPdkj-DC-w,76
cohere/manually_maintained/cohere_aws/rerank.py,sha256=NGAtkeaQvcleVjJgMbVXL84L_p0kEZl90ZmzO5IObZs,2195
cohere/manually_maintained/cohere_aws/response.py,sha256=CQglq-rOxbZOWeCXIveHRXZXF9gj9oE8DdAKnQnZV_c,337
cohere/manually_maintained/cohere_aws/summary.py,sha256=AMPnGZBKl-0jWj-XN3pmtLUR3uHjT3YF1AfOqGYbIkc,459
cohere/manually_maintained/lazy_aws_deps.py,sha256=eixFjCTCppSLGEFIfunHLdPyw-Ty_dfS_XM4_14SVpQ,557
cohere/manually_maintained/tokenizers.py,sha256=IHqfMtjzGS3DeRSci8VLQKGIsZP7-UYjiq5-FGQG77Y,3834
cohere/models/__init__.py,sha256=FTtvy8EDg9nNNg9WCatVgKTRYV8-_v1roeGPAKoa_pw,65
cohere/models/__pycache__/__init__.cpython-310.pyc,,
cohere/models/__pycache__/client.cpython-310.pyc,,
cohere/models/client.py,sha256=pAyOGoeOObITnnXPud-FxM1DE-UAwPtUb_3wIRhWBzE,29702
cohere/overrides.py,sha256=impnWDl94f8avpsFHawrso3uONoJmBRE0MZpwdM5tR0,1615
cohere/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cohere/sagemaker_client.py,sha256=REYOXh7R0ggN5HlokOHK-JMYSkY0EalUYXfai7s3evA,1821
cohere/types/__init__.py,sha256=u3Gle42AO1QsBzpuVuPExChBf6mGTN-9ZjjTEGWzr4Q,19802
cohere/types/__pycache__/__init__.cpython-310.pyc,,
cohere/types/__pycache__/api_meta.cpython-310.pyc,,
cohere/types/__pycache__/api_meta_api_version.cpython-310.pyc,,
cohere/types/__pycache__/api_meta_billed_units.cpython-310.pyc,,
cohere/types/__pycache__/api_meta_tokens.cpython-310.pyc,,
cohere/types/__pycache__/assistant_message.cpython-310.pyc,,
cohere/types/__pycache__/assistant_message_content.cpython-310.pyc,,
cohere/types/__pycache__/assistant_message_content_item.cpython-310.pyc,,
cohere/types/__pycache__/assistant_message_response.cpython-310.pyc,,
cohere/types/__pycache__/assistant_message_response_content_item.cpython-310.pyc,,
cohere/types/__pycache__/auth_token_type.cpython-310.pyc,,
cohere/types/__pycache__/chat_citation.cpython-310.pyc,,
cohere/types/__pycache__/chat_citation_generation_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_citation_type.cpython-310.pyc,,
cohere/types/__pycache__/chat_connector.cpython-310.pyc,,
cohere/types/__pycache__/chat_content_delta_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_content_delta_event_delta.cpython-310.pyc,,
cohere/types/__pycache__/chat_content_delta_event_delta_message.cpython-310.pyc,,
cohere/types/__pycache__/chat_content_delta_event_delta_message_content.cpython-310.pyc,,
cohere/types/__pycache__/chat_content_end_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_content_start_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_content_start_event_delta.cpython-310.pyc,,
cohere/types/__pycache__/chat_content_start_event_delta_message.cpython-310.pyc,,
cohere/types/__pycache__/chat_content_start_event_delta_message_content.cpython-310.pyc,,
cohere/types/__pycache__/chat_data_metrics.cpython-310.pyc,,
cohere/types/__pycache__/chat_debug_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_document.cpython-310.pyc,,
cohere/types/__pycache__/chat_finish_reason.cpython-310.pyc,,
cohere/types/__pycache__/chat_message.cpython-310.pyc,,
cohere/types/__pycache__/chat_message_end_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_message_end_event_delta.cpython-310.pyc,,
cohere/types/__pycache__/chat_message_start_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_message_start_event_delta.cpython-310.pyc,,
cohere/types/__pycache__/chat_message_start_event_delta_message.cpython-310.pyc,,
cohere/types/__pycache__/chat_message_v2.cpython-310.pyc,,
cohere/types/__pycache__/chat_messages.cpython-310.pyc,,
cohere/types/__pycache__/chat_request_citation_quality.cpython-310.pyc,,
cohere/types/__pycache__/chat_request_connectors_search_options.cpython-310.pyc,,
cohere/types/__pycache__/chat_request_prompt_truncation.cpython-310.pyc,,
cohere/types/__pycache__/chat_request_safety_mode.cpython-310.pyc,,
cohere/types/__pycache__/chat_response.cpython-310.pyc,,
cohere/types/__pycache__/chat_search_queries_generation_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_search_query.cpython-310.pyc,,
cohere/types/__pycache__/chat_search_result.cpython-310.pyc,,
cohere/types/__pycache__/chat_search_result_connector.cpython-310.pyc,,
cohere/types/__pycache__/chat_search_results_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_stream_end_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_stream_end_event_finish_reason.cpython-310.pyc,,
cohere/types/__pycache__/chat_stream_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_stream_event_type.cpython-310.pyc,,
cohere/types/__pycache__/chat_stream_request_citation_quality.cpython-310.pyc,,
cohere/types/__pycache__/chat_stream_request_connectors_search_options.cpython-310.pyc,,
cohere/types/__pycache__/chat_stream_request_prompt_truncation.cpython-310.pyc,,
cohere/types/__pycache__/chat_stream_request_safety_mode.cpython-310.pyc,,
cohere/types/__pycache__/chat_stream_start_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_text_generation_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_call_delta_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_call_delta_event_delta.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_call_delta_event_delta_message.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_call_delta_event_delta_message_tool_calls.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_call_delta_event_delta_message_tool_calls_function.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_call_end_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_call_start_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_call_start_event_delta.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_call_start_event_delta_message.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_calls_chunk_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_calls_generation_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_plan_delta_event.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_plan_delta_event_delta.cpython-310.pyc,,
cohere/types/__pycache__/chat_tool_plan_delta_event_delta_message.cpython-310.pyc,,
cohere/types/__pycache__/check_api_key_response.cpython-310.pyc,,
cohere/types/__pycache__/citation.cpython-310.pyc,,
cohere/types/__pycache__/citation_end_event.cpython-310.pyc,,
cohere/types/__pycache__/citation_options.cpython-310.pyc,,
cohere/types/__pycache__/citation_options_mode.cpython-310.pyc,,
cohere/types/__pycache__/citation_start_event.cpython-310.pyc,,
cohere/types/__pycache__/citation_start_event_delta.cpython-310.pyc,,
cohere/types/__pycache__/citation_start_event_delta_message.cpython-310.pyc,,
cohere/types/__pycache__/citation_type.cpython-310.pyc,,
cohere/types/__pycache__/classify_data_metrics.cpython-310.pyc,,
cohere/types/__pycache__/classify_example.cpython-310.pyc,,
cohere/types/__pycache__/classify_request_truncate.cpython-310.pyc,,
cohere/types/__pycache__/classify_response.cpython-310.pyc,,
cohere/types/__pycache__/classify_response_classifications_item.cpython-310.pyc,,
cohere/types/__pycache__/classify_response_classifications_item_classification_type.cpython-310.pyc,,
cohere/types/__pycache__/classify_response_classifications_item_labels_value.cpython-310.pyc,,
cohere/types/__pycache__/compatible_endpoint.cpython-310.pyc,,
cohere/types/__pycache__/connector.cpython-310.pyc,,
cohere/types/__pycache__/connector_auth_status.cpython-310.pyc,,
cohere/types/__pycache__/connector_o_auth.cpython-310.pyc,,
cohere/types/__pycache__/content.cpython-310.pyc,,
cohere/types/__pycache__/create_connector_o_auth.cpython-310.pyc,,
cohere/types/__pycache__/create_connector_response.cpython-310.pyc,,
cohere/types/__pycache__/create_connector_service_auth.cpython-310.pyc,,
cohere/types/__pycache__/create_embed_job_response.cpython-310.pyc,,
cohere/types/__pycache__/dataset.cpython-310.pyc,,
cohere/types/__pycache__/dataset_part.cpython-310.pyc,,
cohere/types/__pycache__/dataset_type.cpython-310.pyc,,
cohere/types/__pycache__/dataset_validation_status.cpython-310.pyc,,
cohere/types/__pycache__/delete_connector_response.cpython-310.pyc,,
cohere/types/__pycache__/detokenize_response.cpython-310.pyc,,
cohere/types/__pycache__/document.cpython-310.pyc,,
cohere/types/__pycache__/document_content.cpython-310.pyc,,
cohere/types/__pycache__/document_source.cpython-310.pyc,,
cohere/types/__pycache__/embed_by_type_response.cpython-310.pyc,,
cohere/types/__pycache__/embed_by_type_response_embeddings.cpython-310.pyc,,
cohere/types/__pycache__/embed_content.cpython-310.pyc,,
cohere/types/__pycache__/embed_floats_response.cpython-310.pyc,,
cohere/types/__pycache__/embed_image.cpython-310.pyc,,
cohere/types/__pycache__/embed_image_url.cpython-310.pyc,,
cohere/types/__pycache__/embed_input.cpython-310.pyc,,
cohere/types/__pycache__/embed_input_type.cpython-310.pyc,,
cohere/types/__pycache__/embed_job.cpython-310.pyc,,
cohere/types/__pycache__/embed_job_status.cpython-310.pyc,,
cohere/types/__pycache__/embed_job_truncate.cpython-310.pyc,,
cohere/types/__pycache__/embed_request_truncate.cpython-310.pyc,,
cohere/types/__pycache__/embed_response.cpython-310.pyc,,
cohere/types/__pycache__/embed_text.cpython-310.pyc,,
cohere/types/__pycache__/embedding_type.cpython-310.pyc,,
cohere/types/__pycache__/finetune_dataset_metrics.cpython-310.pyc,,
cohere/types/__pycache__/finish_reason.cpython-310.pyc,,
cohere/types/__pycache__/generate_request_return_likelihoods.cpython-310.pyc,,
cohere/types/__pycache__/generate_request_truncate.cpython-310.pyc,,
cohere/types/__pycache__/generate_stream_end.cpython-310.pyc,,
cohere/types/__pycache__/generate_stream_end_response.cpython-310.pyc,,
cohere/types/__pycache__/generate_stream_error.cpython-310.pyc,,
cohere/types/__pycache__/generate_stream_event.cpython-310.pyc,,
cohere/types/__pycache__/generate_stream_request_return_likelihoods.cpython-310.pyc,,
cohere/types/__pycache__/generate_stream_request_truncate.cpython-310.pyc,,
cohere/types/__pycache__/generate_stream_text.cpython-310.pyc,,
cohere/types/__pycache__/generate_streamed_response.cpython-310.pyc,,
cohere/types/__pycache__/generation.cpython-310.pyc,,
cohere/types/__pycache__/get_connector_response.cpython-310.pyc,,
cohere/types/__pycache__/get_model_response.cpython-310.pyc,,
cohere/types/__pycache__/image.cpython-310.pyc,,
cohere/types/__pycache__/image_content.cpython-310.pyc,,
cohere/types/__pycache__/image_url.cpython-310.pyc,,
cohere/types/__pycache__/json_response_format.cpython-310.pyc,,
cohere/types/__pycache__/json_response_format_v2.cpython-310.pyc,,
cohere/types/__pycache__/label_metric.cpython-310.pyc,,
cohere/types/__pycache__/list_connectors_response.cpython-310.pyc,,
cohere/types/__pycache__/list_embed_job_response.cpython-310.pyc,,
cohere/types/__pycache__/list_models_response.cpython-310.pyc,,
cohere/types/__pycache__/logprob_item.cpython-310.pyc,,
cohere/types/__pycache__/message.cpython-310.pyc,,
cohere/types/__pycache__/metrics.cpython-310.pyc,,
cohere/types/__pycache__/metrics_embed_data.cpython-310.pyc,,
cohere/types/__pycache__/metrics_embed_data_fields_item.cpython-310.pyc,,
cohere/types/__pycache__/non_streamed_chat_response.cpython-310.pyc,,
cohere/types/__pycache__/o_auth_authorize_response.cpython-310.pyc,,
cohere/types/__pycache__/parse_info.cpython-310.pyc,,
cohere/types/__pycache__/reasoning_effort.cpython-310.pyc,,
cohere/types/__pycache__/rerank_document.cpython-310.pyc,,
cohere/types/__pycache__/rerank_request_documents_item.cpython-310.pyc,,
cohere/types/__pycache__/rerank_response.cpython-310.pyc,,
cohere/types/__pycache__/rerank_response_results_item.cpython-310.pyc,,
cohere/types/__pycache__/rerank_response_results_item_document.cpython-310.pyc,,
cohere/types/__pycache__/reranker_data_metrics.cpython-310.pyc,,
cohere/types/__pycache__/response_format.cpython-310.pyc,,
cohere/types/__pycache__/response_format_v2.cpython-310.pyc,,
cohere/types/__pycache__/single_generation.cpython-310.pyc,,
cohere/types/__pycache__/single_generation_in_stream.cpython-310.pyc,,
cohere/types/__pycache__/single_generation_token_likelihoods_item.cpython-310.pyc,,
cohere/types/__pycache__/source.cpython-310.pyc,,
cohere/types/__pycache__/streamed_chat_response.cpython-310.pyc,,
cohere/types/__pycache__/streamed_chat_response_v2.cpython-310.pyc,,
cohere/types/__pycache__/summarize_request_extractiveness.cpython-310.pyc,,
cohere/types/__pycache__/summarize_request_format.cpython-310.pyc,,
cohere/types/__pycache__/summarize_request_length.cpython-310.pyc,,
cohere/types/__pycache__/summarize_response.cpython-310.pyc,,
cohere/types/__pycache__/system_message.cpython-310.pyc,,
cohere/types/__pycache__/system_message_content.cpython-310.pyc,,
cohere/types/__pycache__/system_message_content_item.cpython-310.pyc,,
cohere/types/__pycache__/text_content.cpython-310.pyc,,
cohere/types/__pycache__/text_response_format.cpython-310.pyc,,
cohere/types/__pycache__/text_response_format_v2.cpython-310.pyc,,
cohere/types/__pycache__/tokenize_response.cpython-310.pyc,,
cohere/types/__pycache__/tool.cpython-310.pyc,,
cohere/types/__pycache__/tool_call.cpython-310.pyc,,
cohere/types/__pycache__/tool_call_delta.cpython-310.pyc,,
cohere/types/__pycache__/tool_call_v2.cpython-310.pyc,,
cohere/types/__pycache__/tool_call_v2function.cpython-310.pyc,,
cohere/types/__pycache__/tool_content.cpython-310.pyc,,
cohere/types/__pycache__/tool_message.cpython-310.pyc,,
cohere/types/__pycache__/tool_message_v2.cpython-310.pyc,,
cohere/types/__pycache__/tool_message_v2content.cpython-310.pyc,,
cohere/types/__pycache__/tool_parameter_definitions_value.cpython-310.pyc,,
cohere/types/__pycache__/tool_result.cpython-310.pyc,,
cohere/types/__pycache__/tool_source.cpython-310.pyc,,
cohere/types/__pycache__/tool_v2.cpython-310.pyc,,
cohere/types/__pycache__/tool_v2function.cpython-310.pyc,,
cohere/types/__pycache__/truncation_strategy.cpython-310.pyc,,
cohere/types/__pycache__/truncation_strategy_auto_preserve_order.cpython-310.pyc,,
cohere/types/__pycache__/truncation_strategy_none.cpython-310.pyc,,
cohere/types/__pycache__/update_connector_response.cpython-310.pyc,,
cohere/types/__pycache__/usage.cpython-310.pyc,,
cohere/types/__pycache__/usage_billed_units.cpython-310.pyc,,
cohere/types/__pycache__/usage_tokens.cpython-310.pyc,,
cohere/types/__pycache__/user_message.cpython-310.pyc,,
cohere/types/__pycache__/user_message_content.cpython-310.pyc,,
cohere/types/api_meta.py,sha256=1KO6UWIGpQrGlTrbwGPOUEdKq6QoDRCDMfcXLz0-cUA,873
cohere/types/api_meta_api_version.py,sha256=q5zrD0bzEZfz8RYGMXM31P_HS3ldKmZKh2V02cU0xtY,624
cohere/types/api_meta_billed_units.py,sha256=CJN-ketMgTe3FMcdWS5MhGrEZEcu0o_zaDWYYOlqOeg,1151
cohere/types/api_meta_tokens.py,sha256=PWm_2nyBrrkhM0Gbi-HTetuS5dVvanfkRHKSFZ7JSYY,784
cohere/types/assistant_message.py,sha256=oTdPnRaPlCN1VcUZ6X6UdLgML23EsCp-o_8rUcoOWgY,1107
cohere/types/assistant_message_content.py,sha256=T_1Cki2AwwDRhrh8yroebb0Qst2Wq8Uix9UAi7Gnv54,238
cohere/types/assistant_message_content_item.py,sha256=L1ALKlRYKm8onR1Z6pIp8oYhbqcXdfyh38ebEGTGBNk,828
cohere/types/assistant_message_response.py,sha256=02ABvPmSadrIOVw6It6JWqpKrj4figjJmx86jxAWggA,1218
cohere/types/assistant_message_response_content_item.py,sha256=2muRB1mCJ1_v4wdAeoGn4GGUALRAlLsRfzLQZUgh8Ys,852
cohere/types/auth_token_type.py,sha256=AQmH1BCKJgW6rMn5d0mlcD6itpVzi1Wtyw0WctK1Etw,168
cohere/types/chat_citation.py,sha256=0HqLaY_u-rXj2zaKQ0cpxj2deHKznvrRd3Bcitf1maw,1759
cohere/types/chat_citation_generation_event.py,sha256=n2vJ-h1J51W51SsOddDbhRJYX3wqANUrJnxAjj9p78Y,659
cohere/types/chat_citation_type.py,sha256=JEX2_1y1OPx3osOJ06dtFsDI9ROSu2n1tACa2DFxq1s,164
cohere/types/chat_connector.py,sha256=JRxBHijM99jDVBgslx4_d44TZ-CrbJFlguvgf0C_P2I,1479
cohere/types/chat_content_delta_event.py,sha256=g8Gm7ebTlSnIgHbx4UbW_L4KY34tTLHaMsY0lmm3hb8,860
cohere/types/chat_content_delta_event_delta.py,sha256=ORalzT66OUe480DAY5mAWTDZkqILt6Ovn7s5rUqUMuE,675
cohere/types/chat_content_delta_event_delta_message.py,sha256=GRqRc-kKQZ_ifmHfDxqLtrrxu0esCs46nI2fCbchAek,704
cohere/types/chat_content_delta_event_delta_message_content.py,sha256=3sug5IQFFih7A9u907N47uMsMDuIFuv1bJeqlzlprv8,570
cohere/types/chat_content_end_event.py,sha256=k0UuDzSAknrzHrJZHbSSDAhJYFP1PRf25KO69MUSoj0,642
cohere/types/chat_content_start_event.py,sha256=1GoBgjAgVDMs7zHD1arcKd5V1ply9PaMqk6KU1lcYQo,781
cohere/types/chat_content_start_event_delta.py,sha256=d0cn4XIBfHo6XBY09Iu9hVfvofz9f7OGu35DcEN3oWI,675
cohere/types/chat_content_start_event_delta_message.py,sha256=ChJ1TeD2KVNLesL0O2fD20Sj-slkOpOWHL-do_F5sgE,704
cohere/types/chat_content_start_event_delta_message_content.py,sha256=p0EJY4eZ8yKLso7L9TNF_cFXYMS9tbDu3pDctPPizCg,627
cohere/types/chat_data_metrics.py,sha256=GdP1bPFhiyVy1ATAOFcTSZ9IXD-yX9CaxBTe2b2l-Zo,901
cohere/types/chat_debug_event.py,sha256=Ecy9qg-12D0JzkqpPFpWE6u_EU4eCZ5lr3vYYWs7g6w,531
cohere/types/chat_document.py,sha256=AZqukupDQO9n3RXbJotqDXyYZwBW6kBpFS_ULkqXDB0,117
cohere/types/chat_finish_reason.py,sha256=dEqMZxmICcUjXrTcLivZifNoXNc6b6m1c_Nf7WMrSto,211
cohere/types/chat_message.py,sha256=NbmsMdH_hg684-fei2j1QnzbrAgPdl7tyPYBLZcyV48,1151
cohere/types/chat_message_end_event.py,sha256=GUEh2ju3Pq-SsiIsa3NeetJ_sALN7Tkp9J3_KZpmyyE,759
cohere/types/chat_message_end_event_delta.py,sha256=vj-eXmBvy8uoyJwqTFHT6NI4zPsYkXUohxVvT61MlJY,691
cohere/types/chat_message_start_event.py,sha256=ItATYOu7Jf33zbvChsL7rPG_wlt0Gk8nmrrwUJDzs5g,849
cohere/types/chat_message_start_event_delta.py,sha256=lzdFfem3NmGLgn4RflOZWjZ0Nzm2xK64fC-kpknhYzo,675
cohere/types/chat_message_start_event_delta_message.py,sha256=ese4Kt4mF24Mn-OkwVBAvXgUKcC38j4xTWxePbjGoS8,656
cohere/types/chat_message_v2.py,sha256=XoNhBaXKab5uPT0q4HH_Fghn7Y-fmOBzn5yF5ZtFNEk,2909
cohere/types/chat_messages.py,sha256=U8m8pK2GWmnJRijXdoh4KmmTRPAXstMi2I7ApplOOog,165
cohere/types/chat_request_citation_quality.py,sha256=tufVRDnY3jcgomhe2IpmXTr0JGMa-hMaJfcq2EOv3TY,177
cohere/types/chat_request_connectors_search_options.py,sha256=umIMU4amGouUekYb8Bl2saJcx6nZKg2pvHTa7hodZu0,1230
cohere/types/chat_request_prompt_truncation.py,sha256=JZscyWGgcQxtHl5Y2sikD6buxx_DiBhGevqe36uC-Wo,189
cohere/types/chat_request_safety_mode.py,sha256=gTJbm52fAA48t8L9ibWgQHacybl5S9xBjpZ8LAhQYno,177
cohere/types/chat_response.py,sha256=18w16kUXvR6PrAasXwCCS7TMNVga38CeOvfuvByINDk,1162
cohere/types/chat_search_queries_generation_event.py,sha256=nzVDmtkkaUqyxaaReTPmNg03Sjlzgozc7_PeeXf0Nwc,712
cohere/types/chat_search_query.py,sha256=c7rW_7SMrtq878pSoO3l7NDE5BkpoAhUAfzQHSbNNe4,854
cohere/types/chat_search_result.py,sha256=Z7E2TPz9eOfrivtyR7r2BvgIIHR_Lr1cb7vnuZHMZcs,1254
cohere/types/chat_search_result_connector.py,sha256=zErcXh6LtEAJYlmIPk0uwXO5vaXH4c92Eg-iAKGqQ1I,665
cohere/types/chat_search_results_event.py,sha256=ilpZrhHzyVQlc-rzYwiwXtysh8aVLP-vCm76WF-0WN8,946
cohere/types/chat_stream_end_event.py,sha256=fg2abVvlhe8szqTyAz1jc1DJA2WtVOLkHmftzSTT1zs,1390
cohere/types/chat_stream_end_event_finish_reason.py,sha256=DU32GwkalGiEIBg6CnMY0dJZOV7W3AovkIXp40eW7pQ,225
cohere/types/chat_stream_event.py,sha256=BSEZiMq-ao3Ua2MPLPNoY6V2jZqT13-49y2AeTEFnr8,506
cohere/types/chat_stream_event_type.py,sha256=HK4fqpox8yKUXrFH2w9IpMrpAYoOspqeVEzpwNgh2e4,556
cohere/types/chat_stream_request_citation_quality.py,sha256=-mqj4msWULs8fO8J7NvEy7PeAlFcxfvwdpA1lFMky6A,183
cohere/types/chat_stream_request_connectors_search_options.py,sha256=-_zzemAjsbb_DpMxaVb4_dCtI8o2VdMAfNC1pWq7ZhQ,1236
cohere/types/chat_stream_request_prompt_truncation.py,sha256=Kwtx_ZWymZyMjJGISXqyd6nryqslQtSjqufxKc_YAys,195
cohere/types/chat_stream_request_safety_mode.py,sha256=UwK-L9YxVpDpjj8lNn3UiEp_6AiFhrR114IrwMlxyE8,183
cohere/types/chat_stream_start_event.py,sha256=pl0lyMaGY1B6BIeTkgCvgLP1RVlAKSbmFXLGUUeya-U,634
cohere/types/chat_text_generation_event.py,sha256=13MhoVO1amYFSGvCNCSuLLf4iMsSL3lIZJP3XdcDLhU,600
cohere/types/chat_tool_call_delta_event.py,sha256=xfzL5pHw-QEDfPQLUU_Ay6jvwl6PPsCcLuV_Xv82rKI,780
cohere/types/chat_tool_call_delta_event_delta.py,sha256=wDLQ5zwXS-CYWLGb7dzG-YWAk8NdBNYXoddLHRErxMg,680
cohere/types/chat_tool_call_delta_event_delta_message.py,sha256=pCOH8s6zEVldWejfhEYckQ6yhZWIv-EHTbo_TMes3w8,719
cohere/types/chat_tool_call_delta_event_delta_message_tool_calls.py,sha256=1ziRDk_jHBgoqnVxbWyL3aiV5ICgCdONtglVcrhLavw,760
cohere/types/chat_tool_call_delta_event_delta_message_tool_calls_function.py,sha256=Oc2lecDhMg5bXCbP6JRcBk9rqIpXaNq4q09PXvpuQ9s,586
cohere/types/chat_tool_call_end_event.py,sha256=LrAS8b-UfO_jIxiHFLJQ4Qm11U4iwW2lRZ89mYNivqw,645
cohere/types/chat_tool_call_start_event.py,sha256=Sk6rOOlQm_dG3yLZXxQqgbkEdBkCzDOP8kASESwTww0,783
cohere/types/chat_tool_call_start_event_delta.py,sha256=RupAuxJkWlajFwRmEOrdbst8-6yvlavYxPCABcVOGqw,680
cohere/types/chat_tool_call_start_event_delta_message.py,sha256=fcRzotsIykH0yU3wyIErRiF00Vj2VLeRd5U3FmNmT7U,614
cohere/types/chat_tool_calls_chunk_event.py,sha256=zqQ48MCXmZXDrqvualGmuNxxHKOZ2VUcb40u2ya68w0,616
cohere/types/chat_tool_calls_generation_event.py,sha256=N3phsi4j-W7cgoEz0zfxcmyBO-i5Si3yHefEcKauaBc,713
cohere/types/chat_tool_plan_delta_event.py,sha256=jXR84egnpcriU0-eyVx3fLY1I3pX8Hkuy0nRy1CWWcQ,729
cohere/types/chat_tool_plan_delta_event_delta.py,sha256=9O3EGBS3YIDNFSKjlM9boW9k5B2Quei9h6-fW9TEhh8,680
cohere/types/chat_tool_plan_delta_event_delta_message.py,sha256=1Ehkamz5-nXZ1JFCrbx1Fxh1PE6oIPG-ZxI6NQMRw0U,569
cohere/types/check_api_key_response.py,sha256=lJlrxCu17Z7ScxgcToaDXFg_8J0Pncqw2UhE6kfnghQ,618
cohere/types/citation.py,sha256=h53jhbaRsalgXo92KayqhWRIcohApRgNJJ_SSYbDgew,1157
cohere/types/citation_end_event.py,sha256=vXC9O7A1df0s3VRHe6WvtSsbafQcxj-fL9lO9SHUSnE,634
cohere/types/citation_options.py,sha256=towMt04C6IeuxVPehrDffpHN16MoN0CluqoRNuErdn4,1067
cohere/types/citation_options_mode.py,sha256=fCe-Wb87TBndIP023VaLZId_ZvyF0ioBGEv1P7tNYec,170
cohere/types/citation_start_event.py,sha256=bYS5VOoGFhWMeEwHOrHmO7k-wIr_5CtqA23cuRUPBDs,753
cohere/types/citation_start_event_delta.py,sha256=dw2D-ZOYaLKv6D3B1Msd-O1aPqJlmxRFbVqgCi-Iye8,662
cohere/types/citation_start_event_delta_message.py,sha256=GDTebzkNdBwOF0fgI8fiBAQogH3VNED-om2yCshRG2M,601
cohere/types/citation_type.py,sha256=y4We70SmeW_-tfQGQzkV_LFkIS6g0gKSNgju8YLNsWQ,160
cohere/types/classify_data_metrics.py,sha256=ZwDJvJy4e-Ypc2YAa3TgPJzhGCjHqWdkb0fqIWPMbRM,617
cohere/types/classify_example.py,sha256=p0NYHL8ZFwQdFwEGnVF_NBtlAZ4snJ1_cGniWFaRG4k,584
cohere/types/classify_request_truncate.py,sha256=TKdXn82b_64yxc26GBP3Ry23QscuKxyXVVw6oil8BT4,171
cohere/types/classify_response.py,sha256=redluz2xErGawCcXazI0RLpFMYf_b8auA7NyPuFiu7s,750
cohere/types/classify_response_classifications_item.py,sha256=q5Hvm7_IqwvalytSVTSxwef-n9-afncIBgOcRja__p0,2152
cohere/types/classify_response_classifications_item_classification_type.py,sha256=uxDQgMTHLjQ5OhvAhuLs75AgWQOUIerqUbHC-RrA2UA,214
cohere/types/classify_response_classifications_item_labels_value.py,sha256=W9EEx1s9F5coOFePGIzZJFwBEXvb-kMsxG_z-dYZ-6A,584
cohere/types/compatible_endpoint.py,sha256=OQyyMpzT8s02WuQvLprJUHxRsHVp_AiJKZLJsghgTbA,220
cohere/types/connector.py,sha256=5W7-yRrVpShb1yRESpMdYgZMh0QUe9USrd6qKq7GZJo,2979
cohere/types/connector_auth_status.py,sha256=tdWndU6GSLTBfsXEfBAJ4d2Yytw3Hn_OtvrK7uo_w4g,163
cohere/types/connector_o_auth.py,sha256=VSxxY1aG8BhXQ3QiILqY3Tb5ufuCKfBd6NTWShOst3Q,1258
cohere/types/content.py,sha256=B3kAewfwyZF_QetYJm53Q0hAk2j6JNfCaqJnyLbu27g,1423
cohere/types/create_connector_o_auth.py,sha256=UXwfQW2W07JHiErcGc5RiUNKJS-_nY-rctYZ0_mw6e4,1323
cohere/types/create_connector_response.py,sha256=jpBSr5bs7w7AyawulwacznY_SZL6LWDCPmMBnsHrHCk,573
cohere/types/create_connector_service_auth.py,sha256=_36DJycjm-Sv50dxsoq_0uYD8GID3EkIrcheBp-y6qo,805
cohere/types/create_embed_job_response.py,sha256=D0abnaQEVR8ey--7zlHGX-cZuYCYSSUTs7ruYlnbXqo,660
cohere/types/dataset.py,sha256=Arz-youQj-orhntYnABkuOJ2sN1BeIUDiC0vk9dsMJA,1872
cohere/types/dataset_part.py,sha256=LYmtItPaEwP1wy9uaIzLzN-kDweDIsDPsE8eJRipwFE,1377
cohere/types/dataset_type.py,sha256=fRR-olDI-avZFb_iHehRod25ipAT_7v_rjSzHil8v5U,427
cohere/types/dataset_validation_status.py,sha256=FVn6BzIrZ_vtnc7A9-S5SBCrgWgJdfPSSEF5EMCf0RU,222
cohere/types/delete_connector_response.py,sha256=R-z_OGKebOC_Q3MKxLx5FcxdgsXPsMtrmRRQwJaVCX4,152
cohere/types/detokenize_response.py,sha256=X6U6H8Kfzf_BCBnfbIJkAnb0uvScEA87RUasgGlU-B4,678
cohere/types/document.py,sha256=8x8m-OnDAUlwPlR6_aB7RPUHAthpS7nscMb7xvlK8cU,1242
cohere/types/document_content.py,sha256=vPP6GcJV4RV6eed8mLx2nduqL5MyOuTBaYheFBOMl5k,600
cohere/types/document_source.py,sha256=SGeR6LEEsTnIxy0nMqgBXG3cou5FNGIvGVr262MqJNM,829
cohere/types/embed_by_type_response.py,sha256=jgcJIdRswkUJ4yfxv24F-_NqL10lND1NcpRgrrPn_I4,1202
cohere/types/embed_by_type_response_embeddings.py,sha256=bw-YUBDKbhdJdsyLrRbJxa4E5Q4dS6XhbnGXJGNLwXQ,1921
cohere/types/embed_content.py,sha256=jiy57IZNImzWm3oARBoyeVkEMFIcgowlsztCCEEWJus,1296
cohere/types/embed_floats_response.py,sha256=jIhGOmO97HUM7SloQsapCOACJmxVxUcB_ja_t5_0PYk,1150
cohere/types/embed_image.py,sha256=a1ttUhNF48oRF1NeIsxXQY0GtbBAhaOFG6bpEJ5KuDs,647
cohere/types/embed_image_url.py,sha256=CSWfBJmTTEXD1nIGYOG0ZiUkjo51xWJ_NF8CkTnhIxU,560
cohere/types/embed_input.py,sha256=0cW9NzyvODL21n3oakx8fMum5-jgQdKWox59fwgKW50,690
cohere/types/embed_input_type.py,sha256=Qc7TqKbE-qejyk_RlGG8EyZ5aM2K3hFjcbBnAzkRwwA,220
cohere/types/embed_job.py,sha256=b_g30gBcJSahcQL3mMt6WhSZjocDUBcOLwUaXR3bVQ0,1475
cohere/types/embed_job_status.py,sha256=W6dPm5AFkNf-ZffDDHpINzQFTJg8LV7FXVT6zf4Lyvo,201
cohere/types/embed_job_truncate.py,sha256=8sv4eggpQ7mPpFHheTGaFXIriRN-8ATz5rSYHLYEwSk,156
cohere/types/embed_request_truncate.py,sha256=tBf20S3DnyKvxotAe9WhpAi-dHGUkcrJbq4DkoZGIBw,168
cohere/types/embed_response.py,sha256=O5_woTws9XVkC6DSuEBDHOTPrCoKjri-1kPwzsGZ_nA,1783
cohere/types/embed_text.py,sha256=CsQHqYbqFUxQpn3kYSWxoP57MPV57HiKmB5DIYcAuGs,587
cohere/types/embedding_type.py,sha256=x7kJ_13_tMtFjid1hEJn7JkQJz9Nma8cFaqeP3w2_b0,184
cohere/types/finetune_dataset_metrics.py,sha256=QUfLOlA-vsSSRH5_PxM_xREhq7Y0_rvihp0ay_VQMXM,1330
cohere/types/finish_reason.py,sha256=s5eHrYoIReueONqZ2GjJ1kkuuaB5ylCk1l_2HAZiu3A,244
cohere/types/generate_request_return_likelihoods.py,sha256=ByEfuP4reLXZQm0cA77tFoLDxgnLf4aK-bRFfuS6ydQ,185
cohere/types/generate_request_truncate.py,sha256=fuXd-KHG3HRfORgUwt-DA4VLcoCzIFM1Bv7jWWwzCE8,171
cohere/types/generate_stream_end.py,sha256=FyE13AuhQ3eYCXMBsTzRmZC6v8nKxDkruPeNbMD-WkA,732
cohere/types/generate_stream_end_response.py,sha256=vONyMN_P0sTQ9wkMwJQStyhlsn0KK0lechyrQPui7-c,714
cohere/types/generate_stream_error.py,sha256=Y4VM3sCYQ254MjDJVLjYakwoti3NkO7rJ9whcIRGv6Q,840
cohere/types/generate_stream_event.py,sha256=aSRS9REXWP6rV5g_6bCIHEqH0W_bGtWegFSWmcEdR84,510
cohere/types/generate_stream_request_return_likelihoods.py,sha256=9DBmSUnXJNjkJXPDrF2hGy2N7q7dThRfCMenLcUfx-4,191
cohere/types/generate_stream_request_truncate.py,sha256=5cOP9rNzdtIhuAeNDYQt1-g6Ey2PJHKame90_11ohuU,177
cohere/types/generate_stream_text.py,sha256=r51K-ciEPMZoMVs9ycp4-NCP02AfvCOeD38T8OdvJlA,841
cohere/types/generate_streamed_response.py,sha256=a2ru-AraMJo5HDqHgYsVOhhiiAmiL86eQKWz3p6cJbI,2811
cohere/types/generation.py,sha256=PNO2dBe74plcIbAWsl4ajAyvWAaU_jLJEihGI_k_DKg,861
cohere/types/get_connector_response.py,sha256=odayxRA3_wqwFz3qxFQ-iK6M-nxqBRfWyssg8Iez9Ug,570
cohere/types/get_model_response.py,sha256=cAeFpIVYwXaaPJ-MaboXAaCiTbMGC5_nWzpowaw4G1E,2026
cohere/types/image.py,sha256=Bg6EWDV8CznEa-SbcgFyk12yGcBuD0l9GiBoAjISx2w,824
cohere/types/image_content.py,sha256=QGzMu5K0W4m12F1K2nEDy4uiyM2lR60VsHX0WPuV6Hg,611
cohere/types/image_url.py,sha256=-5RB1ryquW7ZQKaF35wIYIT-O222Vd6IhAHRiVdCRBY,555
cohere/types/json_response_format.py,sha256=ndPPn4W8c7YYmNsAoXWXuDJFGnQbgLIAqjdRimosWLM,1327
cohere/types/json_response_format_v2.py,sha256=-laTy_jPRq0_xJ_jzt_gJ0LZrVc4m_UrJ0K3gWh4c_M,1242
cohere/types/label_metric.py,sha256=cvQdLsK2oFiM-BHpIIHVkvk7Y_ONJCLgmnbQTyS6Ly8,860
cohere/types/list_connectors_response.py,sha256=FhsVKEmyqgZLjQC2vE4v2j3gLahkgY6IiDDl3DhJfH8,705
cohere/types/list_embed_job_response.py,sha256=5DCwHXaUNBOwFjdxgEcE_1bJTsLeIVITvQ2CBOgOwvI,606
cohere/types/list_models_response.py,sha256=QBgZhlMzrFOFWo6pB1f8VMEtXgrkSNBoNW_vkB36N38,797
cohere/types/logprob_item.py,sha256=No6TeZkUNB7meluV8mrc-oSlBtwUFSm_kLI-ZW8ulLM,952
cohere/types/message.py,sha256=N2qAOCj8vLVudmFKOxjjTO6ljqJw9qx_KTpO_OwnltE,2212
cohere/types/metrics.py,sha256=Ozr-fUr2VKTTtv25pukP-ep1eKthzrREexgpViPeZIQ,743
cohere/types/metrics_embed_data.py,sha256=vIG_bGhF9JetXziNVYdLKAnUQF4S5Cefm5wXoXTerpg,725
cohere/types/metrics_embed_data_fields_item.py,sha256=RS0KUP-2k2B5NJ4H2WMqTTM2GOYAn1Pvh850-nejlZM,761
cohere/types/non_streamed_chat_response.py,sha256=YjCTNCrLhXF3LFE8XHUGBwNDfh1wwIMIV8P2c5ttfbU,2652
cohere/types/o_auth_authorize_response.py,sha256=HIQkbpV2bqI3XeM5h2k3ECqEzrJ1-W-GclZeYGK2RF8,690
cohere/types/parse_info.py,sha256=pXiCYYBwmUPj_PmkBFg_RckFz-vZ4JRnQQ6TCmKIKJc,587
cohere/types/reasoning_effort.py,sha256=_TBLn3rQgzJAdnKqV2g0PETbrSBZl0fPLfQ5ZE9H4Pc,164
cohere/types/rerank_document.py,sha256=vlpCrRWBC2JOnR2QwoZttKlsYL8wtBgOEIwpDA9la6s,119
cohere/types/rerank_request_documents_item.py,sha256=f7rwtXa9f3Sa68sBnzQ6KUUTaMPi7JHvTdErRD5uh54,187
cohere/types/rerank_response.py,sha256=k0AZLmq-Y8x0fspNtu3bSbvFgmoLWGr4uxS9Hu_6-zQ,810
cohere/types/rerank_response_results_item.py,sha256=guhId1OWcSDavoqfvvJZFTWo7u1NQt5p0NMcONijRLA,1513
cohere/types/rerank_response_results_item_document.py,sha256=2dYKgQ5oOScjbZ3ARV1ZDpkaTUZw06bkkP2U5KvqeHM,746
cohere/types/reranker_data_metrics.py,sha256=GULv1gr8VKW7nz85qr0Co0diHVztZAerngZZuz8Y2xM,1413
cohere/types/response_format.py,sha256=t7NPSQmuPbhxT6xmaUckHDC3ocHmIYg9xkgw9xPEZrk,3250
cohere/types/response_format_v2.py,sha256=4ymEYM9Mm1JjDkf2sIDj-ubTlhV0_IkbFabhXsYMd8w,3235
cohere/types/single_generation.py,sha256=dkyoTSmu2iSklQTQoHiCS-PX9vato5F5PJptN2PvuvM,1435
cohere/types/single_generation_in_stream.py,sha256=GlK40QwOpbobYRWDKth37Ret1flFNL7AofJXXa4N2MQ,855
cohere/types/single_generation_token_likelihoods_item.py,sha256=bruCJ-EFBzedBYu8aK4SX1Yo80DswVUTC-l2NdJW4oY,565
cohere/types/source.py,sha256=Mwx3ra9tjAch-B7dRPXOhZh7Frvgn72ACEruK4zc8ok,1559
cohere/types/streamed_chat_response.py,sha256=KjaiKBitzjE63ujom4DSjsi2etutFDx84KI7uSz0oc0,6358
cohere/types/streamed_chat_response_v2.py,sha256=jR12IAcqY4k3eyme0c0XX3nD-kiXYXANbTFt4Z6HswM,8490
cohere/types/summarize_request_extractiveness.py,sha256=qQpS2_ziqQPBYJZiJMBkrovDrH9lBNBCKZpmm_iM6vg,179
cohere/types/summarize_request_format.py,sha256=aGXBz0dsQXWQmYtlz2EJBhoTlpLbCF-npB-SHbVuXPM,170
cohere/types/summarize_request_length.py,sha256=hIgrlh-QFmYLPUgYsvsSFw278CKevYhwX9wo1SOc760,173
cohere/types/summarize_response.py,sha256=LiKgm1ewhw1VO3s_3jIowxcv5HugpPzOP5yGTnJp-tg,808
cohere/types/system_message.py,sha256=b4UOcPIqnjtRbSDap4X6guTL156UwPAXzHtK5hIfrPQ,644
cohere/types/system_message_content.py,sha256=duQa0y5EXh5nkOkwQc7wV9UuGQI0LtBgQ3jDjJkAoC0,226
cohere/types/system_message_content_item.py,sha256=JrMy6X3GMl09NkXOmRvkFP6Dq6WqmMShADk8j81SGIQ,813
cohere/types/text_content.py,sha256=gnV1P2AVNd8y-ycs3xz1MMFdRgKkciD_4vc2qwlXKEc,567
cohere/types/text_response_format.py,sha256=_zPZJvAN67PSsMRaRmFKkuJl8no5JUeO7kbNThhwe6Q,509
cohere/types/text_response_format_v2.py,sha256=KCdiQK5odO4YZymZKAuRfzVbZoYc19l-k7UYXUZiC80,511
cohere/types/tokenize_response.py,sha256=B7AFcWHZEdlFL18XU2cRviJJt3rGM2sjmvw0O8_lZPA,737
cohere/types/tool.py,sha256=YFpJG3vqzzhEtodJKBrI2si6SDWQVCA8vEN7kipBh6w,1527
cohere/types/tool_call.py,sha256=pbEQHwIIUun757ZAeCu3NbvvcEi6Czq57TNGL9Vn4ss,845
cohere/types/tool_call_delta.py,sha256=cQIOBsdcLt1N79E8ledpb95F8Zir4A_EvoK1FDNiZbg,1040
cohere/types/tool_call_v2.py,sha256=6ogWsiMy8kLKnaSKyMVCkyKckZRy5PiP9fJ593rMi4c,765
cohere/types/tool_call_v2function.py,sha256=oWopRa0ojGYPFAM1Kc8oLEs--7KrcId7QtN3-fmRqvQ,591
cohere/types/tool_content.py,sha256=mPIO6uCQhk1g8wJgs8MF4Tj4wrmW-z3wnG-iNAoW12E,1421
cohere/types/tool_message.py,sha256=7tltwxnTUqMr_UsnckeE-hKEvF2Q37XuittPy6r0Iwk,670
cohere/types/tool_message_v2.py,sha256=SJ7-ISGwPrawOwXrroZQ9ghBZFYkwxo-eo9pV0Ww02g,926
cohere/types/tool_message_v2content.py,sha256=fyaXV_TQ_HsAi6LyvUk5EodPQXId4xfgr5tHfBGRrws,185
cohere/types/tool_parameter_definitions_value.py,sha256=zCerFI4M4sgzzdtD23QLvb0fWlfkGpjfVUUhH7yFs8k,935
cohere/types/tool_result.py,sha256=jM79Cxb5qzXSWba62ee-GVCIqPy-cffIn-HEmd_owys,625
cohere/types/tool_source.py,sha256=JdxOB_hRh-vWDt5ebnBj_nc6Ne0eo4mnqTEcMif_mU4,708
cohere/types/tool_v2.py,sha256=p_R5dzW0PHbeHHSGTHNrxgYUDo55x5qzNPNVAJCp29M,729
cohere/types/tool_v2function.py,sha256=Z2wSmFBSmmpdKo0rpTrlqvVQiDe9kQ6Eu9_kQvxdNV4,909
cohere/types/truncation_strategy.py,sha256=CIYijeCwK02Cvi-yoh_nIGnSQQPT3g1sJPbGgsIcbn4,1418
cohere/types/truncation_strategy_auto_preserve_order.py,sha256=336_g3SLSLsC8XCahAiGJg2BC8fv02hCBqxEtBDuzVM,808
cohere/types/truncation_strategy_none.py,sha256=05ZFvsAXsSMPJgURj9KfZ8AALtfjxg2vbs6JT34gIb4,629
cohere/types/update_connector_response.py,sha256=SeFCGM4yd2Ob1wFTacp_4kaKpIK0HV9-Y7aDlzPzL20,573
cohere/types/usage.py,sha256=C-IF84-CEWTEkTdBaS6GvxXYf9AhY_8vSkUZ47vqWqg,691
cohere/types/usage_billed_units.py,sha256=LPGP1T0ngSOYftV4sbIIKJz_EPg8ldE8u9XqBRTwwTw,1033
cohere/types/usage_tokens.py,sha256=qmM42gNtoqUd67RvpcW5HYj1fw5Nraq4wMUqE2nseOg,782
cohere/types/user_message.py,sha256=MRPPERt4zFTDC_QNX0CJI-HxPnYWRtBgWJh8vFzLJVs,824
cohere/types/user_message_content.py,sha256=P3l2_eqxKF3Bvn_04SeVTehQB-hYVUBibVgdRWdniSk,170
cohere/utils.py,sha256=zhOLxscBlaKlM2i2o6mk5kRiUnP6DHzapm7dqramRgc,10148
cohere/v2/__init__.py,sha256=1qaoH4qOezM-tBix6BAMYitxqCnIbR6PxFxquNNeHjg,767
cohere/v2/__pycache__/__init__.cpython-310.pyc,,
cohere/v2/__pycache__/client.cpython-310.pyc,,
cohere/v2/client.py,sha256=UU-vn9_VfM1f5LyfpvRMs7y0r2MivQbU0ZuNfWql2P4,107305
cohere/v2/types/__init__.py,sha256=hJvEXvqVV3wkX_ksTnva-HbXXzb0ODeFCzh7ugyhxFA,1127
cohere/v2/types/__pycache__/__init__.cpython-310.pyc,,
cohere/v2/types/__pycache__/v2chat_request_documents_item.cpython-310.pyc,,
cohere/v2/types/__pycache__/v2chat_request_safety_mode.cpython-310.pyc,,
cohere/v2/types/__pycache__/v2chat_request_tool_choice.cpython-310.pyc,,
cohere/v2/types/__pycache__/v2chat_stream_request_documents_item.cpython-310.pyc,,
cohere/v2/types/__pycache__/v2chat_stream_request_safety_mode.cpython-310.pyc,,
cohere/v2/types/__pycache__/v2chat_stream_request_tool_choice.cpython-310.pyc,,
cohere/v2/types/__pycache__/v2embed_request_truncate.cpython-310.pyc,,
cohere/v2/types/__pycache__/v2rerank_response.cpython-310.pyc,,
cohere/v2/types/__pycache__/v2rerank_response_results_item.cpython-310.pyc,,
cohere/v2/types/__pycache__/v2rerank_response_results_item_document.cpython-310.pyc,,
cohere/v2/types/v2chat_request_documents_item.py,sha256=ZwPXnDlN-2iqmq4LP6mH6dv_hNZ5xhJvSvaCvzNndVU,176
cohere/v2/types/v2chat_request_safety_mode.py,sha256=dY9Wt4v0Jgzlxz7ptUVkvRcbmpsIi3uY54LW1hg2BH0,178
cohere/v2/types/v2chat_request_tool_choice.py,sha256=saYBSxo0_w_fuXy4GtXdzaJasq0EG6SQp-qP7ZPVCSw,167
cohere/v2/types/v2chat_stream_request_documents_item.py,sha256=HKO6CNPj55lwPt0sj4MUlqBbB0M-OPe_gxsPncG36jI,182
cohere/v2/types/v2chat_stream_request_safety_mode.py,sha256=irPZ_EEt5Ci-vnHdx8-ddQOo2z96eGL7T4ucS1Fhlk8,184
cohere/v2/types/v2chat_stream_request_tool_choice.py,sha256=RimUM2p46FrDC5qHfG2QE6Il_cc-JDRxIUz_fEVGDbM,173
cohere/v2/types/v2embed_request_truncate.py,sha256=lA2nzWdWojH73eYdEDoiu1CAqiK3uba0LHhaYd02hVw,170
cohere/v2/types/v2rerank_response.py,sha256=0YwHM8D_cJn6ZW7BqkG6hfFVlhsJ91zUbKa5XCN1Kag,828
cohere/v2/types/v2rerank_response_results_item.py,sha256=endlT9KePeESyt2pWoSQAdU0aDlNvHxrYKp_9TZ1eK4,1523
cohere/v2/types/v2rerank_response_results_item_document.py,sha256=vh894zhEPSiPvgtB2MjHYtwwbJm4PuCFyo3qKvCIs0k,750
cohere/version.py,sha256=09AHRA5BDnc2MiTWLtm5ehYUG7qLPP96rmLdpcSZE4o,73
