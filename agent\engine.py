"""
Rilance Agent Engine
Advanced AI agent system inspired by Manus and Augment
Provides task decomposition, step execution, and tool calling capabilities
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from enum import Enum
from pathlib import Path

from core.logging import logger
from models.manager import ModelManager
from tools.manager import ToolManager
from agent.memory import Memory<PERSON>anager, MemoryType, MemoryImportance
from agent.context_engine import ReverieContextEngine, ContextType, ContextScope
from agent.types import (
    TaskStatus, StepType, ToolCallStatus, ToolCall, AgentStep, AgentTask
)
from agent.planner import TaskPlanner


# All types are now imported from server.agent.types


class AgentEngine:
    """Advanced Agent Engine inspired by Manus and Augment"""
    
    def __init__(self, model_manager: ModelManager):
        self.model_manager = model_manager
        self.tool_manager = ToolManager()
        self.memory_manager = MemoryManager()
        self.context_engine = ReverieContextEngine(self.memory_manager)
        self.planner = TaskPlanner(self.tool_manager)
        self.active_tasks: Dict[str, AgentTask] = {}
        self.task_queue: List[str] = []
        self.agent_memory: Dict[str, Any] = {}
        self.max_concurrent_tasks = 5

        # Enhanced workflow management
        self.task_dependencies: Dict[str, List[str]] = {}  # task_id -> [dependency_task_ids]
        self.task_retry_counts: Dict[str, int] = {}
        self.max_retries = 3
        self.workflow_state: Dict[str, Any] = {}
        self.error_recovery_strategies: Dict[str, Callable] = {}

        # Performance tracking
        self.task_metrics: Dict[str, Dict[str, Any]] = {}
        self.workflow_patterns: Dict[str, List[str]] = {}  # pattern_name -> [task_ids]

        # Event broadcasting callbacks
        self.event_callbacks: Dict[str, List[Callable]] = {
            "task_created": [],
            "task_updated": [],
            "step_started": [],
            "step_completed": [],
            "tool_called": [],
            "workflow_started": [],
            "workflow_completed": [],
            "error_recovery": [],
            "retry_attempted": []
        }

        logger.info("Agent Engine initialized")

    def register_event_callback(self, event_type: str, callback: Callable):
        """Register callback for events"""
        if event_type in self.event_callbacks:
            self.event_callbacks[event_type].append(callback)

    async def _broadcast_event(self, event_type: str, data: Dict[str, Any]):
        """Broadcast event to registered callbacks"""
        if event_type in self.event_callbacks:
            for callback in self.event_callbacks[event_type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    logger.error(f"Error in event callback: {e}")
    

    
    async def create_task(self, description: str, user_id: str = "default",
                         metadata: Optional[Dict[str, Any]] = None) -> str:
        """Create a new agent task with memory context"""
        task_id = str(uuid.uuid4())
        task = AgentTask(task_id, description, user_id)

        if metadata:
            task.metadata.update(metadata)

        # Get comprehensive context from both memory and context engine
        try:
            # Get memory context
            memory_context = await self.memory_manager.get_relevant_context(user_id, description)

            # Get relevant context from context engine
            context_items = await self.context_engine.get_relevant_context(
                query=description,
                context_types=[ContextType.FILE_CONTENT, ContextType.CODE_SYMBOLS, ContextType.PROJECT_STRUCTURE],
                max_items=10
            )

            # Get conversation context
            conversation_context = await self.context_engine.get_conversation_context(limit=5)

            # Combine all context
            comprehensive_context = {
                "memory_context": memory_context,
                "code_context": [item.to_dict() for item in context_items],
                "conversation_context": conversation_context,
                "context_summary": await self.context_engine.get_context_summary()
            }

            task.metadata["context"] = comprehensive_context

            # Add to conversation context
            await self.context_engine.add_conversation_context(
                message=f"Task created: {description}",
                role="system",
                metadata={"task_id": task_id, "action": "task_created"}
            )

            # Store task creation in memory
            await self.memory_manager.store_memory(
                memory_type=MemoryType.CONTEXT_INFO,
                content={
                    "task_id": task_id,
                    "description": description,
                    "action": "task_created",
                    "context_items_count": len(context_items)
                },
                user_id=user_id,
                importance=MemoryImportance.MEDIUM,
                tags={"task", "creation", "context"}
            )
        except Exception as e:
            logger.warning(f"Failed to get comprehensive context: {e}")

        self.active_tasks[task_id] = task
        self.task_queue.append(task_id)

        # Broadcast task created event
        await self._broadcast_event("task_created", task.to_dict())

        logger.info(f"Created task: {task_id} - {description}")
        return task_id
    
    async def execute_task(self, task_id: str) -> AgentTask:
        """Execute a task with full decomposition and step execution"""
        if task_id not in self.active_tasks:
            raise ValueError(f"Task {task_id} not found")
        
        task = self.active_tasks[task_id]
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()

        # Broadcast task updated event
        await self._broadcast_event("task_updated", task.to_dict())

        try:
            # Step 1: Decompose task into steps
            steps = await self._decompose_task(task)
            for step in steps:
                task.add_step(step)
            
            # Step 2: Execute each step
            for step in task.steps:
                # Broadcast step started
                await self._broadcast_event("step_started", {
                    "task_id": task_id,
                    "step_data": step.to_dict()
                })

                await self._execute_step(step)
                task.update_progress()

                # Broadcast step completed
                await self._broadcast_event("step_completed", {
                    "task_id": task_id,
                    "step_data": step.to_dict()
                })

                # Broadcast task progress update
                await self._broadcast_event("task_updated", task.to_dict())

                if step.status == TaskStatus.FAILED:
                    task.status = TaskStatus.FAILED
                    task.error = f"Step failed: {step.error}"
                    break
            
            # Step 3: Complete task
            if task.status != TaskStatus.FAILED:
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()
                task.result = "Task completed successfully"

        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            logger.error(f"Task execution failed: {e}")

        # Learn from task execution
        try:
            await self.memory_manager.learn_from_task(
                task_id=task_id,
                task_description=task.description,
                result=task.result or task.error or "No result",
                user_id=task.user_id,
                success=(task.status == TaskStatus.COMPLETED)
            )
        except Exception as e:
            logger.warning(f"Failed to learn from task: {e}")

        # Broadcast final task status
        await self._broadcast_event("task_updated", task.to_dict())

        return task
    
    async def _decompose_task(self, task: AgentTask) -> List[AgentStep]:
        """Decompose task into executable steps using intelligent planner"""

        try:
            # Use intelligent planner for task analysis and decomposition
            task_analysis = self.planner.analyze_task(task.description, self.agent_memory)

            # Store analysis in task metadata
            task.metadata["analysis"] = task_analysis

            # Create execution plan
            steps = self.planner.create_execution_plan(task_analysis, task.task_id)

            # Update agent memory with task context
            self.agent_memory[f"task_{task.task_id}"] = {
                "description": task.description,
                "analysis": task_analysis,
                "step_count": len(steps),
                "created_at": task.created_at.isoformat()
            }

            logger.info(f"Task decomposed into {len(steps)} steps using intelligent planner")
            return steps

        except Exception as e:
            logger.error(f"Intelligent task decomposition failed: {e}")
            # Fallback to basic decomposition
            return self._basic_decomposition(task)
    
    def _basic_decomposition(self, task: AgentTask) -> List[AgentStep]:
        """Basic task decomposition fallback"""
        steps = []
        
        step_templates = [
            (StepType.ANALYSIS, f"Analyze the task: {task.description}"),
            (StepType.PLANNING, "Create detailed execution plan"),
            (StepType.TOOL_CALL, "Execute necessary tool calls"),
            (StepType.VALIDATION, "Validate results"),
            (StepType.OUTPUT, "Provide final output")
        ]
        
        for i, (step_type, description) in enumerate(step_templates):
            step_id = f"{task.task_id}_step_{i}"
            step = AgentStep(step_id, step_type, description, task.task_id)
            steps.append(step)
        
        return steps
    
    async def _execute_step(self, step: AgentStep):
        """Execute a single step"""
        step.status = TaskStatus.RUNNING
        step.started_at = datetime.now()
        
        try:
            if step.step_type == StepType.TOOL_CALL:
                await self._execute_tool_step(step)
            elif step.step_type == StepType.CODE_EXECUTION:
                await self._execute_code_step(step)
            else:
                await self._execute_generic_step(step)
            
            step.status = TaskStatus.COMPLETED
            step.completed_at = datetime.now()
            
        except Exception as e:
            step.status = TaskStatus.FAILED
            step.error = str(e)
            logger.error(f"Step execution failed: {e}")
    
    async def _execute_tool_step(self, step: AgentStep):
        """Execute tool-based step"""
        try:
            # Extract tool calls from step input data
            if step.input_data and "tool_calls" in step.input_data:
                results = []
                for tool_call in step.input_data["tool_calls"]:
                    tool_name = tool_call.get("name")
                    parameters = tool_call.get("parameters", {})

                    # Execute tool using tool manager
                    result = await self.tool_manager.execute_tool(tool_name, parameters)

                    # Broadcast tool call event
                    await self._broadcast_event("tool_called", {
                        "task_id": step.task_id,
                        "tool_name": tool_name,
                        "result": result
                    })

                    results.append({
                        "tool": tool_name,
                        "parameters": parameters,
                        "result": result
                    })

                step.output_data = {"tool_results": results}
            else:
                step.output_data = {"result": "No tool calls specified"}

        except Exception as e:
            step.output_data = {"error": f"Tool execution failed: {str(e)}"}

    async def _execute_code_step(self, step: AgentStep):
        """Execute code-based step"""
        try:
            if step.input_data and "code" in step.input_data:
                code = step.input_data["code"]
                language = step.input_data.get("language", "python")

                # Use code execution tool
                result = await self.tool_manager.execute_tool("code_execute", {
                    "code": code,
                    "language": language
                })

                step.output_data = {"execution_result": result}
            else:
                step.output_data = {"result": "No code specified"}

        except Exception as e:
            step.output_data = {"error": f"Code execution failed: {str(e)}"}

    async def _execute_generic_step(self, step: AgentStep):
        """Execute generic step using LLM"""
        try:
            # Use LLM to process the step
            prompt = f"""
Execute the following step:
Type: {step.step_type.value}
Description: {step.description}
Input: {json.dumps(step.input_data) if step.input_data else 'None'}
Context: {json.dumps(self.agent_memory)}

Provide a detailed response about what was accomplished.
"""

            response = await self.model_manager.generate_text(
                prompt,
                max_tokens=1000,
                temperature=0.3
            )

            step.output_data = {"llm_response": response}

        except Exception as e:
            step.output_data = {"error": f"LLM processing failed: {str(e)}"}
    
    def get_task(self, task_id: str) -> Optional[AgentTask]:
        """Get task by ID"""
        return self.active_tasks.get(task_id)
    
    def list_tasks(self, user_id: str = None) -> List[AgentTask]:
        """List tasks, optionally filtered by user"""
        tasks = list(self.active_tasks.values())
        if user_id:
            tasks = [t for t in tasks if t.user_id == user_id]
        return tasks
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a task"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            task.status = TaskStatus.CANCELLED
            return True
        return False

    async def get_user_memory(self, user_id: str, memory_type: Optional[MemoryType] = None,
                             limit: int = 50) -> List[Dict[str, Any]]:
        """Get user memories"""
        try:
            memories = await self.memory_manager.retrieve_memories(
                user_id=user_id,
                memory_type=memory_type,
                limit=limit
            )
            return [memory.to_dict() for memory in memories]
        except Exception as e:
            logger.error(f"Failed to get user memory: {e}")
            return []

    async def search_user_memory(self, user_id: str, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Search user memories"""
        try:
            memories = await self.memory_manager.search_memories(
                user_id=user_id,
                query=query,
                limit=limit
            )
            return [memory.to_dict() for memory in memories]
        except Exception as e:
            logger.error(f"Failed to search user memory: {e}")
            return []

    async def store_user_preference(self, user_id: str, preference_key: str, preference_value: Any):
        """Store user preference"""
        try:
            await self.memory_manager.store_memory(
                memory_type=MemoryType.USER_PREFERENCE,
                content={preference_key: preference_value},
                user_id=user_id,
                importance=MemoryImportance.HIGH,
                tags={"preference", preference_key}
            )
        except Exception as e:
            logger.error(f"Failed to store user preference: {e}")

    async def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive user profile"""
        try:
            return await self.memory_manager.get_user_profile(user_id)
        except Exception as e:
            logger.error(f"Failed to get user profile: {e}")
            return {"user_id": user_id, "preferences": {}, "patterns": {}}

    async def cleanup_memory(self):
        """Clean up expired memories"""
        try:
            await self.memory_manager.cleanup_expired_memories()
            await self.context_engine.cleanup_old_context()
        except Exception as e:
            logger.error(f"Failed to cleanup memory: {e}")

    async def analyze_project_context(self, project_path: str) -> Dict[str, Any]:
        """Analyze project for context"""
        try:
            from pathlib import Path
            return await self.context_engine.analyze_project_structure(Path(project_path))
        except Exception as e:
            logger.error(f"Failed to analyze project context: {e}")
            return {}

    async def extract_file_context(self, file_path: str, query: str = None) -> List[Dict[str, Any]]:
        """Extract context from a specific file"""
        try:
            from pathlib import Path
            context_items = await self.context_engine.extract_file_context(Path(file_path), query)
            return [item.to_dict() for item in context_items]
        except Exception as e:
            logger.error(f"Failed to extract file context: {e}")
            return []

    async def get_context_summary(self) -> Dict[str, Any]:
        """Get comprehensive context summary"""
        try:
            return await self.context_engine.get_context_summary()
        except Exception as e:
            logger.error(f"Failed to get context summary: {e}")
            return {}

    async def search_context(self, query: str, context_types: List[str] = None,
                           max_items: int = 20) -> List[Dict[str, Any]]:
        """Search context with query"""
        try:
            # Convert string context types to enum
            context_type_enums = []
            if context_types:
                for ct in context_types:
                    try:
                        context_type_enums.append(ContextType(ct))
                    except ValueError:
                        logger.warning(f"Invalid context type: {ct}")

            context_items = await self.context_engine.get_relevant_context(
                query=query,
                context_types=context_type_enums if context_type_enums else None,
                max_items=max_items
            )

            return [item.to_dict() for item in context_items]
        except Exception as e:
            logger.error(f"Failed to search context: {e}")
            return []

    # Enhanced Workflow Management Methods

    async def create_workflow(self, workflow_name: str, tasks: List[Dict[str, Any]],
                             dependencies: Dict[str, List[str]] = None,
                             user_id: str = "default") -> str:
        """Create a multi-task workflow with dependencies"""
        workflow_id = str(uuid.uuid4())

        try:
            # Create all tasks first
            task_ids = []
            task_mapping = {}  # original_id -> actual_task_id

            for task_config in tasks:
                task_id = await self.create_task(
                    description=task_config["description"],
                    user_id=user_id,
                    metadata={
                        "workflow_id": workflow_id,
                        "workflow_name": workflow_name,
                        "original_task_id": task_config.get("id", ""),
                        **task_config.get("metadata", {})
                    }
                )
                task_ids.append(task_id)
                if "id" in task_config:
                    task_mapping[task_config["id"]] = task_id

            # Set up dependencies
            if dependencies:
                for task_original_id, dep_original_ids in dependencies.items():
                    if task_original_id in task_mapping:
                        actual_task_id = task_mapping[task_original_id]
                        actual_dep_ids = [task_mapping[dep_id] for dep_id in dep_original_ids
                                        if dep_id in task_mapping]
                        self.task_dependencies[actual_task_id] = actual_dep_ids

            # Store workflow metadata
            self.workflow_state[workflow_id] = {
                "name": workflow_name,
                "task_ids": task_ids,
                "dependencies": dependencies or {},
                "created_at": datetime.now().isoformat(),
                "status": "created",
                "user_id": user_id
            }

            # Broadcast workflow created event
            await self._broadcast_event("workflow_started", {
                "workflow_id": workflow_id,
                "workflow_name": workflow_name,
                "task_count": len(task_ids)
            })

            logger.info(f"Created workflow '{workflow_name}' with {len(task_ids)} tasks")
            return workflow_id

        except Exception as e:
            logger.error(f"Failed to create workflow: {e}")
            raise AgentException(f"Workflow creation failed: {str(e)}")

    async def execute_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Execute a workflow with dependency management"""
        if workflow_id not in self.workflow_state:
            raise ValueError(f"Workflow {workflow_id} not found")

        workflow = self.workflow_state[workflow_id]
        workflow["status"] = "running"
        workflow["started_at"] = datetime.now().isoformat()

        try:
            # Execute tasks in dependency order
            completed_tasks = set()
            failed_tasks = set()

            while len(completed_tasks) + len(failed_tasks) < len(workflow["task_ids"]):
                # Find tasks ready to execute (dependencies satisfied)
                ready_tasks = []
                for task_id in workflow["task_ids"]:
                    if (task_id not in completed_tasks and
                        task_id not in failed_tasks and
                        self._are_dependencies_satisfied(task_id, completed_tasks)):
                        ready_tasks.append(task_id)

                if not ready_tasks:
                    # Check if we're stuck due to failed dependencies
                    remaining_tasks = set(workflow["task_ids"]) - completed_tasks - failed_tasks
                    if remaining_tasks:
                        logger.warning(f"Workflow {workflow_id} stuck - no ready tasks")
                        break

                # Execute ready tasks (potentially in parallel)
                for task_id in ready_tasks:
                    try:
                        result = await self.execute_task_with_retry(task_id)
                        if result.status == TaskStatus.COMPLETED:
                            completed_tasks.add(task_id)
                        else:
                            failed_tasks.add(task_id)
                            # Try error recovery
                            if await self._attempt_error_recovery(task_id, result):
                                completed_tasks.add(task_id)
                    except Exception as e:
                        logger.error(f"Task {task_id} failed in workflow: {e}")
                        failed_tasks.add(task_id)

            # Determine workflow result
            if len(completed_tasks) == len(workflow["task_ids"]):
                workflow["status"] = "completed"
                workflow["result"] = "All tasks completed successfully"
            else:
                workflow["status"] = "failed"
                workflow["result"] = f"Completed: {len(completed_tasks)}, Failed: {len(failed_tasks)}"

            workflow["completed_at"] = datetime.now().isoformat()

            # Broadcast workflow completion
            await self._broadcast_event("workflow_completed", {
                "workflow_id": workflow_id,
                "status": workflow["status"],
                "completed_tasks": len(completed_tasks),
                "failed_tasks": len(failed_tasks)
            })

            return workflow

        except Exception as e:
            workflow["status"] = "error"
            workflow["error"] = str(e)
            logger.error(f"Workflow execution failed: {e}")
            return workflow

    def _are_dependencies_satisfied(self, task_id: str, completed_tasks: set) -> bool:
        """Check if task dependencies are satisfied"""
        dependencies = self.task_dependencies.get(task_id, [])
        return all(dep_id in completed_tasks for dep_id in dependencies)

    async def execute_task_with_retry(self, task_id: str) -> AgentTask:
        """Execute task with retry mechanism"""
        retry_count = self.task_retry_counts.get(task_id, 0)

        while retry_count <= self.max_retries:
            try:
                result = await self.execute_task(task_id)

                # Track successful execution
                self._track_task_metrics(task_id, result, retry_count)

                if result.status == TaskStatus.COMPLETED:
                    return result
                elif retry_count < self.max_retries:
                    # Retry failed task
                    retry_count += 1
                    self.task_retry_counts[task_id] = retry_count

                    await self._broadcast_event("retry_attempted", {
                        "task_id": task_id,
                        "retry_count": retry_count,
                        "max_retries": self.max_retries
                    })

                    logger.info(f"Retrying task {task_id} (attempt {retry_count}/{self.max_retries})")

                    # Wait before retry with exponential backoff
                    await asyncio.sleep(2 ** retry_count)
                else:
                    # Max retries exceeded
                    logger.error(f"Task {task_id} failed after {self.max_retries} retries")
                    return result

            except Exception as e:
                if retry_count < self.max_retries:
                    retry_count += 1
                    self.task_retry_counts[task_id] = retry_count
                    logger.warning(f"Task {task_id} exception on attempt {retry_count}: {e}")
                    await asyncio.sleep(2 ** retry_count)
                else:
                    logger.error(f"Task {task_id} failed with exception after retries: {e}")
                    # Create a failed task result
                    task = self.get_task(task_id)
                    if task:
                        task.status = TaskStatus.FAILED
                        task.error = str(e)
                    return task

        # Should not reach here, but return the task anyway
        return self.get_task(task_id)

    async def _attempt_error_recovery(self, task_id: str, failed_task: AgentTask) -> bool:
        """Attempt to recover from task failure"""
        try:
            # Check if we have a recovery strategy for this type of error
            error_type = self._classify_error(failed_task.error or "")

            if error_type in self.error_recovery_strategies:
                recovery_func = self.error_recovery_strategies[error_type]

                await self._broadcast_event("error_recovery", {
                    "task_id": task_id,
                    "error_type": error_type,
                    "recovery_strategy": recovery_func.__name__
                })

                # Attempt recovery
                recovery_result = await recovery_func(task_id, failed_task)

                if recovery_result:
                    logger.info(f"Successfully recovered task {task_id} using {error_type} strategy")
                    return True

            # Generic recovery attempts
            if "timeout" in (failed_task.error or "").lower():
                # For timeout errors, try with increased timeout
                return await self._recover_from_timeout(task_id, failed_task)
            elif "permission" in (failed_task.error or "").lower():
                # For permission errors, try alternative approach
                return await self._recover_from_permission_error(task_id, failed_task)

            return False

        except Exception as e:
            logger.error(f"Error recovery failed for task {task_id}: {e}")
            return False

    def _classify_error(self, error_message: str) -> str:
        """Classify error type for recovery strategy selection"""
        error_lower = error_message.lower()

        if "timeout" in error_lower:
            return "timeout"
        elif "permission" in error_lower or "access" in error_lower:
            return "permission"
        elif "network" in error_lower or "connection" in error_lower:
            return "network"
        elif "file not found" in error_lower or "no such file" in error_lower:
            return "file_not_found"
        elif "tool" in error_lower and "not found" in error_lower:
            return "tool_not_found"
        else:
            return "generic"

    async def _recover_from_timeout(self, task_id: str, failed_task: AgentTask) -> bool:
        """Recover from timeout errors"""
        try:
            # Increase timeout and retry
            task = self.get_task(task_id)
            if task and task.metadata:
                current_timeout = task.metadata.get("timeout", 30)
                task.metadata["timeout"] = min(current_timeout * 2, 300)  # Max 5 minutes

                # Reset task status for retry
                task.status = TaskStatus.PENDING
                task.error = None

                return True
        except Exception as e:
            logger.error(f"Timeout recovery failed: {e}")

        return False

    async def _recover_from_permission_error(self, task_id: str, failed_task: AgentTask) -> bool:
        """Recover from permission errors"""
        try:
            # Try alternative approach or request elevated permissions
            task = self.get_task(task_id)
            if task:
                # Mark for manual intervention or alternative strategy
                task.metadata["requires_manual_intervention"] = True
                task.metadata["recovery_suggestion"] = "Check file permissions or use alternative approach"

                # For now, we'll mark it as requiring manual intervention
                # In a real implementation, this could trigger user prompts or alternative workflows
                return False
        except Exception as e:
            logger.error(f"Permission recovery failed: {e}")

        return False

    def _track_task_metrics(self, task_id: str, task: AgentTask, retry_count: int):
        """Track task execution metrics"""
        if task_id not in self.task_metrics:
            self.task_metrics[task_id] = {
                "execution_count": 0,
                "total_duration": 0,
                "retry_count": 0,
                "success_rate": 0
            }

        metrics = self.task_metrics[task_id]
        metrics["execution_count"] += 1
        metrics["retry_count"] = retry_count

        if task.started_at and task.completed_at:
            duration = (task.completed_at - task.started_at).total_seconds()
            metrics["total_duration"] += duration
            metrics["average_duration"] = metrics["total_duration"] / metrics["execution_count"]

        if task.status == TaskStatus.COMPLETED:
            metrics["success_rate"] = (metrics.get("successes", 0) + 1) / metrics["execution_count"]
            metrics["successes"] = metrics.get("successes", 0) + 1

    def register_error_recovery_strategy(self, error_type: str, recovery_func: Callable):
        """Register a custom error recovery strategy"""
        self.error_recovery_strategies[error_type] = recovery_func
        logger.info(f"Registered error recovery strategy for {error_type}")

    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get workflow status and progress"""
        if workflow_id not in self.workflow_state:
            return {"error": "Workflow not found"}

        workflow = self.workflow_state[workflow_id].copy()

        # Add task status summary
        task_statuses = {}
        for task_id in workflow["task_ids"]:
            task = self.get_task(task_id)
            if task:
                task_statuses[task_id] = {
                    "status": task.status.value,
                    "progress": task.progress,
                    "error": task.error
                }

        workflow["task_statuses"] = task_statuses
        workflow["metrics"] = {
            task_id: self.task_metrics.get(task_id, {})
            for task_id in workflow["task_ids"]
        }

        return workflow

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics"""
        return {
            "task_metrics": self.task_metrics,
            "workflow_patterns": self.workflow_patterns,
            "active_tasks_count": len(self.active_tasks),
            "total_workflows": len(self.workflow_state),
            "error_recovery_strategies": list(self.error_recovery_strategies.keys())
        }
