"""
Intelligent Task Planner for Rilance Agent
Inspired by Manus planning capabilities
"""

import json
import re
from typing import Dict, List, Any, Optional
from enum import Enum

from agent.types import StepType, AgentStep, TaskStatus
from core.logging import logger


class TaskComplexity(Enum):
    """Task complexity levels"""
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    VERY_COMPLEX = "very_complex"


class TaskCategory(Enum):
    """Task categories"""
    CODE_GENERATION = "code_generation"
    CODE_ANALYSIS = "code_analysis"
    FILE_OPERATIONS = "file_operations"
    WEB_RESEARCH = "web_research"
    PROJECT_MANAGEMENT = "project_management"
    DEBUGGING = "debugging"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    DEPLOYMENT = "deployment"
    GENERAL = "general"


class TaskPlanner:
    """Intelligent task planner inspired by Man<PERSON>"""
    
    def __init__(self, tool_manager):
        self.tool_manager = tool_manager
        self.planning_templates = self._load_planning_templates()
        
        logger.info("Task Planner initialized")
    
    def _load_planning_templates(self) -> Dict[str, List[Dict[str, Any]]]:
        """Load planning templates for different task types"""
        return {
            TaskCategory.CODE_GENERATION.value: [
                {"type": StepType.ANALYSIS, "description": "Analyze requirements and specifications"},
                {"type": StepType.PLANNING, "description": "Design code structure and architecture"},
                {"type": StepType.TOOL_CALL, "description": "Generate code using appropriate tools"},
                {"type": StepType.VALIDATION, "description": "Validate generated code"},
                {"type": StepType.OUTPUT, "description": "Provide final code output"}
            ],
            
            TaskCategory.CODE_ANALYSIS.value: [
                {"type": StepType.ANALYSIS, "description": "Read and parse code files"},
                {"type": StepType.TOOL_CALL, "description": "Analyze code structure and patterns"},
                {"type": StepType.VALIDATION, "description": "Identify issues and improvements"},
                {"type": StepType.OUTPUT, "description": "Provide analysis report"}
            ],
            
            TaskCategory.FILE_OPERATIONS.value: [
                {"type": StepType.ANALYSIS, "description": "Analyze file operation requirements"},
                {"type": StepType.PLANNING, "description": "Plan file operations sequence"},
                {"type": StepType.TOOL_CALL, "description": "Execute file operations"},
                {"type": StepType.VALIDATION, "description": "Verify operations completed successfully"},
                {"type": StepType.OUTPUT, "description": "Report operation results"}
            ],
            
            TaskCategory.WEB_RESEARCH.value: [
                {"type": StepType.ANALYSIS, "description": "Analyze research requirements"},
                {"type": StepType.TOOL_CALL, "description": "Perform web searches"},
                {"type": StepType.ANALYSIS, "description": "Analyze and synthesize findings"},
                {"type": StepType.OUTPUT, "description": "Provide research summary"}
            ],
            
            TaskCategory.DEBUGGING.value: [
                {"type": StepType.ANALYSIS, "description": "Analyze error symptoms and context"},
                {"type": StepType.TOOL_CALL, "description": "Examine code and logs"},
                {"type": StepType.PLANNING, "description": "Develop debugging strategy"},
                {"type": StepType.TOOL_CALL, "description": "Implement fixes"},
                {"type": StepType.VALIDATION, "description": "Test fixes"},
                {"type": StepType.OUTPUT, "description": "Document solution"}
            ],
            
            TaskCategory.GENERAL.value: [
                {"type": StepType.ANALYSIS, "description": "Analyze task requirements"},
                {"type": StepType.PLANNING, "description": "Create execution plan"},
                {"type": StepType.TOOL_CALL, "description": "Execute planned actions"},
                {"type": StepType.VALIDATION, "description": "Validate results"},
                {"type": StepType.OUTPUT, "description": "Provide final output"}
            ]
        }
    
    def analyze_task(self, description: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Analyze task to determine complexity, category, and requirements"""
        
        analysis = {
            "description": description,
            "complexity": self._assess_complexity(description),
            "category": self._categorize_task(description),
            "estimated_steps": 0,
            "required_tools": [],
            "risks": [],
            "dependencies": []
        }
        
        # Analyze for tool requirements
        analysis["required_tools"] = self._identify_required_tools(description)
        
        # Assess risks
        analysis["risks"] = self._assess_risks(description, analysis["complexity"])
        
        # Estimate steps
        analysis["estimated_steps"] = self._estimate_steps(analysis["complexity"], analysis["category"])
        
        logger.info(f"Task analysis: {analysis['category'].value} - {analysis['complexity'].value}")
        
        return analysis
    
    def _assess_complexity(self, description: str) -> TaskComplexity:
        """Assess task complexity based on description"""
        
        # Simple heuristics for complexity assessment
        complexity_indicators = {
            TaskComplexity.SIMPLE: [
                "read", "list", "show", "display", "get", "simple"
            ],
            TaskComplexity.MEDIUM: [
                "create", "write", "modify", "update", "analyze", "search"
            ],
            TaskComplexity.COMPLEX: [
                "implement", "develop", "build", "design", "refactor", "optimize"
            ],
            TaskComplexity.VERY_COMPLEX: [
                "architecture", "system", "framework", "multiple", "integrate", "deploy"
            ]
        }
        
        description_lower = description.lower()
        
        # Count indicators for each complexity level
        scores = {}
        for complexity, indicators in complexity_indicators.items():
            score = sum(1 for indicator in indicators if indicator in description_lower)
            scores[complexity] = score
        
        # Return highest scoring complexity (default to MEDIUM)
        if scores:
            return max(scores.items(), key=lambda x: x[1])[0]
        
        return TaskComplexity.MEDIUM
    
    def _categorize_task(self, description: str) -> TaskCategory:
        """Categorize task based on description"""
        
        category_keywords = {
            TaskCategory.CODE_GENERATION: [
                "generate", "create code", "write function", "implement", "develop"
            ],
            TaskCategory.CODE_ANALYSIS: [
                "analyze code", "review", "examine", "inspect", "audit"
            ],
            TaskCategory.FILE_OPERATIONS: [
                "file", "directory", "folder", "read file", "write file", "copy", "move"
            ],
            TaskCategory.WEB_RESEARCH: [
                "search", "research", "find information", "web", "lookup"
            ],
            TaskCategory.DEBUGGING: [
                "debug", "fix", "error", "bug", "issue", "problem", "troubleshoot"
            ],
            TaskCategory.DOCUMENTATION: [
                "document", "readme", "docs", "comment", "explain"
            ],
            TaskCategory.TESTING: [
                "test", "unit test", "integration test", "verify", "validate"
            ]
        }
        
        description_lower = description.lower()
        
        # Score each category
        scores = {}
        for category, keywords in category_keywords.items():
            score = sum(1 for keyword in keywords if keyword in description_lower)
            scores[category] = score
        
        # Return highest scoring category (default to GENERAL)
        if scores and max(scores.values()) > 0:
            return max(scores.items(), key=lambda x: x[1])[0]
        
        return TaskCategory.GENERAL
    
    def _identify_required_tools(self, description: str) -> List[str]:
        """Identify tools likely needed for the task"""
        
        tool_keywords = {
            "file_read": ["read file", "open file", "view file", "examine file"],
            "file_write": ["write file", "create file", "save file", "edit file"],
            "file_list": ["list files", "directory", "folder contents"],
            "web_search": ["search", "research", "find information", "lookup"],
            "code_execute": ["run code", "execute", "test code"],
            "command_execute": ["run command", "execute command", "shell"],
            "git_operations": ["git", "commit", "push", "pull", "clone"],
            "project_analyze": ["analyze project", "project structure"]
        }
        
        description_lower = description.lower()
        required_tools = []
        
        for tool, keywords in tool_keywords.items():
            if any(keyword in description_lower for keyword in keywords):
                required_tools.append(tool)
        
        return required_tools
    
    def _assess_risks(self, description: str, complexity: TaskComplexity) -> List[str]:
        """Assess potential risks for the task"""
        
        risks = []
        
        # Complexity-based risks
        if complexity in [TaskComplexity.COMPLEX, TaskComplexity.VERY_COMPLEX]:
            risks.append("High complexity may require multiple iterations")
            risks.append("May need additional clarification")
        
        # Content-based risks
        description_lower = description.lower()
        
        if any(word in description_lower for word in ["delete", "remove", "destroy"]):
            risks.append("Destructive operations - requires careful validation")
        
        if any(word in description_lower for word in ["system", "production", "deploy"]):
            risks.append("System-level changes - high impact")
        
        if "multiple" in description_lower or "many" in description_lower:
            risks.append("Multiple operations - potential for partial failures")
        
        return risks
    
    def _estimate_steps(self, complexity: TaskComplexity, category: TaskCategory) -> int:
        """Estimate number of steps needed"""
        
        base_steps = {
            TaskComplexity.SIMPLE: 3,
            TaskComplexity.MEDIUM: 5,
            TaskComplexity.COMPLEX: 7,
            TaskComplexity.VERY_COMPLEX: 10
        }
        
        category_modifiers = {
            TaskCategory.CODE_GENERATION: 1.2,
            TaskCategory.DEBUGGING: 1.3,
            TaskCategory.PROJECT_MANAGEMENT: 1.4,
            TaskCategory.DEPLOYMENT: 1.5
        }
        
        base = base_steps.get(complexity, 5)
        modifier = category_modifiers.get(category, 1.0)
        
        return max(3, int(base * modifier))
    
    def create_execution_plan(self, task_analysis: Dict[str, Any], task_id: str) -> List[AgentStep]:
        """Create detailed execution plan based on task analysis"""
        
        category = task_analysis["category"]
        complexity = task_analysis["complexity"]
        
        # Get template for category
        template = self.planning_templates.get(
            category.value, 
            self.planning_templates[TaskCategory.GENERAL.value]
        )
        
        steps = []
        
        # Create steps based on template
        for i, step_template in enumerate(template):
            step_id = f"{task_id}_step_{i}"
            step = AgentStep(
                step_id=step_id,
                step_type=step_template["type"],
                description=step_template["description"],
                task_id=task_id
            )
            
            # Add tool requirements if this is a tool step
            if step.step_type == StepType.TOOL_CALL:
                step.input_data = {
                    "required_tools": task_analysis["required_tools"],
                    "category": category.value
                }
            
            steps.append(step)
        
        # Add complexity-specific steps
        if complexity == TaskComplexity.VERY_COMPLEX:
            # Add additional validation step
            validation_step = AgentStep(
                step_id=f"{task_id}_step_validation_extra",
                step_type=StepType.VALIDATION,
                description="Additional validation for complex task",
                task_id=task_id
            )
            steps.insert(-1, validation_step)  # Insert before output step
        
        logger.info(f"Created execution plan with {len(steps)} steps")
        
        return steps
