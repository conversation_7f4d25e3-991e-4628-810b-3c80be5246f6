Metadata-Version: 2.4
Name: docstring-to-markdown
Version: 0.17
Summary: On the fly conversion of Python docstrings to markdown
Author: <PERSON><PERSON><PERSON>
Author-email: <EMAIL>
License: LGPL-2.1-or-later
Project-URL: <PERSON><PERSON> Tracker, https://github.com/python-lsp/docstring-to-markdown/issues
Project-URL: Source Code, https://github.com/python-lsp/docstring-to-markdown
Keywords: Docstring,conversion,markdown
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v2 or later (LGPLv2+)
Classifier: Programming Language :: Python
Classifier: Topic :: Text Processing :: Markup
Classifier: Topic :: Documentation :: Sphinx
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: importlib-metadata>=3.6
Requires-Dist: typing_extensions>=4.6
Dynamic: license-file

# docstring-to-markdown

[![tests](https://github.com/python-lsp/docstring-to-markdown/workflows/tests/badge.svg)](https://github.com/python-lsp/docstring-to-markdown/actions?query=workflow%3A%22tests%22)
![CodeQL](https://github.com/python-lsp/docstring-to-markdown/workflows/CodeQL/badge.svg)
[![pypi-version](https://img.shields.io/pypi/v/docstring-to-markdown.svg)](https://python.org/pypi/docstring-to-markdown)

On the fly conversion of Python docstrings to markdown

- Python 3.7+ (tested on 3.8 up to 3.13)
- can recognise reStructuredText and convert multiple of its features to Markdown
- since v0.13 includes initial support for Google-formatted docstrings

### Installation

```bash
pip install docstring-to-markdown
```

### Example

Convert reStructuredText:

```python
>>> import docstring_to_markdown
>>> docstring_to_markdown.convert(':math:`\\sum`')
'$\\sum$'
```

When given the format cannot be recognised an exception will be raised:

```python
>>> docstring_to_markdown.convert('\\sum')
Traceback (most recent call last):
    raise UnknownFormatError()
docstring_to_markdown.UnknownFormatError
```

### Extensibility

`docstring_to_markdown` entry point group allows to add custom converters which follow the `Converter` protocol.
The built-in converters can be customized by providing entry point with matching name.

### Development

```bash
pip install -e .
pytest
```
