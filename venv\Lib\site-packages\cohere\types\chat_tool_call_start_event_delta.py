# This file was auto-generated by <PERSON>rn from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .chat_tool_call_start_event_delta_message import ChatToolCallStartEventDeltaMessage
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class ChatToolCallStartEventDelta(UncheckedBaseModel):
    message: typing.Optional[ChatToolCallStartEventDeltaMessage] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
