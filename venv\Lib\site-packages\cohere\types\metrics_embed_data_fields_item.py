# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class MetricsEmbedDataFieldsItem(UncheckedBaseModel):
    name: typing.Optional[str] = pydantic.Field(default=None)
    """
    the name of the field
    """

    count: typing.Optional[float] = pydantic.Field(default=None)
    """
    the number of times the field appears in the dataset
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
