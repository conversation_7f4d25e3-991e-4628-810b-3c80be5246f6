"""
Intelligent Search System for Reverie Code Studio
Automatically detects when web search is needed and formats results beautifully
"""

import re
import json
import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator
from datetime import datetime
from core.logging import logger


class IntelligentSearchDetector:
    """Detects when web search should be automatically triggered"""
    
    # Keywords that suggest need for current information
    SEARCH_TRIGGERS = {
        'temporal': [
            'latest', 'newest', 'recent', 'current', 'updated', 'new', 'modern',
            'today', 'now', '2024', '2025', 'this year', 'recently'
        ],
        'version': [
            'version', 'release', 'update', 'changelog', 'what\'s new',
            'features', 'deprecated', 'support', 'compatibility'
        ],
        'comparison': [
            'vs', 'versus', 'compare', 'comparison', 'better', 'best',
            'alternative', 'difference', 'which', 'choose'
        ],
        'learning': [
            'tutorial', 'guide', 'how to', 'learn', 'example', 'documentation',
            'getting started', 'introduction', 'beginner'
        ],
        'troubleshooting': [
            'error', 'issue', 'problem', 'fix', 'solve', 'debug',
            'troubleshoot', 'not working', 'broken'
        ],
        'specific_tech': [
            'python', 'javascript', 'react', 'vue', 'angular', 'node',
            'fastapi', 'django', 'flask', 'pytorch', 'tensorflow'
        ]
    }
    
    # Phrases that strongly indicate need for search
    STRONG_INDICATORS = [
        "what are the latest",
        "what's new in",
        "how to install",
        "best practices for",
        "current version of",
        "is there a way to",
        "how do I",
        "what is the difference between",
        "which is better",
        "how to fix",
        "error when",
        "tutorial for",
        "guide to"
    ]
    
    def should_search(self, user_message: str, context_messages: List[Dict] = None) -> bool:
        """Determine if web search should be triggered"""
        
        message_lower = user_message.lower()
        
        # Check for strong indicators first
        for indicator in self.STRONG_INDICATORS:
            if indicator in message_lower:
                logger.info(f"🔍 Search triggered by strong indicator: '{indicator}'")
                return True
        
        # Count trigger words by category
        trigger_score = 0
        triggered_categories = []
        
        for category, keywords in self.SEARCH_TRIGGERS.items():
            category_hits = sum(1 for keyword in keywords if keyword in message_lower)
            if category_hits > 0:
                trigger_score += category_hits
                triggered_categories.append(category)
        
        # Decision logic
        if trigger_score >= 3:  # Multiple trigger words
            logger.info(f"🔍 Search triggered by score {trigger_score}, categories: {triggered_categories}")
            return True
        
        if len(triggered_categories) >= 2:  # Multiple categories
            logger.info(f"🔍 Search triggered by multiple categories: {triggered_categories}")
            return True
        
        # Check for question patterns that likely need current info
        question_patterns = [
            r"what.*(?:latest|new|current|best|recommended)",
            r"how.*(?:install|setup|configure|use)",
            r"which.*(?:better|best|recommended|should)",
            r"is.*(?:supported|compatible|available)",
            r"does.*(?:support|work|have)"
        ]
        
        for pattern in question_patterns:
            if re.search(pattern, message_lower):
                logger.info(f"🔍 Search triggered by question pattern: {pattern}")
                return True
        
        return False
    
    def extract_search_query(self, user_message: str) -> str:
        """Extract an appropriate search query from user message"""
        
        # Remove common conversational elements
        clean_message = re.sub(r'\b(please|can you|could you|would you|help me|i want to|i need to)\b', '', user_message, flags=re.IGNORECASE)
        clean_message = re.sub(r'\b(what|how|when|where|why|which|who)\s+(is|are|do|does|can|should|would|could)\b', '', clean_message, flags=re.IGNORECASE)
        
        # Extract key technical terms
        tech_terms = []
        for category, keywords in self.SEARCH_TRIGGERS.items():
            for keyword in keywords:
                if keyword in clean_message.lower() and len(keyword) > 3:
                    tech_terms.append(keyword)
        
        # Build search query
        if tech_terms:
            # Prioritize technical terms
            query_parts = tech_terms[:3]  # Limit to top 3 terms
        else:
            # Fall back to important words from the message
            words = clean_message.split()
            important_words = [w for w in words if len(w) > 3 and w.lower() not in ['the', 'and', 'for', 'with', 'this', 'that']]
            query_parts = important_words[:4]  # Limit to 4 words
        
        query = ' '.join(query_parts).strip()
        
        # Add context for better results
        if any(word in user_message.lower() for word in ['tutorial', 'guide', 'how to']):
            query += ' tutorial guide'
        elif any(word in user_message.lower() for word in ['error', 'fix', 'problem']):
            query += ' fix solution'
        elif any(word in user_message.lower() for word in ['latest', 'new', 'current']):
            query += ' 2024 latest'
        
        return query[:100]  # Limit query length


class SearchResultFormatter:
    """Formats search results in a beautiful, readable way"""
    
    @staticmethod
    def format_search_results(search_data: Dict[str, Any], query: str) -> str:
        """Format search results into beautiful display text"""
        
        if not search_data or not search_data.get('results'):
            return f"\n🔍 **Web Search**: No results found for '{query}'\n"
        
        results = search_data['results']
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # Header
        formatted = f"\n🌐 **Web Search Results** ({timestamp})\n"
        formatted += f"📝 Query: *{query}*\n"
        formatted += f"📊 Found {len(results)} results\n\n"
        
        # Format each result
        for i, result in enumerate(results[:3], 1):  # Limit to top 3 results
            title = result.get('title', 'No title')[:80]
            url = result.get('url', 'No URL')
            snippet = result.get('snippet', 'No description')[:200]
            
            formatted += f"**{i}. {title}**\n"
            formatted += f"   📄 {snippet}\n"
            formatted += f"   🔗 {url}\n\n"
        
        formatted += "---\n"
        return formatted


class IntelligentSearchProcessor:
    """Main processor for intelligent search functionality"""
    
    def __init__(self):
        self.detector = IntelligentSearchDetector()
        self.formatter = SearchResultFormatter()
    
    async def process_message_with_search(
        self, 
        user_message: str, 
        model_response_generator: AsyncGenerator[str, None],
        tool_manager,
        context_messages: List[Dict] = None
    ) -> AsyncGenerator[str, None]:
        """Process a message and automatically inject search results if needed"""
        
        # Check if search is needed
        should_search = self.detector.should_search(user_message, context_messages)
        
        if should_search:
            # Extract search query
            search_query = self.detector.extract_search_query(user_message)
            logger.info(f"🔍 Auto-searching for: '{search_query}'")
            
            # Perform search
            try:
                search_result = await tool_manager.web_search(search_query, max_results=3)
                
                # Format and yield search results first
                formatted_results = self.formatter.format_search_results(search_result, search_query)
                yield formatted_results
                
                # Then yield the model's response
                async for chunk in model_response_generator:
                    yield chunk
                    
            except Exception as e:
                logger.error(f"Auto-search failed: {e}")
                # Continue with model response even if search fails
                yield f"\n⚠️ *Web search temporarily unavailable*\n\n"
                async for chunk in model_response_generator:
                    yield chunk
        else:
            # No search needed, just pass through model response
            async for chunk in model_response_generator:
                yield chunk
