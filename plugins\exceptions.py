"""
Plugin System Exceptions for Reverie Code Studio
"""


class PluginException(Exception):
    """Base exception for plugin system errors"""
    
    def __init__(self, message: str, plugin_name: str = None):
        self.plugin_name = plugin_name
        super().__init__(message)
    
    def __str__(self):
        if self.plugin_name:
            return f"Plugin '{self.plugin_name}': {super().__str__()}"
        return super().__str__()


class PluginLoadError(PluginException):
    """Raised when a plugin fails to load"""
    pass


class PluginConfigError(PluginException):
    """Raised when plugin configuration is invalid"""
    pass


class PluginDependencyError(PluginException):
    """Raised when plugin dependencies are not met"""
    pass


class PluginVersionError(PluginException):
    """Raised when plugin version is incompatible"""
    pass


class PluginActivationError(PluginException):
    """Raised when plugin activation fails"""
    pass


class PluginDeactivationError(PluginException):
    """Raised when plugin deactivation fails"""
    pass


class PluginNotFoundError(PluginException):
    """Raised when a requested plugin is not found"""
    pass


class PluginAlreadyLoadedError(PluginException):
    """Raised when attempting to load an already loaded plugin"""
    pass
