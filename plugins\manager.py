"""
Plugin Manager for Reverie Code Studio
Central management system for plugin lifecycle and coordination
"""

import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Callable
from collections import defaultdict

from .base import BasePlugin, PluginMetadata, PluginConfig, PluginStatus, PluginCategory
from .registry import PluginRegistry
from .loader import <PERSON>luginLoader
from .exceptions import (
    PluginException, PluginLoadError, PluginDependencyError,
    PluginNotFoundError, PluginAlreadyLoadedError, PluginActivationError
)
from core.logging import logger
from core.config import settings


class PluginManager:
    """
    Central plugin management system
    
    Handles plugin discovery, loading, activation, deactivation, and coordination.
    Manages plugin dependencies and provides hooks for plugin communication.
    """
    
    def __init__(self, plugin_dirs: Optional[List[Path]] = None):
        # Plugin directories
        if plugin_dirs is None:
            plugin_dirs = [
                Path("server/plugins/builtin"),
                Path("server/plugins/community"),
                Path("server/plugins/custom")
            ]
        
        self.plugin_dirs = plugin_dirs
        
        # Core components
        self.registry = PluginRegistry(Path("server/plugins/registry"))
        self.loader = PluginLoader(plugin_dirs)
        
        # Runtime state
        self._loaded_plugins: Dict[str, BasePlugin] = {}
        self._active_plugins: Dict[str, BasePlugin] = {}
        self._plugin_hooks: Dict[str, List[Callable]] = defaultdict(list)
        
        # Dependency tracking
        self._dependency_graph: Dict[str, Set[str]] = {}
        self._reverse_dependencies: Dict[str, Set[str]] = {}
        
        logger.info("Plugin manager initialized")
    
    async def initialize(self):
        """Initialize the plugin manager"""
        # Initialize registry
        await self.registry.initialize()
        
        # Discover and register new plugins
        await self.discover_plugins()
        
        # Auto-load enabled plugins
        await self.auto_load_plugins()
        
        logger.info("Plugin manager initialization complete")
    
    async def discover_plugins(self):
        """Discover and register new plugins"""
        logger.info("Discovering plugins...")
        
        discovered = await self.loader.discover_plugins()
        
        for metadata in discovered:
            if not self.registry.plugin_exists(metadata.name):
                await self.registry.register_plugin(metadata)
                logger.info(f"Registered new plugin: {metadata.name}")
            else:
                # Update metadata if version changed
                existing = self.registry.get_plugin_metadata(metadata.name)
                if existing and existing.version != metadata.version:
                    await self.registry.register_plugin(metadata)
                    logger.info(f"Updated plugin metadata: {metadata.name}")
    
    async def auto_load_plugins(self):
        """Automatically load plugins marked for auto-loading"""
        enabled_plugins = self.registry.get_enabled_plugins()
        
        for plugin_name in enabled_plugins:
            config = self.registry.get_plugin_config(plugin_name)
            if config and config.auto_load:
                try:
                    await self.load_plugin(plugin_name)
                    await self.activate_plugin(plugin_name)
                except Exception as e:
                    logger.error(f"Failed to auto-load plugin {plugin_name}: {e}")
    
    async def load_plugin(self, plugin_name: str) -> BasePlugin:
        """
        Load a plugin by name
        
        Args:
            plugin_name: Name of the plugin to load
            
        Returns:
            Loaded plugin instance
        """
        if plugin_name in self._loaded_plugins:
            raise PluginAlreadyLoadedError(f"Plugin already loaded: {plugin_name}")
        
        # Get plugin metadata
        metadata = self.registry.get_plugin_metadata(plugin_name)
        if not metadata:
            raise PluginNotFoundError(f"Plugin not found: {plugin_name}")
        
        # Check dependencies
        await self._check_dependencies(plugin_name)
        
        try:
            # Load plugin
            plugin = await self.loader.load_plugin(plugin_name, metadata.plugin_path)
            
            # Set up plugin logger
            plugin.logger = logger.bind(plugin=plugin_name)
            
            # Initialize plugin
            success = await plugin.initialize()
            if not success:
                raise PluginLoadError(f"Plugin initialization failed: {plugin_name}")
            
            # Store plugin
            self._loaded_plugins[plugin_name] = plugin
            
            # Update status
            await self.registry.update_plugin_status(plugin_name, PluginStatus.LOADED)
            
            # Update dependency tracking
            self._update_dependency_graph(plugin_name, metadata.dependencies)
            
            logger.info(f"Plugin loaded: {plugin_name}")
            return plugin
            
        except Exception as e:
            await self.registry.update_plugin_status(plugin_name, PluginStatus.ERROR)
            raise PluginLoadError(f"Failed to load plugin {plugin_name}: {e}", plugin_name)
    
    async def unload_plugin(self, plugin_name: str):
        """
        Unload a plugin
        
        Args:
            plugin_name: Name of the plugin to unload
        """
        if plugin_name not in self._loaded_plugins:
            raise PluginNotFoundError(f"Plugin not loaded: {plugin_name}")
        
        # Check if other plugins depend on this one
        dependents = self._reverse_dependencies.get(plugin_name, set())
        if dependents:
            active_dependents = [dep for dep in dependents if dep in self._active_plugins]
            if active_dependents:
                raise PluginDependencyError(
                    f"Cannot unload plugin {plugin_name}: active dependents {active_dependents}"
                )
        
        try:
            plugin = self._loaded_plugins[plugin_name]
            
            # Deactivate if active
            if plugin_name in self._active_plugins:
                await self.deactivate_plugin(plugin_name)
            
            # Cleanup plugin
            await plugin.cleanup()
            
            # Remove from loaded plugins
            del self._loaded_plugins[plugin_name]
            
            # Update dependency tracking
            self._remove_from_dependency_graph(plugin_name)
            
            # Unload from loader
            await self.loader.unload_plugin(plugin_name)
            
            # Update status
            await self.registry.update_plugin_status(plugin_name, PluginStatus.UNLOADED)
            
            logger.info(f"Plugin unloaded: {plugin_name}")
            
        except Exception as e:
            await self.registry.update_plugin_status(plugin_name, PluginStatus.ERROR)
            raise PluginLoadError(f"Failed to unload plugin {plugin_name}: {e}", plugin_name)
    
    async def activate_plugin(self, plugin_name: str):
        """
        Activate a loaded plugin
        
        Args:
            plugin_name: Name of the plugin to activate
        """
        if plugin_name not in self._loaded_plugins:
            raise PluginNotFoundError(f"Plugin not loaded: {plugin_name}")
        
        if plugin_name in self._active_plugins:
            logger.warning(f"Plugin already active: {plugin_name}")
            return
        
        try:
            plugin = self._loaded_plugins[plugin_name]
            
            # Check if plugin is enabled
            config = self.registry.get_plugin_config(plugin_name)
            if not config or not config.enabled:
                raise PluginActivationError(f"Plugin is disabled: {plugin_name}")
            
            # Activate plugin
            success = await plugin.activate()
            if not success:
                raise PluginActivationError(f"Plugin activation failed: {plugin_name}")
            
            # Store as active
            self._active_plugins[plugin_name] = plugin
            
            # Update status
            await self.registry.update_plugin_status(plugin_name, PluginStatus.ACTIVE)
            
            # Register plugin hooks
            await self._register_plugin_hooks(plugin)
            
            logger.info(f"Plugin activated: {plugin_name}")
            
        except Exception as e:
            await self.registry.update_plugin_status(plugin_name, PluginStatus.ERROR)
            raise PluginActivationError(f"Failed to activate plugin {plugin_name}: {e}", plugin_name)
    
    async def deactivate_plugin(self, plugin_name: str):
        """
        Deactivate an active plugin
        
        Args:
            plugin_name: Name of the plugin to deactivate
        """
        if plugin_name not in self._active_plugins:
            logger.warning(f"Plugin not active: {plugin_name}")
            return
        
        try:
            plugin = self._active_plugins[plugin_name]
            
            # Deactivate plugin
            success = await plugin.deactivate()
            if not success:
                logger.warning(f"Plugin deactivation returned false: {plugin_name}")
            
            # Remove from active plugins
            del self._active_plugins[plugin_name]
            
            # Unregister plugin hooks
            await self._unregister_plugin_hooks(plugin)
            
            # Update status
            await self.registry.update_plugin_status(plugin_name, PluginStatus.LOADED)
            
            logger.info(f"Plugin deactivated: {plugin_name}")
            
        except Exception as e:
            logger.error(f"Failed to deactivate plugin {plugin_name}: {e}")
    
    async def reload_plugin(self, plugin_name: str):
        """
        Reload a plugin (unload and load again)
        
        Args:
            plugin_name: Name of the plugin to reload
        """
        was_active = plugin_name in self._active_plugins
        
        # Unload plugin
        if plugin_name in self._loaded_plugins:
            await self.unload_plugin(plugin_name)
        
        # Load plugin
        await self.load_plugin(plugin_name)
        
        # Activate if it was active before
        if was_active:
            await self.activate_plugin(plugin_name)
        
        logger.info(f"Plugin reloaded: {plugin_name}")
    
    def get_plugin(self, plugin_name: str) -> Optional[BasePlugin]:
        """Get a loaded plugin by name"""
        return self._loaded_plugins.get(plugin_name)
    
    def get_active_plugin(self, plugin_name: str) -> Optional[BasePlugin]:
        """Get an active plugin by name"""
        return self._active_plugins.get(plugin_name)
    
    def list_loaded_plugins(self) -> List[str]:
        """Get list of loaded plugin names"""
        return list(self._loaded_plugins.keys())
    
    def list_active_plugins(self) -> List[str]:
        """Get list of active plugin names"""
        return list(self._active_plugins.keys())
    
    def get_plugins_by_category(self, category: PluginCategory) -> List[BasePlugin]:
        """Get active plugins by category"""
        plugins = []
        for plugin in self._active_plugins.values():
            if plugin.metadata.category == category:
                plugins.append(plugin)
        return plugins
    
    async def emit_hook(self, event: str, *args, **kwargs):
        """Emit a hook event to all registered handlers"""
        if event in self._plugin_hooks:
            for handler in self._plugin_hooks[event]:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(*args, **kwargs)
                    else:
                        handler(*args, **kwargs)
                except Exception as e:
                    logger.error(f"Hook handler failed for event {event}: {e}")
    
    async def _check_dependencies(self, plugin_name: str):
        """Check if plugin dependencies are satisfied"""
        metadata = self.registry.get_plugin_metadata(plugin_name)
        if not metadata or not metadata.dependencies:
            return
        
        missing_deps = []
        for dep in metadata.dependencies:
            if dep not in self._loaded_plugins:
                missing_deps.append(dep)
        
        if missing_deps:
            raise PluginDependencyError(
                f"Plugin {plugin_name} has missing dependencies: {missing_deps}"
            )
    
    def _update_dependency_graph(self, plugin_name: str, dependencies: List[str]):
        """Update dependency tracking graphs"""
        self._dependency_graph[plugin_name] = set(dependencies)
        
        for dep in dependencies:
            if dep not in self._reverse_dependencies:
                self._reverse_dependencies[dep] = set()
            self._reverse_dependencies[dep].add(plugin_name)
    
    def _remove_from_dependency_graph(self, plugin_name: str):
        """Remove plugin from dependency tracking"""
        # Remove from dependency graph
        if plugin_name in self._dependency_graph:
            deps = self._dependency_graph[plugin_name]
            del self._dependency_graph[plugin_name]
            
            # Remove from reverse dependencies
            for dep in deps:
                if dep in self._reverse_dependencies:
                    self._reverse_dependencies[dep].discard(plugin_name)
        
        # Remove from reverse dependencies
        if plugin_name in self._reverse_dependencies:
            del self._reverse_dependencies[plugin_name]
    
    async def _register_plugin_hooks(self, plugin: BasePlugin):
        """Register hooks from a plugin"""
        # This would be implemented based on specific hook patterns
        pass
    
    async def _unregister_plugin_hooks(self, plugin: BasePlugin):
        """Unregister hooks from a plugin"""
        # This would be implemented based on specific hook patterns
        pass
