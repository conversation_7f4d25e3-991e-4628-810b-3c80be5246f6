"""
Main API routes for Reverie Code Studio Server
"""

from fastapi import APIRouter
from api.endpoints import models, tools, system, plugins, enhanced_chat

# Create main API router
api_router = APIRouter()

# Include endpoint routers
api_router.include_router(
    models.router,
    prefix="/models",
    tags=["models"]
)

api_router.include_router(
    enhanced_chat.router,
    prefix="/api/v1",
    tags=["agent"]
)

api_router.include_router(
    tools.router,
    prefix="/tools",
    tags=["tools"]
)

api_router.include_router(
    system.router,
    prefix="/system",
    tags=["system"]
)

api_router.include_router(
    plugins.router,
    prefix="/plugins",
    tags=["plugins"]
)
