"""Unicode Properties from Unicode version 13.0.0 (autogen)."""
from __future__ import annotations

unicode_numeric_type: dict[str, str] = {
    "^decimal": "\x00-\x2f\x3a-\u065f\u066a-\u06ef\u06fa-\u07bf\u07ca-\u0965\u0970-\u09e5\u09f0-\u0a65\u0a70-\u0ae5\u0af0-\u0b65\u0b70-\u0be5\u0bf0-\u0c65\u0c70-\u0ce5\u0cf0-\u0d65\u0d70-\u0de5\u0df0-\u0e4f\u0e5a-\u0ecf\u0eda-\u0f1f\u0f2a-\u103f\u104a-\u108f\u109a-\u17df\u17ea-\u180f\u181a-\u1945\u1950-\u19cf\u19da-\u1a7f\u1a8a-\u1a8f\u1a9a-\u1b4f\u1b5a-\u1baf\u1bba-\u1c3f\u1c4a-\u1c4f\u1c5a-\ua61f\ua62a-\ua8cf\ua8da-\ua8ff\ua90a-\ua9cf\ua9da-\ua9ef\ua9fa-\uaa4f\uaa5a-\uabef\uabfa-\uff0f\uff1a-\U0001049f\U000104aa-\U00010d2f\U00010d3a-\U00011065\U00011070-\U000110ef\U000110fa-\U00011135\U00011140-\U000111cf\U000111da-\U000112ef\U000112fa-\U0001144f\U0001145a-\U000114cf\U000114da-\U0001164f\U0001165a-\U000116bf\U000116ca-\U0001172f\U0001173a-\U000118df\U000118ea-\U0001194f\U0001195a-\U00011c4f\U00011c5a-\U00011d4f\U00011d5a-\U00011d9f\U00011daa-\U00016a5f\U00016a6a-\U00016b4f\U00016b5a-\U0001d7cd\U0001d800-\U0001e13f\U0001e14a-\U0001e2ef\U0001e2fa-\U0001e94f\U0001e95a-\U0001fbef\U0001fbfa-\U0010ffff",
    "^digit": "\x00-\xb1\xb4-\xb8\xba-\u1368\u1372-\u19d9\u19db-\u206f\u2071-\u2073\u207a-\u207f\u208a-\u245f\u2469-\u2473\u247d-\u2487\u2491-\u24e9\u24eb-\u24f4\u24fe\u2500-\u2775\u277f\u2789\u2793-\U00010a3f\U00010a44-\U00010e5f\U00010e69-\U00011051\U0001105b-\U0001f0ff\U0001f10b-\U0010ffff",
    "^none": "\x30-\x39\xb2-\xb3\xb9\xbc-\xbe\u0660-\u0669\u06f0-\u06f9\u07c0-\u07c9\u0966-\u096f\u09e6-\u09ef\u09f4-\u09f9\u0a66-\u0a6f\u0ae6-\u0aef\u0b66-\u0b6f\u0b72-\u0b77\u0be6-\u0bf2\u0c66-\u0c6f\u0c78-\u0c7e\u0ce6-\u0cef\u0d58-\u0d5e\u0d66-\u0d78\u0de6-\u0def\u0e50-\u0e59\u0ed0-\u0ed9\u0f20-\u0f33\u1040-\u1049\u1090-\u1099\u1369-\u137c\u16ee-\u16f0\u17e0-\u17e9\u17f0-\u17f9\u1810-\u1819\u1946-\u194f\u19d0-\u19da\u1a80-\u1a89\u1a90-\u1a99\u1b50-\u1b59\u1bb0-\u1bb9\u1c40-\u1c49\u1c50-\u1c59\u2070\u2074-\u2079\u2080-\u2089\u2150-\u2182\u2185-\u2189\u2460-\u249b\u24ea-\u24ff\u2776-\u2793\u2cfd\u3007\u3021-\u3029\u3038-\u303a\u3192-\u3195\u3220-\u3229\u3248-\u324f\u3251-\u325f\u3280-\u3289\u32b1-\u32bf\u3405\u3483\u382a\u3b4d\u4e00\u4e03\u4e07\u4e09\u4e5d\u4e8c\u4e94\u4e96\u4ebf-\u4ec0\u4edf\u4ee8\u4f0d\u4f70\u5104\u5146\u5169\u516b\u516d\u5341\u5343-\u5345\u534c\u53c1-\u53c4\u56db\u58f1\u58f9\u5e7a\u5efe-\u5eff\u5f0c-\u5f0e\u5f10\u62fe\u634c\u67d2\u6f06\u7396\u767e\u8086\u842c\u8cae\u8cb3\u8d30\u9621\u9646\u964c\u9678\u96f6\ua620-\ua629\ua6e6-\ua6ef\ua830-\ua835\ua8d0-\ua8d9\ua900-\ua909\ua9d0-\ua9d9\ua9f0-\ua9f9\uaa50-\uaa59\uabf0-\uabf9\uf96b\uf973\uf978\uf9b2\uf9d1\uf9d3\uf9fd\uff10-\uff19\U00010107-\U00010133\U00010140-\U00010178\U0001018a-\U0001018b\U000102e1-\U000102fb\U00010320-\U00010323\U00010341\U0001034a\U000103d1-\U000103d5\U000104a0-\U000104a9\U00010858-\U0001085f\U00010879-\U0001087f\U000108a7-\U000108af\U000108fb-\U000108ff\U00010916-\U0001091b\U000109bc-\U000109bd\U000109c0-\U000109cf\U000109d2-\U000109ff\U00010a40-\U00010a48\U00010a7d-\U00010a7e\U00010a9d-\U00010a9f\U00010aeb-\U00010aef\U00010b58-\U00010b5f\U00010b78-\U00010b7f\U00010ba9-\U00010baf\U00010cfa-\U00010cff\U00010d30-\U00010d39\U00010e60-\U00010e7e\U00010f1d-\U00010f26\U00010f51-\U00010f54\U00010fc5-\U00010fcb\U00011052-\U0001106f\U000110f0-\U000110f9\U00011136-\U0001113f\U000111d0-\U000111d9\U000111e1-\U000111f4\U000112f0-\U000112f9\U00011450-\U00011459\U000114d0-\U000114d9\U00011650-\U00011659\U000116c0-\U000116c9\U00011730-\U0001173b\U000118e0-\U000118f2\U00011950-\U00011959\U00011c50-\U00011c6c\U00011d50-\U00011d59\U00011da0-\U00011da9\U00011fc0-\U00011fd4\U00012400-\U0001246e\U00016a60-\U00016a69\U00016b50-\U00016b59\U00016b5b-\U00016b61\U00016e80-\U00016e96\U0001d2e0-\U0001d2f3\U0001d360-\U0001d378\U0001d7ce-\U0001d7ff\U0001e140-\U0001e149\U0001e2f0-\U0001e2f9\U0001e8c7-\U0001e8cf\U0001e950-\U0001e959\U0001ec71-\U0001ecab\U0001ecad-\U0001ecaf\U0001ecb1-\U0001ecb4\U0001ed01-\U0001ed2d\U0001ed2f-\U0001ed3d\U0001f100-\U0001f10c\U0001fbf0-\U0001fbf9\U00020001\U00020064\U000200e2\U00020121\U0002092a\U00020983\U0002098c\U0002099c\U00020aea\U00020afd\U00020b19\U00022390\U00022998\U00023b1b\U0002626d\U0002f890",
    "^numeric": "\x00-\xbb\xbf-\u09f3\u09fa-\u0b71\u0b78-\u0bef\u0bf3-\u0c77\u0c7f-\u0d57\u0d5f-\u0d6f\u0d79-\u0f29\u0f34-\u1371\u137d-\u16ed\u16f1-\u17ef\u17fa-\u214f\u2183-\u2184\u218a-\u2468\u2474-\u247c\u2488-\u2490\u249c-\u24ea\u24f5-\u24fd\u24ff-\u277e\u2780-\u2788\u278a-\u2792\u2794-\u2cfc\u2cfe-\u3006\u3008-\u3020\u302a-\u3037\u303b-\u3191\u3196-\u321f\u322a-\u3247\u3250\u3260-\u327f\u328a-\u32b0\u32c0-\u3404\u3406-\u3482\u3484-\u3829\u382b-\u3b4c\u3b4e-\u4dff\u4e01-\u4e02\u4e04-\u4e06\u4e08\u4e0a-\u4e5c\u4e5e-\u4e8b\u4e8d-\u4e93\u4e95\u4e97-\u4ebe\u4ec1-\u4ede\u4ee0-\u4ee7\u4ee9-\u4f0c\u4f0e-\u4f6f\u4f71-\u5103\u5105-\u5145\u5147-\u5168\u516a\u516c\u516e-\u5340\u5342\u5346-\u534b\u534d-\u53c0\u53c5-\u56da\u56dc-\u58f0\u58f2-\u58f8\u58fa-\u5e79\u5e7b-\u5efd\u5f00-\u5f0b\u5f0f\u5f11-\u62fd\u62ff-\u634b\u634d-\u67d1\u67d3-\u6f05\u6f07-\u7395\u7397-\u767d\u767f-\u8085\u8087-\u842b\u842d-\u8cad\u8caf-\u8cb2\u8cb4-\u8d2f\u8d31-\u9620\u9622-\u9645\u9647-\u964b\u964d-\u9677\u9679-\u96f5\u96f7-\ua6e5\ua6f0-\ua82f\ua836-\uf96a\uf96c-\uf972\uf974-\uf977\uf979-\uf9b1\uf9b3-\uf9d0\uf9d2\uf9d4-\uf9fc\uf9fe-\U00010106\U00010134-\U0001013f\U00010179-\U00010189\U0001018c-\U000102e0\U000102fc-\U0001031f\U00010324-\U00010340\U00010342-\U00010349\U0001034b-\U000103d0\U000103d6-\U00010857\U00010860-\U00010878\U00010880-\U000108a6\U000108b0-\U000108fa\U00010900-\U00010915\U0001091c-\U000109bb\U000109be-\U000109bf\U000109d0-\U000109d1\U00010a00-\U00010a43\U00010a49-\U00010a7c\U00010a7f-\U00010a9c\U00010aa0-\U00010aea\U00010af0-\U00010b57\U00010b60-\U00010b77\U00010b80-\U00010ba8\U00010bb0-\U00010cf9\U00010d00-\U00010e68\U00010e7f-\U00010f1c\U00010f27-\U00010f50\U00010f55-\U00010fc4\U00010fcc-\U0001105a\U00011066-\U000111e0\U000111f5-\U00011739\U0001173c-\U000118e9\U000118f3-\U00011c59\U00011c6d-\U00011fbf\U00011fd5-\U000123ff\U0001246f-\U00016b5a\U00016b62-\U00016e7f\U00016e97-\U0001d2df\U0001d2f4-\U0001d35f\U0001d379-\U0001e8c6\U0001e8d0-\U0001ec70\U0001ecac\U0001ecb0\U0001ecb5-\U0001ed00\U0001ed2e\U0001ed3e-\U0001f10a\U0001f10d-\U00020000\U00020002-\U00020063\U00020065-\U000200e1\U000200e3-\U00020120\U00020122-\U00020929\U0002092b-\U00020982\U00020984-\U0002098b\U0002098d-\U0002099b\U0002099d-\U00020ae9\U00020aeb-\U00020afc\U00020afe-\U00020b18\U00020b1a-\U0002238f\U00022391-\U00022997\U00022999-\U00023b1a\U00023b1c-\U0002626c\U0002626e-\U0002f88f\U0002f891-\U0010ffff",
    "decimal": "\x30-\x39\u0660-\u0669\u06f0-\u06f9\u07c0-\u07c9\u0966-\u096f\u09e6-\u09ef\u0a66-\u0a6f\u0ae6-\u0aef\u0b66-\u0b6f\u0be6-\u0bef\u0c66-\u0c6f\u0ce6-\u0cef\u0d66-\u0d6f\u0de6-\u0def\u0e50-\u0e59\u0ed0-\u0ed9\u0f20-\u0f29\u1040-\u1049\u1090-\u1099\u17e0-\u17e9\u1810-\u1819\u1946-\u194f\u19d0-\u19d9\u1a80-\u1a89\u1a90-\u1a99\u1b50-\u1b59\u1bb0-\u1bb9\u1c40-\u1c49\u1c50-\u1c59\ua620-\ua629\ua8d0-\ua8d9\ua900-\ua909\ua9d0-\ua9d9\ua9f0-\ua9f9\uaa50-\uaa59\uabf0-\uabf9\uff10-\uff19\U000104a0-\U000104a9\U00010d30-\U00010d39\U00011066-\U0001106f\U000110f0-\U000110f9\U00011136-\U0001113f\U000111d0-\U000111d9\U000112f0-\U000112f9\U00011450-\U00011459\U000114d0-\U000114d9\U00011650-\U00011659\U000116c0-\U000116c9\U00011730-\U00011739\U000118e0-\U000118e9\U00011950-\U00011959\U00011c50-\U00011c59\U00011d50-\U00011d59\U00011da0-\U00011da9\U00016a60-\U00016a69\U00016b50-\U00016b59\U0001d7ce-\U0001d7ff\U0001e140-\U0001e149\U0001e2f0-\U0001e2f9\U0001e950-\U0001e959\U0001fbf0-\U0001fbf9",
    "digit": "\xb2-\xb3\xb9\u1369-\u1371\u19da\u2070\u2074-\u2079\u2080-\u2089\u2460-\u2468\u2474-\u247c\u2488-\u2490\u24ea\u24f5-\u24fd\u24ff\u2776-\u277e\u2780-\u2788\u278a-\u2792\U00010a40-\U00010a43\U00010e60-\U00010e68\U00011052-\U0001105a\U0001f100-\U0001f10a",
    "none": "\x00-\x2f\x3a-\xb1\xb4-\xb8\xba-\xbb\xbf-\u065f\u066a-\u06ef\u06fa-\u07bf\u07ca-\u0965\u0970-\u09e5\u09f0-\u09f3\u09fa-\u0a65\u0a70-\u0ae5\u0af0-\u0b65\u0b70-\u0b71\u0b78-\u0be5\u0bf3-\u0c65\u0c70-\u0c77\u0c7f-\u0ce5\u0cf0-\u0d57\u0d5f-\u0d65\u0d79-\u0de5\u0df0-\u0e4f\u0e5a-\u0ecf\u0eda-\u0f1f\u0f34-\u103f\u104a-\u108f\u109a-\u1368\u137d-\u16ed\u16f1-\u17df\u17ea-\u17ef\u17fa-\u180f\u181a-\u1945\u1950-\u19cf\u19db-\u1a7f\u1a8a-\u1a8f\u1a9a-\u1b4f\u1b5a-\u1baf\u1bba-\u1c3f\u1c4a-\u1c4f\u1c5a-\u206f\u2071-\u2073\u207a-\u207f\u208a-\u214f\u2183-\u2184\u218a-\u245f\u249c-\u24e9\u2500-\u2775\u2794-\u2cfc\u2cfe-\u3006\u3008-\u3020\u302a-\u3037\u303b-\u3191\u3196-\u321f\u322a-\u3247\u3250\u3260-\u327f\u328a-\u32b0\u32c0-\u3404\u3406-\u3482\u3484-\u3829\u382b-\u3b4c\u3b4e-\u4dff\u4e01-\u4e02\u4e04-\u4e06\u4e08\u4e0a-\u4e5c\u4e5e-\u4e8b\u4e8d-\u4e93\u4e95\u4e97-\u4ebe\u4ec1-\u4ede\u4ee0-\u4ee7\u4ee9-\u4f0c\u4f0e-\u4f6f\u4f71-\u5103\u5105-\u5145\u5147-\u5168\u516a\u516c\u516e-\u5340\u5342\u5346-\u534b\u534d-\u53c0\u53c5-\u56da\u56dc-\u58f0\u58f2-\u58f8\u58fa-\u5e79\u5e7b-\u5efd\u5f00-\u5f0b\u5f0f\u5f11-\u62fd\u62ff-\u634b\u634d-\u67d1\u67d3-\u6f05\u6f07-\u7395\u7397-\u767d\u767f-\u8085\u8087-\u842b\u842d-\u8cad\u8caf-\u8cb2\u8cb4-\u8d2f\u8d31-\u9620\u9622-\u9645\u9647-\u964b\u964d-\u9677\u9679-\u96f5\u96f7-\ua61f\ua62a-\ua6e5\ua6f0-\ua82f\ua836-\ua8cf\ua8da-\ua8ff\ua90a-\ua9cf\ua9da-\ua9ef\ua9fa-\uaa4f\uaa5a-\uabef\uabfa-\uf96a\uf96c-\uf972\uf974-\uf977\uf979-\uf9b1\uf9b3-\uf9d0\uf9d2\uf9d4-\uf9fc\uf9fe-\uff0f\uff1a-\U00010106\U00010134-\U0001013f\U00010179-\U00010189\U0001018c-\U000102e0\U000102fc-\U0001031f\U00010324-\U00010340\U00010342-\U00010349\U0001034b-\U000103d0\U000103d6-\U0001049f\U000104aa-\U00010857\U00010860-\U00010878\U00010880-\U000108a6\U000108b0-\U000108fa\U00010900-\U00010915\U0001091c-\U000109bb\U000109be-\U000109bf\U000109d0-\U000109d1\U00010a00-\U00010a3f\U00010a49-\U00010a7c\U00010a7f-\U00010a9c\U00010aa0-\U00010aea\U00010af0-\U00010b57\U00010b60-\U00010b77\U00010b80-\U00010ba8\U00010bb0-\U00010cf9\U00010d00-\U00010d2f\U00010d3a-\U00010e5f\U00010e7f-\U00010f1c\U00010f27-\U00010f50\U00010f55-\U00010fc4\U00010fcc-\U00011051\U00011070-\U000110ef\U000110fa-\U00011135\U00011140-\U000111cf\U000111da-\U000111e0\U000111f5-\U000112ef\U000112fa-\U0001144f\U0001145a-\U000114cf\U000114da-\U0001164f\U0001165a-\U000116bf\U000116ca-\U0001172f\U0001173c-\U000118df\U000118f3-\U0001194f\U0001195a-\U00011c4f\U00011c6d-\U00011d4f\U00011d5a-\U00011d9f\U00011daa-\U00011fbf\U00011fd5-\U000123ff\U0001246f-\U00016a5f\U00016a6a-\U00016b4f\U00016b5a\U00016b62-\U00016e7f\U00016e97-\U0001d2df\U0001d2f4-\U0001d35f\U0001d379-\U0001d7cd\U0001d800-\U0001e13f\U0001e14a-\U0001e2ef\U0001e2fa-\U0001e8c6\U0001e8d0-\U0001e94f\U0001e95a-\U0001ec70\U0001ecac\U0001ecb0\U0001ecb5-\U0001ed00\U0001ed2e\U0001ed3e-\U0001f0ff\U0001f10d-\U0001fbef\U0001fbfa-\U00020000\U00020002-\U00020063\U00020065-\U000200e1\U000200e3-\U00020120\U00020122-\U00020929\U0002092b-\U00020982\U00020984-\U0002098b\U0002098d-\U0002099b\U0002099d-\U00020ae9\U00020aeb-\U00020afc\U00020afe-\U00020b18\U00020b1a-\U0002238f\U00022391-\U00022997\U00022999-\U00023b1a\U00023b1c-\U0002626c\U0002626e-\U0002f88f\U0002f891-\U0010ffff",
    "numeric": "\xbc-\xbe\u09f4-\u09f9\u0b72-\u0b77\u0bf0-\u0bf2\u0c78-\u0c7e\u0d58-\u0d5e\u0d70-\u0d78\u0f2a-\u0f33\u1372-\u137c\u16ee-\u16f0\u17f0-\u17f9\u2150-\u2182\u2185-\u2189\u2469-\u2473\u247d-\u2487\u2491-\u249b\u24eb-\u24f4\u24fe\u277f\u2789\u2793\u2cfd\u3007\u3021-\u3029\u3038-\u303a\u3192-\u3195\u3220-\u3229\u3248-\u324f\u3251-\u325f\u3280-\u3289\u32b1-\u32bf\u3405\u3483\u382a\u3b4d\u4e00\u4e03\u4e07\u4e09\u4e5d\u4e8c\u4e94\u4e96\u4ebf-\u4ec0\u4edf\u4ee8\u4f0d\u4f70\u5104\u5146\u5169\u516b\u516d\u5341\u5343-\u5345\u534c\u53c1-\u53c4\u56db\u58f1\u58f9\u5e7a\u5efe-\u5eff\u5f0c-\u5f0e\u5f10\u62fe\u634c\u67d2\u6f06\u7396\u767e\u8086\u842c\u8cae\u8cb3\u8d30\u9621\u9646\u964c\u9678\u96f6\ua6e6-\ua6ef\ua830-\ua835\uf96b\uf973\uf978\uf9b2\uf9d1\uf9d3\uf9fd\U00010107-\U00010133\U00010140-\U00010178\U0001018a-\U0001018b\U000102e1-\U000102fb\U00010320-\U00010323\U00010341\U0001034a\U000103d1-\U000103d5\U00010858-\U0001085f\U00010879-\U0001087f\U000108a7-\U000108af\U000108fb-\U000108ff\U00010916-\U0001091b\U000109bc-\U000109bd\U000109c0-\U000109cf\U000109d2-\U000109ff\U00010a44-\U00010a48\U00010a7d-\U00010a7e\U00010a9d-\U00010a9f\U00010aeb-\U00010aef\U00010b58-\U00010b5f\U00010b78-\U00010b7f\U00010ba9-\U00010baf\U00010cfa-\U00010cff\U00010e69-\U00010e7e\U00010f1d-\U00010f26\U00010f51-\U00010f54\U00010fc5-\U00010fcb\U0001105b-\U00011065\U000111e1-\U000111f4\U0001173a-\U0001173b\U000118ea-\U000118f2\U00011c5a-\U00011c6c\U00011fc0-\U00011fd4\U00012400-\U0001246e\U00016b5b-\U00016b61\U00016e80-\U00016e96\U0001d2e0-\U0001d2f3\U0001d360-\U0001d378\U0001e8c7-\U0001e8cf\U0001ec71-\U0001ecab\U0001ecad-\U0001ecaf\U0001ecb1-\U0001ecb4\U0001ed01-\U0001ed2d\U0001ed2f-\U0001ed3d\U0001f10b-\U0001f10c\U00020001\U00020064\U000200e2\U00020121\U0002092a\U00020983\U0002098c\U0002099c\U00020aea\U00020afd\U00020b19\U00022390\U00022998\U00023b1b\U0002626d\U0002f890"
}
ascii_numeric_type: dict[str, str] = {
    "^decimal": "\x00-\x2f\x3a-\U0010ffff",
    "^digit": "\x00-\U0010ffff",
    "^none": "\x30-\x39",
    "^numeric": "\x00-\U0010ffff",
    "decimal": "\x30-\x39",
    "digit": "",
    "none": "\x00-\x2f\x3a-\U0010ffff",
    "numeric": ""
}
