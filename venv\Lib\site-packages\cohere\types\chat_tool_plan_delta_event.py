# This file was auto-generated by Fern from our API Definition.

from .chat_stream_event_type import ChatStreamEventType
import typing
from .chat_tool_plan_delta_event_delta import ChatToolPlanDeltaEventDelta
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class ChatToolPlanDeltaEvent(ChatStreamEventType):
    """
    A streamed event which contains a delta of tool plan text.
    """

    delta: typing.Optional[ChatToolPlanDeltaEventDelta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
