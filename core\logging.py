"""
Enhanced Logging System for Reverie Code Studio Server
Beautiful, structured logging with emoji indicators and visual hierarchy
"""

import sys
from pathlib import Path
from loguru import logger
from core.config import settings


# Emoji mappings for different log levels and operations
LOG_EMOJIS = {
    "TRACE": "🔍",
    "DEBUG": "🐛",
    "INFO": "ℹ️",
    "SUCCESS": "✅",
    "WARNING": "⚠️",
    "ERROR": "❌",
    "CRITICAL": "🚨"
}

OPERATION_EMOJIS = {
    "startup": "🚀",
    "shutdown": "🛑",
    "model": "🤖",
    "download": "📥",
    "upload": "📤",
    "api": "🌐",
    "database": "🗄️",
    "cache": "💾",
    "config": "⚙️",
    "security": "🔒",
    "performance": "⚡",
    "network": "🌍",
    "file": "📁",
    "git": "🔄",
    "docker": "🐳",
    "plugin": "🔌",
    "tool": "🔧",
    "agent": "🤖",
    "chat": "💬",
    "search": "🔍",
    "validation": "✔️",
    "processing": "⚙️",
    "complete": "🎉"
}


def get_emoji_for_level(level: str) -> str:
    """Get emoji for log level"""
    return LOG_EMOJIS.get(level.upper(), "📝")


def get_emoji_for_operation(message: str) -> str:
    """Get emoji for operation based on message content"""
    message_lower = message.lower()

    for operation, emoji in OPERATION_EMOJIS.items():
        if operation in message_lower:
            return emoji

    # Default emoji based on common patterns
    if any(word in message_lower for word in ["start", "init", "launch"]):
        return "🚀"
    elif any(word in message_lower for word in ["complete", "success", "done"]):
        return "✅"
    elif any(word in message_lower for word in ["error", "fail", "exception"]):
        return "❌"
    elif any(word in message_lower for word in ["warn", "warning"]):
        return "⚠️"
    elif any(word in message_lower for word in ["load", "download", "fetch"]):
        return "📥"
    elif any(word in message_lower for word in ["save", "write", "store"]):
        return "💾"

    return "📝"


def format_log_message(record):
    """Custom log formatter with beautiful emojis and compact structure"""
    level = record["level"].name
    level_emoji = get_emoji_for_level(level)
    operation_emoji = get_emoji_for_operation(record["message"])

    # Enhanced color mapping for levels (using Loguru color syntax)
    level_colors = {
        "TRACE": "dim",
        "DEBUG": "cyan",
        "INFO": "blue",
        "SUCCESS": "green",
        "WARNING": "yellow",
        "ERROR": "red",
        "CRITICAL": "red bold"
    }

    level_color = level_colors.get(level, "white")

    # Compact format with beautiful separators and proper line ending
    return (
        f"<green>{record['time']:HH:mm:ss}</green> "
        f"{operation_emoji}<{level_color}>{level_emoji}{level:<4}</{level_color}> "
        f"<white>»</white> "
        f"<cyan>{record['name']:<12}</cyan> "
        f"<white>»</white> "
        f"<level>{record['message']}</level>\n"
    )


def setup_logging():
    """Setup enhanced logging configuration with beautiful output"""

    # Remove default handler
    logger.remove()

    # Ensure logs directory exists
    logs_dir = settings.logs_path
    logs_dir.mkdir(exist_ok=True)

    # Enhanced console handler with beautiful formatting
    logger.add(
        sys.stdout,
        format=format_log_message,
        level=settings.logging.level,
        colorize=True,
        backtrace=False,
        diagnose=False,
        enqueue=True  # Thread-safe logging
    )

    # Structured file handler for machine reading
    log_file = logs_dir / "reverie.log"
    logger.add(
        log_file,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level:<8} | {name}:{function}:{line} | {message}",
        level=settings.logging.level,
        rotation="10 MB",
        retention="7 days",
        compression="zip",
        backtrace=True,
        diagnose=True,
        enqueue=True
    )

    # Separate error log for critical issues
    error_log_file = logs_dir / "errors.log"
    logger.add(
        error_log_file,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level:<8} | {name}:{function}:{line} | {message} | {extra}",
        level="ERROR",
        rotation="5 MB",
        retention="30 days",
        compression="zip",
        backtrace=True,
        diagnose=True,
        enqueue=True
    )

    # Performance log for monitoring
    perf_log_file = logs_dir / "performance.log"
    logger.add(
        perf_log_file,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {message}",
        level="INFO",
        rotation="5 MB",
        retention="3 days",
        filter=lambda record: "performance" in record["message"].lower() or "timing" in record["message"].lower(),
        enqueue=True
    )

    logger.success("🎨 Enhanced logging system initialized")


# Convenience functions for structured logging
def log_operation(operation: str, message: str, level: str = "INFO"):
    """Log an operation with appropriate emoji"""
    emoji = OPERATION_EMOJIS.get(operation, "📝")
    getattr(logger, level.lower())(f"{emoji} {message}")


def log_performance(operation: str, duration: float, details: str = ""):
    """Log performance metrics"""
    logger.info(f"⚡ Performance: {operation} completed in {duration:.2f}s {details}")


def log_api_request(method: str, path: str, status: int, duration: float):
    """Log API request"""
    status_emoji = "✅" if 200 <= status < 300 else "⚠️" if 300 <= status < 400 else "❌"
    logger.info(f"🌐 {method} {path} → {status_emoji} {status} ({duration:.2f}s)")


def log_model_operation(operation: str, model_name: str, details: str = ""):
    """Log model operations"""
    logger.info(f"🤖 Model {operation}: {model_name} {details}")


def log_git_operation(operation: str, details: str):
    """Log Git operations"""
    logger.info(f"🔄 Git {operation}: {details}")


# Export logger and utilities for use in other modules
__all__ = [
    "logger",
    "setup_logging",
    "log_operation",
    "log_performance",
    "log_api_request",
    "log_model_operation",
    "log_git_operation"
]
