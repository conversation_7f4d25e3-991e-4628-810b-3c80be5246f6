# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import pydantic
from .chat_finish_reason import ChatFinishReason
import typing
from .assistant_message_response import AssistantMessageResponse
from .usage import Usage
from .logprob_item import LogprobItem
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ChatResponse(UncheckedBaseModel):
    id: str = pydantic.Field()
    """
    Unique identifier for the generated reply. Useful for submitting feedback.
    """

    finish_reason: ChatFinishReason
    prompt: typing.Optional[str] = pydantic.Field(default=None)
    """
    The prompt that was used. Only present when `return_prompt` in the request is set to true.
    """

    message: AssistantMessageResponse
    usage: typing.Optional[Usage] = None
    logprobs: typing.Optional[typing.List[LogprobItem]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
