"""
Agent API endpoints for autonomous task execution
"""

import asyncio
import uuid
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from enum import Enum

from server.models.manager import ModelManager
from server.tools.manager import Tool<PERSON>anager
from server.core.config import settings
from server.core.logging import logger
from server.core.exceptions import ModelException, ToolException

router = APIRouter()


class TaskStatus(str, Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentTask(BaseModel):
    """Agent task model"""
    id: str
    description: str
    status: TaskStatus
    progress: float = 0.0
    result: Optional[str] = None
    error: Optional[str] = None
    logs: List[str] = []
    created_at: str
    updated_at: str


class AgentRequest(BaseModel):
    """Agent execution request"""
    task: str
    context: Optional[Dict[str, Any]] = None
    max_iterations: Optional[int] = None
    timeout: Optional[int] = None
    tools_enabled: Optional[List[str]] = None


class AgentResponse(BaseModel):
    """Agent response model"""
    task_id: str
    status: TaskStatus
    message: str


# In-memory task storage (in production, use a proper database)
active_tasks: Dict[str, AgentTask] = {}


def get_model_manager():
    """Dependency to get model manager from app state"""
    # This is a placeholder - in the actual implementation,
    # we'll get this from the FastAPI app state
    global _model_manager
    if '_model_manager' not in globals():
        from server.models.manager import ModelManager
        _model_manager = ModelManager()
    return _model_manager


def load_agent_system_prompt() -> str:
    """Load the agent system prompt"""
    try:
        prompt_file = settings.project_root / settings.ai.agent_system_prompt_file
        if prompt_file.exists():
            return prompt_file.read_text(encoding='utf-8')
        else:
            logger.warning(f"Agent system prompt file not found: {prompt_file}")
            return "You are an autonomous AI agent capable of executing complex tasks."
    except Exception as e:
        logger.error(f"Failed to load agent system prompt: {e}")
        return "You are an autonomous AI agent capable of executing complex tasks."


@router.post("/execute", response_model=AgentResponse)
async def execute_task(request: AgentRequest, background_tasks: BackgroundTasks):
    """Execute a task using the AI agent"""
    try:
        model_manager = get_model_manager()
        
        if not model_manager.is_model_loaded():
            raise HTTPException(status_code=503, detail="No model is currently loaded")
        
        # Create task
        task_id = str(uuid.uuid4())
        task = AgentTask(
            id=task_id,
            description=request.task,
            status=TaskStatus.PENDING,
            created_at=asyncio.get_event_loop().time(),
            updated_at=asyncio.get_event_loop().time()
        )
        
        active_tasks[task_id] = task
        
        # Start task execution in background
        background_tasks.add_task(
            execute_agent_task,
            task_id,
            request,
            model_manager
        )
        
        return AgentResponse(
            task_id=task_id,
            status=TaskStatus.PENDING,
            message="Task queued for execution"
        )
        
    except Exception as e:
        logger.error(f"Failed to queue agent task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def execute_agent_task(task_id: str, request: AgentRequest, model_manager: ModelManager):
    """Execute an agent task"""
    task = active_tasks.get(task_id)
    if not task:
        return
    
    try:
        # Update task status
        task.status = TaskStatus.RUNNING
        task.updated_at = asyncio.get_event_loop().time()
        task.logs.append("Task execution started")
        
        # Initialize tool manager
        tool_manager = ToolManager()
        enabled_tools = request.tools_enabled or ["web_search", "file_operations", "command_execution"]
        
        # Load system prompt
        system_prompt = load_agent_system_prompt()
        
        # Add context information
        if request.context:
            context_info = []
            for key, value in request.context.items():
                context_info.append(f"{key}: {value}")
            
            if context_info:
                system_prompt += "\n\n## Context:\n" + "\n".join(context_info)
        
        # Add available tools information
        tools_info = tool_manager.get_tools_description(enabled_tools)
        system_prompt += f"\n\n## Available Tools:\n{tools_info}"
        
        # Execute task with iterations
        max_iterations = request.max_iterations or settings.ai.agent_max_iterations
        conversation_history = [
            f"System: {system_prompt}",
            f"User: {request.task}",
            "Assistant: I'll help you with this task. Let me break it down and execute it step by step."
        ]
        
        for iteration in range(max_iterations):
            task.progress = (iteration / max_iterations) * 100
            task.logs.append(f"Iteration {iteration + 1}/{max_iterations}")
            
            # Generate response
            prompt = "\n\n".join(conversation_history) + "\n\nAssistant:"
            
            response_text = ""
            async for chunk in model_manager.generate_text(
                prompt=prompt,
                max_tokens=settings.models.max_tokens,
                temperature=settings.models.temperature,
                top_p=settings.models.top_p,
                stream=False
            ):
                response_text += chunk
            
            conversation_history.append(f"Assistant: {response_text}")
            
            # Check if task is complete
            if "task completed" in response_text.lower() or "finished" in response_text.lower():
                task.status = TaskStatus.COMPLETED
                task.result = response_text
                task.progress = 100.0
                task.logs.append("Task completed successfully")
                break
            
            # Extract and execute tool calls if any
            # This is a simplified implementation - in practice, you'd parse the response
            # for specific tool call patterns and execute them
            
            # Update task
            task.updated_at = asyncio.get_event_loop().time()
        
        # If we've exhausted iterations without completion
        if task.status == TaskStatus.RUNNING:
            task.status = TaskStatus.COMPLETED
            task.result = conversation_history[-1].replace("Assistant: ", "")
            task.progress = 100.0
            task.logs.append("Task completed after maximum iterations")
        
    except Exception as e:
        logger.error(f"Agent task execution failed: {e}")
        task.status = TaskStatus.FAILED
        task.error = str(e)
        task.logs.append(f"Task failed: {e}")
    
    finally:
        task.updated_at = asyncio.get_event_loop().time()


@router.get("/tasks/{task_id}", response_model=AgentTask)
async def get_task_status(task_id: str):
    """Get the status of a specific task"""
    task = active_tasks.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return task


@router.get("/tasks", response_model=List[AgentTask])
async def list_tasks():
    """List all tasks"""
    return list(active_tasks.values())


@router.delete("/tasks/{task_id}")
async def cancel_task(task_id: str):
    """Cancel a running task"""
    task = active_tasks.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    if task.status == TaskStatus.RUNNING:
        task.status = TaskStatus.CANCELLED
        task.updated_at = asyncio.get_event_loop().time()
        task.logs.append("Task cancelled by user")
    
    return {"message": "Task cancelled"}


@router.get("/tasks/{task_id}/stream")
async def stream_task_logs(task_id: str):
    """Stream task logs in real-time"""
    task = active_tasks.get(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    async def generate():
        last_log_count = 0
        
        while task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            # Send new logs
            if len(task.logs) > last_log_count:
                for log in task.logs[last_log_count:]:
                    yield f"data: {log}\n\n"
                last_log_count = len(task.logs)
            
            # Send progress update
            yield f"data: [PROGRESS] {task.progress:.1f}%\n\n"
            
            await asyncio.sleep(1)
        
        # Send final status
        yield f"data: [STATUS] {task.status.value}\n\n"
        if task.result:
            yield f"data: [RESULT] {task.result}\n\n"
        if task.error:
            yield f"data: [ERROR] {task.error}\n\n"
        
        yield "data: [DONE]\n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )
