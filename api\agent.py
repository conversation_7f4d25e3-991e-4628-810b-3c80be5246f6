"""
Agent API endpoints for Rilance Code Studio
Provides REST API for agent task management and execution
"""

from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field
from datetime import datetime

from server.agent.engine import AgentEngine
from server.agent.types import AgentTask, TaskStatus
from server.agent.memory import MemoryType, MemoryImportance
from server.models.manager import ModelManager
from server.core.logging import logger
from server.api.websocket import (
    broadcast_task_created, broadcast_task_updated,
    broadcast_step_started, broadcast_step_completed,
    broadcast_tool_called
)

router = APIRouter(prefix="/api/v1/agent", tags=["agent"])

# Global agent engine instance
agent_engine: Optional[AgentEngine] = None


def get_agent_engine() -> AgentEngine:
    """Get agent engine instance"""
    global agent_engine
    if agent_engine is None:
        model_manager = ModelManager()
        agent_engine = AgentEngine(model_manager)

        # Register WebSocket event callbacks
        agent_engine.register_event_callback("task_created", broadcast_task_created)
        agent_engine.register_event_callback("task_updated", broadcast_task_updated)
        agent_engine.register_event_callback("step_started", lambda data: broadcast_step_started(data["task_id"], data["step_data"]))
        agent_engine.register_event_callback("step_completed", lambda data: broadcast_step_completed(data["task_id"], data["step_data"]))
        agent_engine.register_event_callback("tool_called", lambda data: broadcast_tool_called(data["task_id"], data["tool_name"], data["result"]))

    return agent_engine


# Request/Response models
class TaskCreateRequest(BaseModel):
    """Task creation request"""
    description: str = Field(..., description="Task description")
    user_id: str = Field(default="default", description="User ID")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Task metadata")


class TaskResponse(BaseModel):
    """Task response"""
    task_id: str
    description: str
    user_id: str
    status: str
    steps: List[Dict[str, Any]]
    created_at: str
    started_at: Optional[str]
    completed_at: Optional[str]
    result: Optional[str]
    error: Optional[str]
    progress: float
    metadata: Dict[str, Any]


class TaskListResponse(BaseModel):
    """Task list response"""
    tasks: List[TaskResponse]
    total: int


class TaskExecuteRequest(BaseModel):
    """Task execution request"""
    task_id: str = Field(..., description="Task ID to execute")


class AgentStatusResponse(BaseModel):
    """Agent status response"""
    active_tasks: int
    queued_tasks: int
    completed_tasks: int
    failed_tasks: int
    memory_size: int
    available_tools: List[str]


class MemorySearchRequest(BaseModel):
    """Memory search request"""
    query: str = Field(..., description="Search query")
    user_id: str = Field(default="default", description="User ID")
    limit: int = Field(default=20, description="Maximum results")


class UserPreferenceRequest(BaseModel):
    """User preference request"""
    user_id: str = Field(default="default", description="User ID")
    preference_key: str = Field(..., description="Preference key")
    preference_value: Any = Field(..., description="Preference value")


class ContextSearchRequest(BaseModel):
    """Context search request"""
    query: str = Field(..., description="Search query")
    context_types: Optional[List[str]] = Field(default=None, description="Context types to search")
    max_items: int = Field(default=20, description="Maximum results")


class ProjectAnalysisRequest(BaseModel):
    """Project analysis request"""
    project_path: str = Field(..., description="Project path to analyze")


class FileContextRequest(BaseModel):
    """File context extraction request"""
    file_path: str = Field(..., description="File path to analyze")
    query: Optional[str] = Field(default=None, description="Optional query for relevance")


@router.post("/tasks", response_model=Dict[str, str])
async def create_task(
    request: TaskCreateRequest,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Create a new agent task"""
    try:
        task_id = await engine.create_task(
            description=request.description,
            user_id=request.user_id,
            metadata=request.metadata
        )
        
        logger.info(f"Created task {task_id} for user {request.user_id}")
        
        return {
            "task_id": task_id,
            "status": "created",
            "message": "Task created successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to create task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/execute", response_model=TaskResponse)
async def execute_task(
    task_id: str,
    background_tasks: BackgroundTasks,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Execute a task"""
    try:
        task = engine.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        if task.status != TaskStatus.PENDING:
            raise HTTPException(
                status_code=400, 
                detail=f"Task is not in pending status: {task.status.value}"
            )
        
        # Execute task in background
        background_tasks.add_task(engine.execute_task, task_id)
        
        # Return updated task
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        
        logger.info(f"Started execution of task {task_id}")
        
        return TaskResponse(**task.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to execute task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks", response_model=TaskListResponse)
async def list_tasks(
    user_id: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """List agent tasks"""
    try:
        tasks = engine.list_tasks(user_id=user_id)
        
        # Filter by status if provided
        if status:
            try:
                status_enum = TaskStatus(status)
                tasks = [t for t in tasks if t.status == status_enum]
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid status: {status}")
        
        # Apply pagination
        total = len(tasks)
        tasks = tasks[offset:offset + limit]
        
        # Convert to response format
        task_responses = [TaskResponse(**task.to_dict()) for task in tasks]
        
        return TaskListResponse(tasks=task_responses, total=total)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list tasks: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: str,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Get a specific task"""
    try:
        task = engine.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return TaskResponse(**task.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/tasks/{task_id}")
async def cancel_task(
    task_id: str,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Cancel a task"""
    try:
        success = engine.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="Task not found")
        
        logger.info(f"Cancelled task {task_id}")
        
        return {"message": "Task cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=AgentStatusResponse)
async def get_agent_status(
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Get agent status"""
    try:
        tasks = engine.list_tasks()
        
        active_tasks = len([t for t in tasks if t.status == TaskStatus.RUNNING])
        queued_tasks = len([t for t in tasks if t.status == TaskStatus.PENDING])
        completed_tasks = len([t for t in tasks if t.status == TaskStatus.COMPLETED])
        failed_tasks = len([t for t in tasks if t.status == TaskStatus.FAILED])
        
        return AgentStatusResponse(
            active_tasks=active_tasks,
            queued_tasks=queued_tasks,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks,
            memory_size=len(engine.agent_memory),
            available_tools=list(engine.tools.keys())
        )
        
    except Exception as e:
        logger.error(f"Failed to get agent status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/create-and-execute", response_model=TaskResponse)
async def create_and_execute_task(
    request: TaskCreateRequest,
    background_tasks: BackgroundTasks,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Create and immediately execute a task"""
    try:
        # Create task
        task_id = await engine.create_task(
            description=request.description,
            user_id=request.user_id,
            metadata=request.metadata
        )
        
        # Get the created task
        task = engine.get_task(task_id)
        if not task:
            raise HTTPException(status_code=500, detail="Failed to retrieve created task")
        
        # Execute task in background
        background_tasks.add_task(engine.execute_task, task_id)
        
        # Update task status
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.now()
        
        logger.info(f"Created and started execution of task {task_id}")
        
        return TaskResponse(**task.to_dict())
        
    except Exception as e:
        logger.error(f"Failed to create and execute task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/memory", response_model=Dict[str, Any])
async def get_agent_memory(
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Get agent memory"""
    try:
        return engine.agent_memory
        
    except Exception as e:
        logger.error(f"Failed to get agent memory: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/memory/clear")
async def clear_agent_memory(
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Clear agent memory"""
    try:
        engine.agent_memory.clear()
        logger.info("Agent memory cleared")
        
        return {"message": "Agent memory cleared successfully"}
        
    except Exception as e:
        logger.error(f"Failed to clear agent memory: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tools", response_model=List[str])
async def list_available_tools(
    engine: AgentEngine = Depends(get_agent_engine)
):
    """List available tools"""
    try:
        return list(engine.tool_manager.list_tools())

    except Exception as e:
        logger.error(f"Failed to list tools: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/memory/{user_id}", response_model=List[Dict[str, Any]])
async def get_user_memory(
    user_id: str,
    memory_type: Optional[str] = None,
    limit: int = 50,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Get user memories"""
    try:
        memory_type_enum = None
        if memory_type:
            try:
                memory_type_enum = MemoryType(memory_type)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid memory type: {memory_type}")

        memories = await engine.get_user_memory(user_id, memory_type_enum, limit)
        return memories

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get user memory: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/memory/search", response_model=List[Dict[str, Any]])
async def search_user_memory(
    request: MemorySearchRequest,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Search user memories"""
    try:
        memories = await engine.search_user_memory(
            user_id=request.user_id,
            query=request.query,
            limit=request.limit
        )
        return memories

    except Exception as e:
        logger.error(f"Failed to search user memory: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/memory/preference")
async def store_user_preference(
    request: UserPreferenceRequest,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Store user preference"""
    try:
        await engine.store_user_preference(
            user_id=request.user_id,
            preference_key=request.preference_key,
            preference_value=request.preference_value
        )

        return {"message": "Preference stored successfully"}

    except Exception as e:
        logger.error(f"Failed to store user preference: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/profile/{user_id}", response_model=Dict[str, Any])
async def get_user_profile(
    user_id: str,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Get user profile"""
    try:
        profile = await engine.get_user_profile(user_id)
        return profile

    except Exception as e:
        logger.error(f"Failed to get user profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/memory/cleanup")
async def cleanup_memory(
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Clean up expired memories"""
    try:
        await engine.cleanup_memory()
        return {"message": "Memory cleanup completed"}

    except Exception as e:
        logger.error(f"Failed to cleanup memory: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/context/search", response_model=List[Dict[str, Any]])
async def search_context(
    request: ContextSearchRequest,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Search context with query"""
    try:
        context_items = await engine.search_context(
            query=request.query,
            context_types=request.context_types,
            max_items=request.max_items
        )
        return context_items

    except Exception as e:
        logger.error(f"Failed to search context: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/context/analyze-project", response_model=Dict[str, Any])
async def analyze_project_context(
    request: ProjectAnalysisRequest,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Analyze project for context"""
    try:
        analysis = await engine.analyze_project_context(request.project_path)
        return analysis

    except Exception as e:
        logger.error(f"Failed to analyze project context: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/context/extract-file", response_model=List[Dict[str, Any]])
async def extract_file_context(
    request: FileContextRequest,
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Extract context from file"""
    try:
        context_items = await engine.extract_file_context(
            file_path=request.file_path,
            query=request.query
        )
        return context_items

    except Exception as e:
        logger.error(f"Failed to extract file context: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/context/summary", response_model=Dict[str, Any])
async def get_context_summary(
    engine: AgentEngine = Depends(get_agent_engine)
):
    """Get context summary"""
    try:
        summary = await engine.get_context_summary()
        return summary

    except Exception as e:
        logger.error(f"Failed to get context summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))
