"""
Enhanced Tool Manager for Reverie Code Studio Agent
Comprehensive tool system with Augment-equivalent capabilities
Manages and executes various tools for the AI agent including web search, file operations,
command execution, code analysis, and project management
"""

import asyncio
import subprocess
import os
import sys
import shutil
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import aiohttp
from bs4 import BeautifulSoup
import json
import git
from duckduckgo_search import DDGS
import ast
import re
import mimetypes
from datetime import datetime

from core.config import settings
from core.logging import logger
from core.exceptions import ToolException


class ToolManager:
    """Enhanced tool manager with comprehensive Augment-equivalent capabilities"""

    def __init__(self):
        self.tools = {
            # Core file operations
            "file_read": self.file_read,
            "file_write": self.file_write,
            "file_list": self.file_list,
            "create_file": self.create_file,
            "delete_file": self.delete_file,
            "move_file": self.move_file,
            "copy_file": self.copy_file,

            # Search and analysis
            "web_search": self.web_search,
            "find_in_files": self.find_in_files,
            "code_analyze": self.code_analyze,
            "project_analyze": self.project_analyze,
            "file_analyze": self.file_analyze,
            "dependency_analysis": self.dependency_analysis,

            # Command execution
            "command_execute": self.command_execute,
            "terminal_execute": self.terminal_execute,
            "batch_execute": self.batch_execute,

            # Git operations
            "git_operations": self.git_operations,
            "git_status": self.git_status,
            "git_diff": self.git_diff,

            # System information
            "environment_info": self.environment_info,
            "system_info": self.system_info,
            "process_info": self.process_info,
            "network_info": self.network_info,
            "disk_usage": self.disk_usage,
            "file_permissions": self.file_permissions,

            # Project management
            "create_project_structure": self.create_project_structure,
            "generate_documentation": self.generate_documentation,

            # Task management
            "task_tracker_create": self.task_tracker_create,
            "task_tracker_list": self.task_tracker_list,
            "task_tracker_update": self.task_tracker_update,

            # Code operations
            "code_format": self.code_format,
            "text_replace": self.text_replace,
            "directory_tree": self.directory_tree,
        }

        # Execution tracking
        self.command_history = []
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "tools_used": {}
        }
        self.max_history = 100

        # Security settings
        self.allowed_extensions = {'.py', '.js', '.ts', '.html', '.css', '.json', '.yaml', '.yml',
                                 '.md', '.txt', '.xml', '.sql', '.sh', '.bat', '.ps1', '.cfg', '.ini'}
        self.blocked_paths = {'/etc/passwd', '/etc/shadow', 'C:\\Windows\\System32'}

        logger.info(f"Enhanced ToolManager initialized with {len(self.tools)} tools")
    
    def _validate_path(self, path: str) -> bool:
        """Validate file path for security"""
        try:
            resolved_path = Path(path).resolve()

            # Check for blocked paths
            for blocked in self.blocked_paths:
                if str(resolved_path).startswith(blocked):
                    return False

            # Check for path traversal attempts
            if '..' in str(resolved_path) or str(resolved_path).startswith('/'):
                # Allow absolute paths but be cautious
                pass

            return True
        except Exception:
            return False

    def _validate_file_extension(self, path: str) -> bool:
        """Validate file extension"""
        ext = Path(path).suffix.lower()
        return ext in self.allowed_extensions or ext == ''

    def _log_execution(self, tool_name: str, success: bool, execution_time: float = 0):
        """Log tool execution for monitoring"""
        self.execution_stats["total_executions"] += 1
        if success:
            self.execution_stats["successful_executions"] += 1
        else:
            self.execution_stats["failed_executions"] += 1

        if tool_name not in self.execution_stats["tools_used"]:
            self.execution_stats["tools_used"][tool_name] = 0
        self.execution_stats["tools_used"][tool_name] += 1

    def get_tools_description(self, enabled_tools: List[str] = None) -> str:
        """Get comprehensive description of available tools"""

        if enabled_tools is None:
            enabled_tools = list(self.tools.keys())

        descriptions = {
            # File operations
            "file_read": "file_read(path: str) - Read contents of a file",
            "file_write": "file_write(path: str, content: str) - Write content to a file",
            "file_list": "file_list(path: str) - List files and directories",
            "create_file": "create_file(path: str, content: str) - Create a new file",
            "delete_file": "delete_file(path: str, backup: bool) - Delete a file with optional backup",
            "move_file": "move_file(source: str, destination: str) - Move or rename a file",
            "copy_file": "copy_file(source: str, destination: str) - Copy a file",

            # Search and analysis
            "web_search": "web_search(query: str) - Search the web for information",
            "find_in_files": "find_in_files(pattern: str, path: str, case_sensitive: bool) - Search for text in files",
            "code_analyze": "code_analyze(code: str, language: str) - Analyze code structure and patterns",
            "project_analyze": "project_analyze(path: str) - Analyze project structure and dependencies",
            "file_analyze": "file_analyze(path: str) - Analyze individual file properties",

            # Command execution
            "command_execute": "command_execute(command: str, timeout: int) - Execute a shell command",
            "terminal_execute": "terminal_execute(command: str, interactive: bool) - Execute command with terminal support",
            "batch_execute": "batch_execute(commands: List[str], stop_on_error: bool) - Execute multiple commands",

            # Git operations
            "git_operations": "git_operations(operation: str, **kwargs) - Perform Git operations",
            "git_status": "git_status(path: str) - Get Git repository status",
            "git_diff": "git_diff(path: str, staged: bool) - Get Git diff information",

            # System information
            "environment_info": "environment_info() - Get current environment information",
            "system_info": "system_info() - Get detailed system information",
            "process_info": "process_info(name: str) - Get information about running processes",

            # Advanced operations
            "text_replace": "text_replace(path: str, old_text: str, new_text: str) - Replace text in file",
            "regex_search": "regex_search(pattern: str, path: str) - Search using regular expressions",
            "directory_tree": "directory_tree(path: str, max_depth: int) - Generate directory tree",
            "file_permissions": "file_permissions(path: str, mode: str) - Manage file permissions",
            "disk_usage": "disk_usage(path: str) - Get disk usage information"
        }

        result = []
        for tool_name in enabled_tools:
            if tool_name in descriptions:
                result.append(descriptions[tool_name])

        return "\n".join(result)
    
    async def execute_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """Execute a tool with comprehensive error handling and logging"""

        if tool_name not in self.tools:
            self._log_execution(tool_name, False)
            raise ToolException(f"Unknown tool: {tool_name}")

        start_time = time.time()

        try:
            logger.info(f"🔧 Executing tool: {tool_name} with args: {kwargs}")

            # Validate inputs for file operations
            if tool_name in ["file_read", "file_write", "create_file", "delete_file", "move_file", "copy_file"]:
                path = kwargs.get("path") or kwargs.get("source")
                if path and not self._validate_path(path):
                    raise ToolException(f"Invalid or blocked path: {path}")

                if tool_name in ["file_write", "create_file"] and path:
                    if not self._validate_file_extension(path):
                        logger.warning(f"Potentially unsafe file extension: {path}")

            # Record command in history
            self.command_history.append({
                "tool": tool_name,
                "args": kwargs,
                "timestamp": datetime.now().isoformat(),
                "execution_time": None,
                "success": None
            })

            # Keep history size manageable
            if len(self.command_history) > self.max_history:
                self.command_history = self.command_history[-self.max_history:]

            # Execute the tool
            result = await self.tools[tool_name](**kwargs)

            execution_time = time.time() - start_time

            # Update history with results
            if self.command_history:
                self.command_history[-1]["execution_time"] = execution_time
                self.command_history[-1]["success"] = True

            self._log_execution(tool_name, True, execution_time)
            logger.info(f"✅ Tool {tool_name} executed successfully in {execution_time:.2f}s")

            return {
                "success": True,
                "result": result,
                "tool": tool_name,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            execution_time = time.time() - start_time

            # Update history with error
            if self.command_history:
                self.command_history[-1]["execution_time"] = execution_time
                self.command_history[-1]["success"] = False
                self.command_history[-1]["error"] = str(e)

            self._log_execution(tool_name, False, execution_time)
            logger.error(f"❌ Tool execution failed: {tool_name} - {e}")

            return {
                "success": False,
                "error": str(e),
                "tool": tool_name,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat()
            }
    
    async def web_search(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """Enhanced web search with multiple sources and content extraction"""

        try:
            results = []

            # Use DuckDuckGo search API
            with DDGS() as ddgs:
                search_results = ddgs.text(query, max_results=max_results)

                for result in search_results:
                    results.append({
                        "title": result.get("title", ""),
                        "url": result.get("href", ""),
                        "snippet": result.get("body", ""),
                        "source": "duckduckgo"
                    })

            # Try to fetch content from top results
            enhanced_results = []
            for result in results[:3]:  # Only enhance top 3 results
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(result["url"], timeout=10) as response:
                            if response.status == 200:
                                html = await response.text()
                                soup = BeautifulSoup(html, 'html.parser')

                                # Extract main content
                                content = ""
                                for tag in soup.find_all(['p', 'h1', 'h2', 'h3']):
                                    content += tag.get_text() + "\n"

                                result["content"] = content[:1000]  # Limit content
                                result["content_extracted"] = True
                except Exception:
                    result["content_extracted"] = False

                enhanced_results.append(result)

            logger.info(f"🔍 Web search completed: {len(results)} results for '{query}'")

            return {
                "query": query,
                "results": enhanced_results,
                "total_results": len(results),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Web search failed: {e}")
            return {
                "query": query,
                "results": [],
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def file_read(self, path: str) -> str:
        """Read contents of a file"""
        
        try:
            file_path = Path(path)
            
            # Security check - ensure file is within allowed directory
            if not self._is_path_allowed(file_path):
                raise ToolException(f"Access to path not allowed: {path}")
            
            # Check file extension
            if file_path.suffix not in settings.tools.file_allowed_extensions:
                raise ToolException(f"File type not allowed: {file_path.suffix}")
            
            # Check file size
            if file_path.stat().st_size > settings.tools.file_max_size:
                raise ToolException(f"File too large: {file_path.stat().st_size} bytes")
            
            # Read file
            content = file_path.read_text(encoding='utf-8')
            return content
            
        except Exception as e:
            raise ToolException(f"Failed to read file {path}: {e}")
    
    async def file_write(self, path: str, content: str, backup: bool = None) -> str:
        """Write content to a file"""
        
        try:
            file_path = Path(path)
            
            # Security check
            if not self._is_path_allowed(file_path):
                raise ToolException(f"Access to path not allowed: {path}")
            
            # Check file extension
            if file_path.suffix not in settings.tools.file_allowed_extensions:
                raise ToolException(f"File type not allowed: {file_path.suffix}")
            
            # Create backup if enabled
            backup = backup if backup is not None else settings.tools.file_backup_enabled
            if backup and file_path.exists():
                backup_path = file_path.with_suffix(file_path.suffix + '.backup')
                backup_path.write_text(file_path.read_text(encoding='utf-8'), encoding='utf-8')
            
            # Create directory if it doesn't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file
            file_path.write_text(content, encoding='utf-8')
            
            return f"File written successfully: {path}"
            
        except Exception as e:
            raise ToolException(f"Failed to write file {path}: {e}")
    
    async def file_list(self, path: str = ".") -> List[Dict[str, Any]]:
        """List files and directories"""
        
        try:
            dir_path = Path(path)
            
            # Security check
            if not self._is_path_allowed(dir_path):
                raise ToolException(f"Access to path not allowed: {path}")
            
            if not dir_path.exists():
                raise ToolException(f"Path does not exist: {path}")
            
            if not dir_path.is_dir():
                raise ToolException(f"Path is not a directory: {path}")
            
            items = []
            for item in dir_path.iterdir():
                try:
                    stat = item.stat()
                    items.append({
                        "name": item.name,
                        "path": str(item),
                        "type": "directory" if item.is_dir() else "file",
                        "size": stat.st_size if item.is_file() else None,
                        "modified": stat.st_mtime
                    })
                except (OSError, PermissionError):
                    # Skip items we can't access
                    continue
            
            return sorted(items, key=lambda x: (x["type"], x["name"]))
            
        except Exception as e:
            raise ToolException(f"Failed to list directory {path}: {e}")
    
    async def command_execute(self, command: str, timeout: int = None, working_dir: str = None) -> Dict[str, Any]:
        """Execute a shell command with enhanced error handling and logging"""

        timeout = timeout or settings.tools.command_timeout
        working_dir = working_dir or settings.tools.command_working_directory

        # Security check - only allow whitelisted commands
        command_parts = command.split()
        if not command_parts:
            raise ToolException("Empty command")

        base_command = command_parts[0]
        if base_command not in settings.tools.command_allowed:
            raise ToolException(f"Command not allowed: {base_command}. Allowed commands: {', '.join(settings.tools.command_allowed)}")

        # Log command execution
        logger.info(f"🔧 Executing command: {command}")
        logger.debug(f"   Working directory: {working_dir}")
        logger.debug(f"   Timeout: {timeout}s")

        start_time = asyncio.get_event_loop().time()

        try:
            # Execute command
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=working_dir
            )

            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()  # Ensure process is cleaned up
                raise ToolException(f"Command timed out after {timeout} seconds")

            execution_time = asyncio.get_event_loop().time() - start_time

            result = {
                "command": command,
                "stdout": stdout.decode('utf-8', errors='replace'),
                "stderr": stderr.decode('utf-8', errors='replace'),
                "return_code": process.returncode,
                "execution_time": round(execution_time, 2),
                "working_directory": working_dir,
                "success": process.returncode == 0
            }

            # Add to command history
            self._add_to_history(result)

            # Log result
            if result["success"]:
                logger.info(f"✅ Command completed successfully in {execution_time:.2f}s")
                if result["stdout"]:
                    logger.debug(f"   Output: {result['stdout'][:200]}...")
            else:
                logger.warning(f"⚠️ Command failed with return code {process.returncode}")
                if result["stderr"]:
                    logger.warning(f"   Error: {result['stderr'][:200]}...")

            return result

        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            error_result = {
                "command": command,
                "stdout": "",
                "stderr": str(e),
                "return_code": -1,
                "execution_time": round(execution_time, 2),
                "working_directory": working_dir,
                "success": False,
                "error": str(e)
            }

            self._add_to_history(error_result)
            logger.error(f"❌ Command execution failed: {e}")
            raise ToolException(f"Command execution failed: {e}")

    def _add_to_history(self, result: Dict[str, Any]):
        """Add command result to history"""
        self.command_history.append({
            **result,
            "timestamp": asyncio.get_event_loop().time()
        })

        # Keep only recent history
        if len(self.command_history) > self.max_history:
            self.command_history = self.command_history[-self.max_history:]

    async def terminal_execute(self, command: str, interactive: bool = False) -> Dict[str, Any]:
        """Execute command in terminal with real-time output support"""

        if interactive:
            # For interactive commands, use a different approach
            logger.info(f"🖥️ Starting interactive terminal session: {command}")

            try:
                # Create a pseudo-terminal for interactive commands
                import pty
                import select

                master, slave = pty.openpty()

                process = await asyncio.create_subprocess_shell(
                    command,
                    stdin=slave,
                    stdout=slave,
                    stderr=slave,
                    preexec_fn=os.setsid
                )

                os.close(slave)

                output = []
                while process.returncode is None:
                    ready, _, _ = select.select([master], [], [], 0.1)
                    if ready:
                        try:
                            data = os.read(master, 1024).decode('utf-8', errors='replace')
                            output.append(data)
                            logger.info(f"📟 {data.strip()}")
                        except OSError:
                            break

                    # Check if process is still running
                    try:
                        process.poll()
                    except:
                        break

                os.close(master)

                return {
                    "command": command,
                    "output": "".join(output),
                    "interactive": True,
                    "success": True
                }

            except Exception as e:
                logger.error(f"Interactive terminal failed: {e}")
                return await self.command_execute(command)
        else:
            # Use regular command execution
            return await self.command_execute(command)

    async def batch_execute(self, commands: List[str], stop_on_error: bool = True) -> Dict[str, Any]:
        """Execute multiple commands in sequence"""

        logger.info(f"🔄 Executing batch of {len(commands)} commands")

        results = []
        total_time = 0

        for i, command in enumerate(commands):
            logger.info(f"📋 Executing command {i+1}/{len(commands)}: {command}")

            try:
                result = await self.command_execute(command)
                results.append(result)
                total_time += result.get("execution_time", 0)

                if not result["success"] and stop_on_error:
                    logger.warning(f"⚠️ Stopping batch execution due to error in command {i+1}")
                    break

            except Exception as e:
                error_result = {
                    "command": command,
                    "error": str(e),
                    "success": False,
                    "execution_time": 0
                }
                results.append(error_result)

                if stop_on_error:
                    logger.error(f"❌ Stopping batch execution due to error: {e}")
                    break

        successful = sum(1 for r in results if r.get("success", False))

        return {
            "commands": commands,
            "results": results,
            "total_commands": len(commands),
            "successful_commands": successful,
            "failed_commands": len(results) - successful,
            "total_execution_time": round(total_time, 2),
            "success": successful == len(commands)
        }

    async def environment_info(self) -> Dict[str, Any]:
        """Get information about the current environment"""

        logger.info("🔍 Gathering environment information")

        try:
            info = {
                "platform": os.name,
                "working_directory": os.getcwd(),
                "python_version": None,
                "git_version": None,
                "node_version": None,
                "npm_version": None,
                "environment_variables": dict(os.environ),
                "path": os.environ.get("PATH", "").split(os.pathsep),
                "command_history_count": len(self.command_history)
            }

            # Get version information for common tools
            version_commands = {
                "python_version": "python --version",
                "git_version": "git --version",
                "node_version": "node --version",
                "npm_version": "npm --version"
            }

            for key, command in version_commands.items():
                try:
                    result = await self.command_execute(command)
                    if result["success"]:
                        info[key] = result["stdout"].strip()
                except:
                    info[key] = "Not available"

            return info

        except Exception as e:
            logger.error(f"Failed to gather environment info: {e}")
            raise ToolException(f"Failed to gather environment info: {e}")
    
    async def code_analyze(self, code: str, language: str = "python") -> Dict[str, Any]:
        """Analyze code structure and provide insights"""
        
        try:
            analysis = {
                "language": language,
                "lines": len(code.split('\n')),
                "characters": len(code),
                "functions": [],
                "classes": [],
                "imports": []
            }
            
            # Simple analysis for Python
            if language.lower() == "python":
                lines = code.split('\n')
                for i, line in enumerate(lines):
                    stripped = line.strip()
                    
                    if stripped.startswith('def '):
                        func_name = stripped.split('(')[0].replace('def ', '')
                        analysis["functions"].append({
                            "name": func_name,
                            "line": i + 1
                        })
                    
                    elif stripped.startswith('class '):
                        class_name = stripped.split('(')[0].replace('class ', '').rstrip(':')
                        analysis["classes"].append({
                            "name": class_name,
                            "line": i + 1
                        })
                    
                    elif stripped.startswith(('import ', 'from ')):
                        analysis["imports"].append({
                            "statement": stripped,
                            "line": i + 1
                        })
            
            return analysis
            
        except Exception as e:
            raise ToolException(f"Code analysis failed: {e}")
    
    async def git_operations(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Perform Git operations"""

        try:
            repo_path = kwargs.get("repo_path", ".")
            repo = git.Repo(repo_path)

            if operation == "status":
                # Get repository status
                status = {
                    "branch": repo.active_branch.name,
                    "is_dirty": repo.is_dirty(),
                    "untracked_files": repo.untracked_files,
                    "modified_files": [item.a_path for item in repo.index.diff(None)],
                    "staged_files": [item.a_path for item in repo.index.diff("HEAD")]
                }
                return status

            elif operation == "add":
                files = kwargs.get("files", [])
                if files:
                    repo.index.add(files)
                    return {"message": f"Added {len(files)} files to staging"}
                else:
                    repo.git.add(A=True)
                    return {"message": "Added all files to staging"}

            elif operation == "commit":
                message = kwargs.get("message", "Auto-commit by Reverie")
                commit = repo.index.commit(message)
                return {"message": f"Committed with hash: {commit.hexsha[:8]}"}

            elif operation == "log":
                limit = kwargs.get("limit", 10)
                commits = []
                for commit in repo.iter_commits(max_count=limit):
                    commits.append({
                        "hash": commit.hexsha[:8],
                        "message": commit.message.strip(),
                        "author": str(commit.author),
                        "date": commit.committed_datetime.isoformat()
                    })
                return {"commits": commits}

            else:
                raise ToolException(f"Unknown git operation: {operation}")

        except git.InvalidGitRepositoryError:
            raise ToolException("Not a git repository")
        except Exception as e:
            raise ToolException(f"Git operation failed: {e}")

    async def project_analyze(self, path: str = ".") -> Dict[str, Any]:
        """Analyze project structure and provide insights"""

        try:
            project_path = Path(path)

            if not self._is_path_allowed(project_path):
                raise ToolException(f"Access to path not allowed: {path}")

            analysis = {
                "project_name": project_path.name,
                "total_files": 0,
                "total_size": 0,
                "languages": {},
                "file_types": {},
                "structure": {},
                "dependencies": {},
                "git_info": None
            }

            # Analyze file structure
            for file_path in project_path.rglob("*"):
                if file_path.is_file():
                    try:
                        stat = file_path.stat()
                        analysis["total_files"] += 1
                        analysis["total_size"] += stat.st_size

                        # File extension analysis
                        ext = file_path.suffix.lower()
                        if ext:
                            analysis["file_types"][ext] = analysis["file_types"].get(ext, 0) + 1

                        # Language detection
                        if ext in ['.py']:
                            analysis["languages"]["Python"] = analysis["languages"].get("Python", 0) + 1
                        elif ext in ['.js', '.jsx']:
                            analysis["languages"]["JavaScript"] = analysis["languages"].get("JavaScript", 0) + 1
                        elif ext in ['.ts', '.tsx']:
                            analysis["languages"]["TypeScript"] = analysis["languages"].get("TypeScript", 0) + 1
                        elif ext in ['.java']:
                            analysis["languages"]["Java"] = analysis["languages"].get("Java", 0) + 1
                        elif ext in ['.cpp', '.cc', '.cxx']:
                            analysis["languages"]["C++"] = analysis["languages"].get("C++", 0) + 1
                        elif ext in ['.c']:
                            analysis["languages"]["C"] = analysis["languages"].get("C", 0) + 1
                        elif ext in ['.rs']:
                            analysis["languages"]["Rust"] = analysis["languages"].get("Rust", 0) + 1
                        elif ext in ['.go']:
                            analysis["languages"]["Go"] = analysis["languages"].get("Go", 0) + 1

                    except (OSError, PermissionError):
                        continue

            # Check for common project files
            common_files = [
                "package.json", "requirements.txt", "Cargo.toml", "go.mod",
                "pom.xml", "build.gradle", "composer.json", "Gemfile"
            ]

            for file_name in common_files:
                file_path = project_path / file_name
                if file_path.exists():
                    try:
                        content = file_path.read_text(encoding='utf-8')
                        if file_name == "package.json":
                            data = json.loads(content)
                            analysis["dependencies"]["npm"] = list(data.get("dependencies", {}).keys())
                        elif file_name == "requirements.txt":
                            deps = [line.split('==')[0].split('>=')[0].split('<=')[0]
                                   for line in content.split('\n') if line.strip() and not line.startswith('#')]
                            analysis["dependencies"]["pip"] = deps
                    except:
                        pass

            # Git information
            try:
                repo = git.Repo(project_path)
                analysis["git_info"] = {
                    "branch": repo.active_branch.name,
                    "is_dirty": repo.is_dirty(),
                    "remote_url": repo.remotes.origin.url if repo.remotes else None
                }
            except:
                pass

            return analysis

        except Exception as e:
            raise ToolException(f"Project analysis failed: {e}")

    def _is_path_allowed(self, path: Path) -> bool:
        """Check if a path is allowed for file operations"""

        try:
            # Resolve the path to handle .. and . components
            resolved_path = path.resolve()

            # Get the current working directory
            cwd = Path.cwd().resolve()

            # Check if the path is within the current working directory
            return str(resolved_path).startswith(str(cwd))

        except Exception:
            return False

    async def create_file(self, path: str, content: str = "", backup: bool = False) -> Dict[str, Any]:
        """Create a new file"""

        file_path = Path(path)

        if not self._is_path_allowed(file_path):
            raise ToolException(f"Access to path not allowed: {path}")

        try:
            # Check if file already exists
            if file_path.exists():
                if not backup:
                    raise ToolException(f"File already exists: {path}")
                else:
                    # Create backup
                    backup_path = file_path.with_suffix(file_path.suffix + ".bak")
                    file_path.rename(backup_path)

            # Create parent directories if needed
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # Write content
            file_path.write_text(content, encoding='utf-8')

            logger.info(f"File created: {path}")
            return {
                "success": True,
                "message": f"File created successfully: {path}",
                "path": str(file_path),
                "size": len(content)
            }

        except Exception as e:
            raise ToolException(f"Failed to create file: {e}")

    async def delete_file(self, path: str, backup: bool = True) -> Dict[str, Any]:
        """Delete a file"""

        file_path = Path(path)

        if not self._is_path_allowed(file_path):
            raise ToolException(f"Access to path not allowed: {path}")

        if not file_path.exists():
            raise ToolException(f"File not found: {path}")

        try:
            if backup and file_path.is_file():
                # Create backup before deletion
                backup_path = file_path.with_suffix(file_path.suffix + ".deleted")
                file_path.rename(backup_path)
                message = f"File deleted with backup: {path}"
            else:
                # Direct deletion
                if file_path.is_file():
                    file_path.unlink()
                else:
                    import shutil
                    shutil.rmtree(file_path)
                message = f"File deleted: {path}"

            logger.info(message)
            return {
                "success": True,
                "message": message,
                "path": str(file_path)
            }

        except Exception as e:
            raise ToolException(f"Failed to delete file: {e}")

    async def move_file(self, source: str, destination: str, backup: bool = True) -> Dict[str, Any]:
        """Move or rename a file"""

        source_path = Path(source)
        dest_path = Path(destination)

        if not self._is_path_allowed(source_path) or not self._is_path_allowed(dest_path):
            raise ToolException("Access to path not allowed")

        if not source_path.exists():
            raise ToolException(f"Source file not found: {source}")

        try:
            # Check if destination exists
            if dest_path.exists():
                if backup:
                    backup_path = dest_path.with_suffix(dest_path.suffix + ".bak")
                    dest_path.rename(backup_path)
                else:
                    raise ToolException(f"Destination already exists: {destination}")

            # Create parent directories if needed
            dest_path.parent.mkdir(parents=True, exist_ok=True)

            # Move file
            source_path.rename(dest_path)

            logger.info(f"File moved: {source} -> {destination}")
            return {
                "success": True,
                "message": f"File moved successfully: {source} -> {destination}",
                "source": str(source_path),
                "destination": str(dest_path)
            }

        except Exception as e:
            raise ToolException(f"Failed to move file: {e}")

    async def find_in_files(self, pattern: str, path: str = ".", file_pattern: str = "*", max_results: int = 100) -> List[Dict[str, Any]]:
        """Search for text pattern in files"""

        search_path = Path(path)

        if not self._is_path_allowed(search_path):
            raise ToolException(f"Access to path not allowed: {path}")

        try:
            import re
            import fnmatch

            results = []
            pattern_regex = re.compile(pattern, re.IGNORECASE)

            for file_path in search_path.rglob(file_pattern):
                if len(results) >= max_results:
                    break

                if file_path.is_file() and self._is_path_allowed(file_path):
                    try:
                        # Skip binary files
                        if file_path.suffix.lower() in ['.exe', '.dll', '.so', '.dylib', '.bin']:
                            continue

                        content = file_path.read_text(encoding='utf-8', errors='ignore')
                        lines = content.split('\n')

                        for line_num, line in enumerate(lines, 1):
                            if pattern_regex.search(line):
                                results.append({
                                    "file": str(file_path),
                                    "line": line_num,
                                    "content": line.strip(),
                                    "match": pattern
                                })

                                if len(results) >= max_results:
                                    break

                    except (UnicodeDecodeError, PermissionError):
                        # Skip files that can't be read
                        continue

            logger.info(f"Found {len(results)} matches for pattern '{pattern}'")
            return results

        except Exception as e:
            raise ToolException(f"Search failed: {e}")

    async def copy_file(self, source: str, destination: str) -> Dict[str, Any]:
        """Copy a file from source to destination"""
        try:
            source_path = Path(source)
            dest_path = Path(destination)

            if not source_path.exists():
                raise FileNotFoundError(f"Source file not found: {source}")

            if not self._validate_path(str(source_path)) or not self._validate_path(str(dest_path)):
                raise ToolException("Invalid or blocked path")

            # Create destination directory if needed
            dest_path.parent.mkdir(parents=True, exist_ok=True)

            shutil.copy2(source_path, dest_path)

            return {
                "source": str(source_path),
                "destination": str(dest_path),
                "size": dest_path.stat().st_size,
                "message": "File copied successfully"
            }

        except Exception as e:
            logger.error(f"File copy failed: {e}")
            raise ToolException(f"Failed to copy file: {str(e)}")

    async def text_replace(self, path: str, old_text: str, new_text: str, backup: bool = True) -> Dict[str, Any]:
        """Replace text in a file"""
        try:
            file_path = Path(path)

            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {path}")

            if not self._validate_path(str(file_path)):
                raise ToolException("Invalid or blocked path")

            # Create backup if requested
            if backup:
                backup_path = file_path.with_suffix(file_path.suffix + ".bak")
                shutil.copy2(file_path, backup_path)

            # Read, replace, and write
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            new_content = content.replace(old_text, new_text)
            replacements = content.count(old_text)

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)

            return {
                "path": str(file_path),
                "replacements": replacements,
                "backup_created": backup,
                "message": f"Replaced {replacements} occurrences"
            }

        except Exception as e:
            logger.error(f"Text replacement failed: {e}")
            raise ToolException(f"Failed to replace text: {str(e)}")

    async def system_info(self) -> Dict[str, Any]:
        """Get detailed system information"""
        try:
            import platform
            import psutil

            return {
                "platform": {
                    "system": platform.system(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "machine": platform.machine(),
                    "processor": platform.processor(),
                    "python_version": platform.python_version()
                },
                "memory": {
                    "total": psutil.virtual_memory().total,
                    "available": psutil.virtual_memory().available,
                    "percent": psutil.virtual_memory().percent
                },
                "disk": {
                    "total": psutil.disk_usage('/').total,
                    "used": psutil.disk_usage('/').used,
                    "free": psutil.disk_usage('/').free,
                    "percent": psutil.disk_usage('/').percent
                },
                "cpu": {
                    "count": psutil.cpu_count(),
                    "percent": psutil.cpu_percent(interval=1)
                }
            }

        except Exception as e:
            logger.error(f"System info failed: {e}")
            raise ToolException(f"Failed to get system info: {str(e)}")

    def get_execution_stats(self) -> Dict[str, Any]:
        """Get tool execution statistics"""
        return {
            "stats": self.execution_stats,
            "recent_history": self.command_history[-10:] if self.command_history else []
        }

    async def git_status(self, path: str = ".") -> Dict[str, Any]:
        """Get Git repository status"""
        try:
            repo_path = Path(path)

            if not self._validate_path(str(repo_path)):
                raise ToolException("Invalid or blocked path")

            try:
                repo = git.Repo(repo_path)
            except git.InvalidGitRepositoryError:
                raise ToolException(f"Not a Git repository: {path}")

            # Get status information
            status = {
                "repository": str(repo_path),
                "branch": repo.active_branch.name,
                "commit": repo.head.commit.hexsha[:8],
                "commit_message": repo.head.commit.message.strip(),
                "is_dirty": repo.is_dirty(),
                "untracked_files": repo.untracked_files,
                "modified_files": [item.a_path for item in repo.index.diff(None)],
                "staged_files": [item.a_path for item in repo.index.diff("HEAD")],
                "remote_url": repo.remotes.origin.url if repo.remotes else None
            }

            return status

        except Exception as e:
            logger.error(f"Git status failed: {e}")
            raise ToolException(f"Failed to get Git status: {str(e)}")

    async def git_diff(self, path: str = ".", staged: bool = False) -> Dict[str, Any]:
        """Get Git diff information"""
        try:
            repo_path = Path(path)

            if not self._validate_path(str(repo_path)):
                raise ToolException("Invalid or blocked path")

            try:
                repo = git.Repo(repo_path)
            except git.InvalidGitRepositoryError:
                raise ToolException(f"Not a Git repository: {path}")

            # Get diff
            if staged:
                diff = repo.index.diff("HEAD")
            else:
                diff = repo.index.diff(None)

            diff_info = {
                "repository": str(repo_path),
                "staged": staged,
                "files": []
            }

            for item in diff:
                file_diff = {
                    "file": item.a_path,
                    "change_type": item.change_type,
                    "diff": item.diff.decode('utf-8') if item.diff else ""
                }
                diff_info["files"].append(file_diff)

            return diff_info

        except Exception as e:
            logger.error(f"Git diff failed: {e}")
            raise ToolException(f"Failed to get Git diff: {str(e)}")

    async def process_info(self, name: str = None) -> Dict[str, Any]:
        """Get information about running processes"""
        try:
            import psutil

            processes = []

            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    proc_info = proc.info
                    if name is None or name.lower() in proc_info['name'].lower():
                        processes.append({
                            "pid": proc_info['pid'],
                            "name": proc_info['name'],
                            "cpu_percent": proc_info['cpu_percent'],
                            "memory_percent": proc_info['memory_percent'],
                            "status": proc_info['status']
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            return {
                "processes": processes,
                "total_count": len(processes),
                "filter": name
            }

        except Exception as e:
            logger.error(f"Process info failed: {e}")
            raise ToolException(f"Failed to get process info: {str(e)}")

    async def file_permissions(self, path: str, mode: str = None) -> Dict[str, Any]:
        """Manage file permissions"""
        try:
            file_path = Path(path)

            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {path}")

            if not self._validate_path(str(file_path)):
                raise ToolException("Invalid or blocked path")

            current_stat = file_path.stat()
            current_mode = oct(current_stat.st_mode)[-3:]

            result = {
                "path": str(file_path),
                "current_mode": current_mode,
                "readable": os.access(file_path, os.R_OK),
                "writable": os.access(file_path, os.W_OK),
                "executable": os.access(file_path, os.X_OK)
            }

            # Change permissions if mode is provided
            if mode:
                try:
                    new_mode = int(mode, 8)
                    file_path.chmod(new_mode)
                    result["new_mode"] = mode
                    result["changed"] = True
                except ValueError:
                    raise ToolException(f"Invalid mode format: {mode}")
            else:
                result["changed"] = False

            return result

        except Exception as e:
            logger.error(f"File permissions operation failed: {e}")
            raise ToolException(f"Failed to manage file permissions: {str(e)}")

    async def disk_usage(self, path: str = ".") -> Dict[str, Any]:
        """Get disk usage information"""
        try:
            target_path = Path(path)

            if not target_path.exists():
                raise FileNotFoundError(f"Path not found: {path}")

            if not self._validate_path(str(target_path)):
                raise ToolException("Invalid or blocked path")

            usage = shutil.disk_usage(target_path)

            return {
                "path": str(target_path),
                "total": usage.total,
                "used": usage.used,
                "free": usage.free,
                "total_gb": round(usage.total / (1024**3), 2),
                "used_gb": round(usage.used / (1024**3), 2),
                "free_gb": round(usage.free / (1024**3), 2),
                "usage_percent": round((usage.used / usage.total) * 100, 1)
            }

        except Exception as e:
            logger.error(f"Disk usage check failed: {e}")
            raise ToolException(f"Failed to get disk usage: {str(e)}")

    async def network_info(self) -> Dict[str, Any]:
        """Get network information"""
        try:
            import psutil
            import socket

            # Get network interfaces
            interfaces = {}
            for interface, addrs in psutil.net_if_addrs().items():
                interface_info = {
                    "addresses": []
                }

                for addr in addrs:
                    addr_info = {
                        "family": str(addr.family),
                        "address": addr.address,
                        "netmask": addr.netmask,
                        "broadcast": addr.broadcast
                    }
                    interface_info["addresses"].append(addr_info)

                interfaces[interface] = interface_info

            # Get network statistics
            net_io = psutil.net_io_counters()

            # Get hostname and IP
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)

            return {
                "hostname": hostname,
                "local_ip": local_ip,
                "interfaces": interfaces,
                "io_stats": {
                    "bytes_sent": net_io.bytes_sent,
                    "bytes_recv": net_io.bytes_recv,
                    "packets_sent": net_io.packets_sent,
                    "packets_recv": net_io.packets_recv
                }
            }

        except Exception as e:
            logger.error(f"Network info failed: {e}")
            raise ToolException(f"Failed to get network info: {str(e)}")

    async def code_format(self, path: str, formatter: str = "black") -> Dict[str, Any]:
        """Format code using various formatters"""
        try:
            file_path = Path(path)

            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {path}")

            if not self._validate_path(str(file_path)):
                raise ToolException("Invalid or blocked path")

            # Read original content
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            formatted_content = original_content

            # Apply formatter based on file type and formatter choice
            if file_path.suffix == '.py':
                if formatter == "black":
                    try:
                        import black
                        formatted_content = black.format_str(original_content, mode=black.FileMode())
                    except ImportError:
                        raise ToolException("Black formatter not available")
                elif formatter == "autopep8":
                    try:
                        import autopep8
                        formatted_content = autopep8.fix_code(original_content)
                    except ImportError:
                        raise ToolException("autopep8 formatter not available")
            elif file_path.suffix in ['.js', '.ts']:
                # Placeholder for JavaScript/TypeScript formatting
                # Would integrate with prettier or similar
                pass

            # Write formatted content
            if formatted_content != original_content:
                # Create backup
                backup_path = file_path.with_suffix(file_path.suffix + '.bak')
                shutil.copy2(file_path, backup_path)

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(formatted_content)

                return {
                    "path": str(file_path),
                    "formatter": formatter,
                    "changed": True,
                    "backup_created": str(backup_path),
                    "lines_changed": len(original_content.splitlines()) - len(formatted_content.splitlines())
                }
            else:
                return {
                    "path": str(file_path),
                    "formatter": formatter,
                    "changed": False,
                    "message": "No formatting changes needed"
                }

        except Exception as e:
            logger.error(f"Code formatting failed: {e}")
            raise ToolException(f"Failed to format code: {str(e)}")

    async def dependency_analysis(self, path: str) -> Dict[str, Any]:
        """Analyze project dependencies"""
        try:
            project_path = Path(path)

            if not project_path.exists():
                raise FileNotFoundError(f"Path not found: {path}")

            if not self._validate_path(str(project_path)):
                raise ToolException("Invalid or blocked path")

            dependencies = {
                "python": {},
                "javascript": {},
                "requirements_files": [],
                "package_files": []
            }

            # Python dependencies
            req_files = list(project_path.rglob("requirements*.txt"))
            for req_file in req_files:
                dependencies["requirements_files"].append(str(req_file))
                try:
                    with open(req_file, 'r') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#'):
                                if '==' in line:
                                    pkg, version = line.split('==', 1)
                                    dependencies["python"][pkg.strip()] = version.strip()
                                else:
                                    dependencies["python"][line] = "latest"
                except Exception:
                    continue

            # JavaScript dependencies
            package_files = list(project_path.rglob("package.json"))
            for pkg_file in package_files:
                dependencies["package_files"].append(str(pkg_file))
                try:
                    with open(pkg_file, 'r') as f:
                        pkg_data = json.load(f)
                        if "dependencies" in pkg_data:
                            dependencies["javascript"].update(pkg_data["dependencies"])
                        if "devDependencies" in pkg_data:
                            for dep, version in pkg_data["devDependencies"].items():
                                dependencies["javascript"][f"{dep} (dev)"] = version
                except Exception:
                    continue

            return {
                "project_path": str(project_path),
                "dependencies": dependencies,
                "total_python_deps": len(dependencies["python"]),
                "total_js_deps": len(dependencies["javascript"]),
                "config_files_found": len(dependencies["requirements_files"]) + len(dependencies["package_files"])
            }

        except Exception as e:
            logger.error(f"Dependency analysis failed: {e}")
            raise ToolException(f"Failed to analyze dependencies: {str(e)}")

    async def create_project_structure(self, path: str, template: str = "python") -> Dict[str, Any]:
        """Create a new project structure from template"""
        try:
            project_path = Path(path)

            if project_path.exists():
                raise ToolException(f"Project directory already exists: {path}")

            if not self._validate_path(str(project_path.parent)):
                raise ToolException("Invalid or blocked parent path")

            # Create project directory
            project_path.mkdir(parents=True, exist_ok=True)

            created_files = []

            if template == "python":
                # Python project structure
                structure = {
                    "src": {},
                    "tests": {},
                    "docs": {},
                    "requirements.txt": "# Project dependencies\n",
                    "README.md": f"# {project_path.name}\n\nProject description here.\n",
                    "setup.py": f"""from setuptools import setup, find_packages

setup(
    name="{project_path.name}",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[],
)
""",
                    ".gitignore": """__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
"""
                }
            elif template == "javascript":
                # JavaScript/Node.js project structure
                structure = {
                    "src": {},
                    "test": {},
                    "docs": {},
                    "package.json": json.dumps({
                        "name": project_path.name.lower(),
                        "version": "1.0.0",
                        "description": "",
                        "main": "src/index.js",
                        "scripts": {
                            "test": "echo \"Error: no test specified\" && exit 1"
                        },
                        "keywords": [],
                        "author": "",
                        "license": "ISC"
                    }, indent=2),
                    "README.md": f"# {project_path.name}\n\nProject description here.\n",
                    ".gitignore": """node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.env
dist/
build/
"""
                }
            else:
                raise ToolException(f"Unknown template: {template}")

            # Create structure
            def create_structure(base_path: Path, structure: Dict):
                for name, content in structure.items():
                    item_path = base_path / name
                    if isinstance(content, dict):
                        # Directory
                        item_path.mkdir(exist_ok=True)
                        created_files.append(f"📁 {item_path}")
                        create_structure(item_path, content)
                    else:
                        # File
                        with open(item_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        created_files.append(f"📄 {item_path}")

            create_structure(project_path, structure)

            return {
                "project_path": str(project_path),
                "template": template,
                "files_created": created_files,
                "total_files": len(created_files)
            }

        except Exception as e:
            logger.error(f"Project creation failed: {e}")
            raise ToolException(f"Failed to create project: {str(e)}")

    async def task_tracker_create(self, title: str, description: str = "", priority: str = "medium",
                                 project: str = "default") -> Dict[str, Any]:
        """Create a new task in the task tracker"""
        try:
            # Create tasks directory if it doesn't exist
            tasks_dir = Path("data/tasks")
            tasks_dir.mkdir(parents=True, exist_ok=True)

            # Generate task ID
            task_id = f"task_{int(time.time())}_{hash(title) % 10000}"

            task_data = {
                "id": task_id,
                "title": title,
                "description": description,
                "priority": priority,
                "project": project,
                "status": "todo",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "completed_at": None,
                "tags": [],
                "assignee": None,
                "estimated_hours": None,
                "actual_hours": None
            }

            # Save task
            task_file = tasks_dir / f"{task_id}.json"
            with open(task_file, 'w') as f:
                json.dump(task_data, f, indent=2)

            return {
                "task_id": task_id,
                "task": task_data,
                "file": str(task_file),
                "message": f"Task '{title}' created successfully"
            }

        except Exception as e:
            logger.error(f"Task creation failed: {e}")
            raise ToolException(f"Failed to create task: {str(e)}")

    async def task_tracker_list(self, project: str = None, status: str = None) -> Dict[str, Any]:
        """List tasks from the task tracker"""
        try:
            tasks_dir = Path("data/tasks")

            if not tasks_dir.exists():
                return {"tasks": [], "total": 0, "message": "No tasks found"}

            tasks = []

            for task_file in tasks_dir.glob("task_*.json"):
                try:
                    with open(task_file, 'r') as f:
                        task_data = json.load(f)

                    # Apply filters
                    if project and task_data.get("project") != project:
                        continue
                    if status and task_data.get("status") != status:
                        continue

                    tasks.append(task_data)
                except Exception:
                    continue

            # Sort by created date
            tasks.sort(key=lambda x: x.get("created_at", ""), reverse=True)

            return {
                "tasks": tasks,
                "total": len(tasks),
                "filters": {"project": project, "status": status}
            }

        except Exception as e:
            logger.error(f"Task listing failed: {e}")
            raise ToolException(f"Failed to list tasks: {str(e)}")

    async def task_tracker_update(self, task_id: str, **updates) -> Dict[str, Any]:
        """Update a task in the task tracker"""
        try:
            tasks_dir = Path("data/tasks")
            task_file = tasks_dir / f"{task_id}.json"

            if not task_file.exists():
                raise ToolException(f"Task not found: {task_id}")

            # Load existing task
            with open(task_file, 'r') as f:
                task_data = json.load(f)

            # Apply updates
            for key, value in updates.items():
                if key in ["title", "description", "priority", "status", "project", "assignee", "estimated_hours", "actual_hours"]:
                    task_data[key] = value
                elif key == "tags":
                    if isinstance(value, list):
                        task_data["tags"] = value
                    else:
                        task_data["tags"] = [value]

            # Update timestamps
            task_data["updated_at"] = datetime.now().isoformat()
            if updates.get("status") == "completed":
                task_data["completed_at"] = datetime.now().isoformat()

            # Save updated task
            with open(task_file, 'w') as f:
                json.dump(task_data, f, indent=2)

            return {
                "task_id": task_id,
                "task": task_data,
                "updates": updates,
                "message": f"Task '{task_id}' updated successfully"
            }

        except Exception as e:
            logger.error(f"Task update failed: {e}")
            raise ToolException(f"Failed to update task: {str(e)}")

    async def generate_documentation(self, path: str, format: str = "markdown") -> Dict[str, Any]:
        """Generate documentation for a project"""
        try:
            project_path = Path(path)

            if not project_path.exists():
                raise FileNotFoundError(f"Project path not found: {path}")

            if not self._validate_path(str(project_path)):
                raise ToolException("Invalid or blocked path")

            # Analyze project structure
            structure_analysis = await self.project_analyze(str(project_path))

            # Generate documentation content
            if format == "markdown":
                doc_content = self._generate_markdown_docs(project_path, structure_analysis)
            else:
                raise ToolException(f"Unsupported documentation format: {format}")

            # Save documentation
            doc_file = project_path / f"GENERATED_DOCS.{format}"
            with open(doc_file, 'w', encoding='utf-8') as f:
                f.write(doc_content)

            return {
                "project_path": str(project_path),
                "documentation_file": str(doc_file),
                "format": format,
                "size": len(doc_content),
                "message": "Documentation generated successfully"
            }

        except Exception as e:
            logger.error(f"Documentation generation failed: {e}")
            raise ToolException(f"Failed to generate documentation: {str(e)}")

    def _generate_markdown_docs(self, project_path: Path, analysis: Dict[str, Any]) -> str:
        """Generate markdown documentation content"""

        project_name = project_path.name

        content = f"""# {project_name}

## Project Overview

This documentation was automatically generated for the {project_name} project.

### Project Statistics
- **Total Files**: {analysis.get('total_files', 0)}
- **Code Files**: {analysis.get('code_files', 0)}
- **Total Lines**: {analysis.get('total_lines', 0)}
- **Languages**: {', '.join(analysis.get('languages', []))}

## Project Structure

```
{project_path.name}/
"""

        # Add directory structure
        for item in analysis.get('structure', []):
            if item['type'] == 'directory':
                content += f"├── {item['name']}/\n"
            else:
                content += f"├── {item['name']}\n"

        content += """```

## Files Overview

"""

        # Add file descriptions
        for file_info in analysis.get('files', []):
            if file_info.get('type') == 'file':
                content += f"### {file_info['name']}\n"
                content += f"- **Size**: {file_info.get('size', 0)} bytes\n"
                content += f"- **Type**: {file_info.get('extension', 'unknown')}\n"
                if file_info.get('lines'):
                    content += f"- **Lines**: {file_info['lines']}\n"
                content += "\n"

        content += f"""
## Generated Information

This documentation was automatically generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}.

For more detailed information, please refer to the individual source files and comments within the codebase.
"""

        return content
