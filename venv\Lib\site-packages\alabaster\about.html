{% if theme_logo %}
<p class="logo">
  <a href="{{ pathto(master_doc) }}">
    <img class="logo" src="{{ pathto('_static/' ~ theme_logo, 1) }}" alt="Logo" />
    {% if theme_logo_name|lower == 'true' %}
    <h1 class="logo logo-name">{{ project }}</h1>
    {% elif theme_logo_name|lower != 'false' %}
    <h1 class="logo logo-name">{{ theme_logo_name }}</h1>
    {% endif %}
  </a>
</p>
{% else %}
<h1 class="logo"><a href="{{ pathto(master_doc) }}">{{ project }}</a></h1>
{% endif %}

{% if theme_description %}
<p class="blurb">{{ theme_description }}</p>
{% endif %}

{% if theme_github_user and theme_github_repo %}
{% if theme_github_button|lower == 'true' %}
<p>
<iframe src="https://ghbtns.com/github-btn.html?user={{ theme_github_user }}&repo={{ theme_github_repo }}&type={{ theme_github_type }}&count={{ theme_github_count }}&size=large&v=2"
  allowtransparency="true" frameborder="0" scrolling="0" width="200px" height="35px"></iframe>
</p>
{% endif %}
{% endif %}

{% if theme_travis_button|lower != 'false' %}
{% if theme_travis_button|lower == 'true' %}
    {% set path = theme_github_user + '/' + theme_github_repo %}
{% else %}
    {% set path = theme_travis_button %}
{% endif %}
<p>
<a class="badge" href="https://travis-ci.org/{{ path }}">
    <img
        alt="https://secure.travis-ci.org/{{ path }}.svg?branch={{ theme_badge_branch }}"
        src="https://secure.travis-ci.org/{{ path }}.svg?branch={{ theme_badge_branch }}"
    />
</a>
</p>
{% endif %}

{% if theme_codecov_button|lower != 'false' %}
{% if theme_codecov_button|lower == 'true' %}
    {% set path = theme_github_user + '/' + theme_github_repo %}
{% else %}
    {% set path = theme_codecov_button %}
{% endif %}
<p>
<a class="badge" href="https://codecov.io/github/{{ path }}">
    <img
    alt="https://codecov.io/github/{{ path }}/coverage.svg?branch={{ theme_badge_branch }}"
    src="https://codecov.io/github/{{ path }}/coverage.svg?branch={{ theme_badge_branch }}"
    />
</a>
</p>
{% endif %}
