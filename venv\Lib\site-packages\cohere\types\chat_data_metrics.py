# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class ChatDataMetrics(UncheckedBaseModel):
    num_train_turns: typing.Optional[int] = pydantic.Field(default=None)
    """
    The sum of all turns of valid train examples.
    """

    num_eval_turns: typing.Optional[int] = pydantic.Field(default=None)
    """
    The sum of all turns of valid eval examples.
    """

    preamble: typing.Optional[str] = pydantic.Field(default=None)
    """
    The preamble of this dataset.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
