# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from .api_meta_api_version import ApiMetaApiVersion
from .api_meta_billed_units import ApiMetaBilledUnits
from .api_meta_tokens import ApiMetaTokens
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class ApiMeta(UncheckedBaseModel):
    api_version: typing.Optional[ApiMetaApiVersion] = None
    billed_units: typing.Optional[ApiMetaBilledUnits] = None
    tokens: typing.Optional[ApiMetaTokens] = None
    warnings: typing.Optional[typing.List[str]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
