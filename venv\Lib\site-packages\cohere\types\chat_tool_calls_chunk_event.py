# This file was auto-generated by Fern from our API Definition.

from .chat_stream_event import ChatStreamEvent
from .tool_call_delta import ToolCallDelta
import typing
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class ChatToolCallsChunkEvent(ChatStreamEvent):
    tool_call_delta: ToolCallDelta
    text: typing.Optional[str] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
