"""
Exception handling for Rilance Code Studio Server
"""

from typing import Any, Dict
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from core.logging import logger


class RilanceException(Exception):
    """Base exception for Rilance Code Studio"""
    
    def __init__(self, message: str, code: str = "RILANCE_ERROR", details: Dict[str, Any] = None):
        self.message = message
        self.code = code
        self.details = details or {}
        super().__init__(self.message)


class ModelException(RilanceException):
    """Exception related to AI model operations"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "MODEL_ERROR", details)


class ToolException(RilanceException):
    """Exception related to tool operations"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "TOOL_ERROR", details)


class ConfigException(RilanceException):
    """Exception related to configuration"""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "CONFIG_ERROR", details)


def create_error_response(
    status_code: int,
    message: str,
    code: str = "ERROR",
    details: Dict[str, Any] = None
) -> JSONResponse:
    """Create standardized error response"""
    
    error_data = {
        "error": {
            "code": code,
            "message": message,
            "status_code": status_code
        }
    }
    
    if details:
        error_data["error"]["details"] = details
    
    return JSONResponse(
        status_code=status_code,
        content=error_data
    )


async def rilance_exception_handler(request: Request, exc: RilanceException) -> JSONResponse:
    """Handle custom Rilance exceptions"""
    
    logger.error(f"Rilance exception: {exc.code} - {exc.message}")
    
    status_code = 500
    if isinstance(exc, ModelException):
        status_code = 503  # Service Unavailable
    elif isinstance(exc, ToolException):
        status_code = 422  # Unprocessable Entity
    elif isinstance(exc, ConfigException):
        status_code = 500  # Internal Server Error
    
    return create_error_response(
        status_code=status_code,
        message=exc.message,
        code=exc.code,
        details=exc.details
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions"""
    
    logger.warning(f"HTTP exception: {exc.status_code} - {exc.detail}")
    
    return create_error_response(
        status_code=exc.status_code,
        message=str(exc.detail),
        code="HTTP_ERROR"
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle request validation exceptions"""
    
    logger.warning(f"Validation error: {exc.errors()}")
    
    return create_error_response(
        status_code=422,
        message="Request validation failed",
        code="VALIDATION_ERROR",
        details={"errors": exc.errors()}
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle general exceptions"""
    
    logger.error(f"Unhandled exception: {type(exc).__name__} - {str(exc)}")
    
    return create_error_response(
        status_code=500,
        message="Internal server error",
        code="INTERNAL_ERROR"
    )


def setup_exception_handlers(app: FastAPI):
    """Setup exception handlers for the FastAPI app"""
    
    # Custom exception handlers
    app.add_exception_handler(RilanceException, rilance_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("Exception handlers configured")
