"""
Interactive Console for Reverie Code Studio Server
Allows running commands while the server is running
"""

import asyncio
import threading
import sys
from pathlib import Path
from typing import Dict, Any, Callable, Optional
from rich.console import Console, Group
from rich.prompt import Prompt
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.align import Align
from rich import print as rprint
from rich.live import Live

from core.logging import logger, log_operation
from core.config import settings


def get_simple_multiline_input(prompt: str = "Enter text") -> str:
    """
    Simplified multiline input using standard input with special commands
    Works reliably across all platforms
    """
    console = Console()
    console.print(f"\n[bold bright_magenta]{prompt}[/bold bright_magenta]")
    console.print("[dim]Type your message. Use 'END' on a new line to finish, 'CANCEL' to cancel[/dim]")
    console.print("┌" + "─" * 78 + "┐")

    lines = []
    line_number = 1

    try:
        while True:
            try:
                line = input(f"│ {line_number:2d}│ ")

                if line.strip().upper() == "END":
                    break
                elif line.strip().upper() == "CANCEL":
                    console.print("└" + "─" * 78 + "┘")
                    console.print("[yellow]Input cancelled[/yellow]")
                    return ""
                else:
                    lines.append(line)
                    line_number += 1

            except KeyboardInterrupt:
                console.print("\n└" + "─" * 78 + "┘")
                console.print("[yellow]Input cancelled[/yellow]")
                return ""

    except Exception as e:
        console.print(f"\n[red]Input error: {e}[/red]")
        return ""

    console.print("└" + "─" * 78 + "┘")

    result = "\n".join(lines).strip()
    if result:
        console.print(f"[dim]✅ Input received ({len(result)} characters, {len(lines)} lines)[/dim]")

    return result


class InteractiveConsole:
    """Interactive console for server control"""
    
    def __init__(self, app=None):
        self.app = app
        self.console = Console()
        self.running = False
        self.console_thread = None
        self.commands = {
            'help': self.cmd_help,
            'status': self.cmd_status,
            'models': self.cmd_models,
            'download': self.cmd_download,
            'load': self.cmd_load,
            'unload': self.cmd_unload,
            'info': self.cmd_info,
            'logs': self.cmd_logs,
            'config': self.cmd_config,
            'clear': self.cmd_clear,
            'stop': self.cmd_stop,
            'restart': self.cmd_restart,
            'plugins': self.cmd_plugins,
            'memory': self.cmd_memory,
            'exec': self.cmd_exec,
            'eval': self.cmd_eval,
            'history': self.cmd_history,
            'api': self.cmd_api,
            'test': self.cmd_test,
            'exit': self.cmd_exit,
            'quit': self.cmd_exit
        }
        
        # Command history
        self.command_history = []
        self.max_history = 50
    
    def start(self):
        """Start the interactive console in a separate thread"""
        if self.running:
            return
        
        self.running = True
        self.console_thread = threading.Thread(target=self._console_loop, daemon=True)
        self.console_thread.start()
        log_operation("console", "Interactive console started")
    
    def stop(self):
        """Stop the interactive console"""
        self.running = False
        if self.console_thread:
            self.console_thread.join(timeout=1.0)
        log_operation("console", "Interactive console stopped")
    
    def _console_loop(self):
        """Main console loop running in separate thread"""
        # Simple welcome message
        self.console.print()
        self.console.print("[bold bright_cyan]🎯 Interactive Console Ready[/bold bright_cyan] [dim]• Type 'help' for commands[/dim]")
        self.console.print()

        while self.running:
            try:
                # Beautiful compact prompt
                prompt_text = Text()
                prompt_text.append("reverie", style="bold bright_magenta")
                prompt_text.append("> ", style="bold bright_green")

                command_input = self.console.input(prompt_text).strip()
                
                if not command_input:
                    continue
                
                # Add to history
                self.command_history.append(command_input)
                if len(self.command_history) > self.max_history:
                    self.command_history.pop(0)
                
                # Parse and execute command
                asyncio.run(self._execute_command(command_input))
                
            except KeyboardInterrupt:
                self.console.print("\n[yellow]Use 'exit' to quit the console[/yellow]")
                continue
            except EOFError:
                break
            except Exception as e:
                self.console.print(f"[red]Console error: {e}[/red]")
    
    async def _execute_command(self, command_input: str):
        """Execute a command"""
        parts = command_input.split()
        if not parts:
            return
        
        cmd = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        if cmd in self.commands:
            try:
                await self.commands[cmd](args)
            except Exception as e:
                self.console.print(f"[red]Command error: {e}[/red]")
        else:
            self.console.print(f"[red]Unknown command: {cmd}[/red]")
            self.console.print("[dim]Type 'help' for available commands[/dim]")
    
    async def cmd_help(self, args):
        """Show help information"""

        # Show specific help for a command if provided
        if args and len(args) > 0:
            await self._show_command_help(args[0])
            return

        # Clear screen for better presentation
        self.console.clear()

        # Beautiful gradient header with enhanced styling
        header_text = Text()
        header_text.append("✨ ", style="bold bright_yellow")
        header_text.append("REVERIE", style="bold bright_magenta")
        header_text.append(" CODE STUDIO ", style="bold bright_cyan")
        header_text.append("✨", style="bold bright_yellow")

        subtitle_text = Text()
        subtitle_text.append("🎯 ", style="bold bright_green")
        subtitle_text.append("Interactive AI Agent Console", style="italic bright_blue")
        subtitle_text.append(" • ", style="dim white")
        subtitle_text.append("Server Control Interface", style="italic bright_white")
        subtitle_text.append(" 🎯", style="bold bright_green")

        # Status indicator
        status_text = Text()
        status_text.append("🟢 ", style="bold green")
        status_text.append("Server Running", style="bold green")
        status_text.append(" • ", style="dim white")
        status_text.append("Port 8000", style="dim bright_blue")
        status_text.append(" • ", style="dim white")
        status_text.append("Ready for Commands", style="dim bright_green")

        header_content = Group(
            Align.center(header_text),
            "",
            Align.center(subtitle_text),
            "",
            Align.center(status_text)
        )

        header_panel = Panel(
            header_content,
            border_style="bright_magenta",
            padding=(1, 2),
            title="[bold bright_yellow]⚡ REVERIE COMMAND CENTER ⚡[/bold bright_yellow]",
            title_align="center",
            subtitle="[dim]Type commands below or 'help <command>' for details[/dim]",
            subtitle_align="center"
        )
        self.console.print(header_panel)
        self.console.print()

        # Model Management Commands with enhanced styling
        model_table = Table(
            title="[bold bright_blue]🤖 AI MODEL MANAGEMENT[/bold bright_blue]",
            border_style="bright_blue",
            header_style="bold bright_cyan",
            title_style="bold bright_blue",
            show_header=True,
            show_lines=True,
            expand=True,
            padding=(0, 1)
        )
        model_table.add_column("🔧 Command", style="bold bright_cyan", width=14, justify="left")
        model_table.add_column("⚙️ Parameters", style="bright_yellow", width=26)
        model_table.add_column("📝 Description", style="bright_white", width=30)
        model_table.add_column("💡 Examples", style="dim bright_green", width=26)

        model_commands = [
            ("models", "[dim]popular | available[/]", "List models by category", "[cyan]models[/]\n[cyan]models popular[/]"),
            ("download", "[red]<model|url>[/] [yellow]--type[/] [dim]llm|t2i[/]", "Download model to ./models/", "[cyan]download default[/]\n[cyan]download https://huggingface.co/Qwen/Qwen2-7B[/]"),
            ("load", "[red]<model>[/] [yellow]backend[/] [green]--gpu|--cpu[/]", "Load model with device choice", "[cyan]load Jan-nano-128k vllm --gpu[/]\n[cyan]load llama-7b transformers --cpu[/]"),
            ("unload", "[dim]—[/]", "Unload current model", "[cyan]unload[/]"),
            ("info", "[yellow]model-name[/]", "Show detailed model info", "[cyan]info[/]\n[cyan]info Jan-nano-128k[/]")
        ]

        for cmd, options, desc, examples in model_commands:
            model_table.add_row(f"[bold bright_cyan]{cmd}[/]", options, desc, examples)

        self.console.print(model_table)
        self.console.print()

        # AI Testing Commands with enhanced styling
        test_table = Table(
            title="[bold bright_magenta]🧪 AI TESTING & VALIDATION[/bold bright_magenta]",
            border_style="bright_magenta",
            header_style="bold bright_cyan",
            title_style="bold bright_magenta",
            show_header=True,
            show_lines=True,
            expand=True,
            padding=(0, 1)
        )
        test_table.add_column("🔧 Command", style="bold bright_cyan", width=14, justify="left")
        test_table.add_column("⚙️ Parameters", style="bright_yellow", width=26)
        test_table.add_column("📝 Description", style="bright_white", width=30)
        test_table.add_column("💡 Examples", style="dim bright_green", width=26)

        test_commands = [
            ("test chat", "[yellow]<message>|multiline[/]", "Test chat functionality", "[cyan]test chat 'Hello'[/]\n[cyan]test chat[/] (multiline)"),
            ("test agent", "[yellow]<task>|multiline[/]", "Test agent capabilities", "[cyan]test agent 'Create file'[/]\n[cyan]test agent[/] (multiline)"),
            ("test models", "[dim]—[/]", "Test model system", "[cyan]test models[/]"),
            ("test api", "[dim]—[/]", "Test API endpoints", "[cyan]test api[/]"),
            ("test basic", "[dim]—[/]", "Run basic system tests", "[cyan]test basic[/]")
        ]

        for cmd, options, desc, examples in test_commands:
            test_table.add_row(f"[bold bright_cyan]{cmd}[/]", options, desc, examples)

        self.console.print(test_table)
        self.console.print()

        # Server Management Commands with enhanced styling
        server_table = Table(
            title="[bold bright_green]🚀 SERVER CONTROL[/bold bright_green]",
            border_style="bright_green",
            header_style="bold bright_cyan",
            title_style="bold bright_green",
            show_header=True,
            show_lines=True,
            expand=True,
            padding=(0, 1)
        )
        server_table.add_column("🔧 Command", style="bold bright_cyan", width=14, justify="left")
        server_table.add_column("⚙️ Parameters", style="bright_yellow", width=26)
        server_table.add_column("📝 Description", style="bright_white", width=30)
        server_table.add_column("💡 Examples", style="dim bright_green", width=26)

        server_commands = [
            ("status", "[dim]—[/]", "Server status & statistics", "[cyan]status[/]"),
            ("config", "[yellow]key value[/]", "View/modify configuration", "[cyan]config[/]\n[cyan]config debug true[/]"),
            ("memory", "[dim]—[/]", "Memory usage details", "[cyan]memory[/]"),
            ("logs", "[yellow]lines[/]", "Show recent log entries", "[cyan]logs[/]\n[cyan]logs 50[/]"),
            ("clear", "[dim]—[/]", "Clear console screen", "[cyan]clear[/]"),
            ("exit", "[dim]—[/]", "Exit console (server continues)", "[cyan]exit[/]")
        ]

        for cmd, options, desc, examples in server_commands:
            server_table.add_row(f"[bold bright_cyan]{cmd}[/]", options, desc, examples)

        self.console.print(server_table)
        self.console.print()

        # Development & API Commands with enhanced styling
        dev_table = Table(
            title="[bold bright_magenta]🔧 DEVELOPMENT & API[/bold bright_magenta]",
            border_style="bright_magenta",
            header_style="bold bright_cyan",
            title_style="bold bright_magenta",
            show_header=True,
            show_lines=True,
            expand=True,
            padding=(0, 1)
        )
        dev_table.add_column("🔧 Command", style="bold bright_cyan", width=14, justify="left")
        dev_table.add_column("⚙️ Parameters", style="bright_yellow", width=26)
        dev_table.add_column("📝 Description", style="bright_white", width=30)
        dev_table.add_column("💡 Examples", style="dim bright_green", width=26)

        dev_commands = [
            ("exec", "[red]<command>[/]", "Execute system command", "[cyan]exec nvidia-smi[/]\n[cyan]exec ls -la[/]"),
            ("eval", "[red]<expression>[/]", "Evaluate Python code", "[cyan]eval 2+2[/]\n[cyan]eval len('hello')[/]"),
            ("api", "[red]<endpoint>[/] [yellow]method[/]", "Test API endpoints", "[cyan]api /health[/]\n[cyan]api /models GET[/]"),
            ("test", "[red]<type>[/] [yellow]args[/]", "Run comprehensive tests", "[cyan]test chat 'Hello'[/]\n[cyan]test agent 'Create file'[/]")
        ]

        for cmd, options, desc, examples in dev_commands:
            dev_table.add_row(f"[bold bright_cyan]{cmd}[/]", options, desc, examples)

        self.console.print(dev_table)
        self.console.print()

        # Quick Reference Panel with enhanced workflow
        quick_ref = Panel(
            "[bold bright_yellow]💡 QUICK START WORKFLOW[/bold bright_yellow]\n\n"
            "[bright_green]1.[/bright_green] [cyan]download default[/cyan] - Download Jan-nano-128k model\n"
            "[bright_green]2.[/bright_green] [cyan]load Jan-nano-128k[/cyan] - Load model for inference\n"
            "[bright_green]3.[/bright_green] [cyan]test chat 'Hello'[/cyan] - Test chat functionality\n"
            "[bright_green]4.[/bright_green] [cyan]test agent 'Create file'[/cyan] - Test agent capabilities\n\n"
            "[bold bright_cyan]🎯 ESSENTIAL COMMANDS[/bold bright_cyan]\n\n"
            "[bright_green]•[/bright_green] [cyan]models popular[/cyan] - See popular models\n"
            "[bright_green]•[/bright_green] [cyan]status[/cyan] - Check server health\n"
            "[bright_green]•[/bright_green] [cyan]help <command>[/cyan] - Get detailed help\n"
            "[bright_green]•[/bright_green] [cyan]clear[/cyan] - Clear screen for better view",
            border_style="bright_yellow",
            padding=(1, 2),
            title="[bold bright_yellow]⚡ GETTING STARTED ⚡[/bold bright_yellow]",
            title_align="center"
        )

        self.console.print(quick_ref)
        self.console.print()

        # Enhanced footer with model paths and tips
        footer_panel = Panel(
            "[bold bright_yellow]📁 MODEL STORAGE PATHS[/bold bright_yellow]\n\n"
            "[bright_blue]🤖 LLM Models:[/bright_blue] [dim]./models/llm/[/dim] [bright_green](Jan-nano-128k, Llama, etc.)[/bright_green]\n"
            "[bright_magenta]🎨 T2I Models:[/bright_magenta] [dim]./models/t2i/[/dim] [bright_green](Stable Diffusion, etc.)[/bright_green]\n\n"
            "[bold bright_cyan]⚡ PERFORMANCE TIPS[/bold bright_cyan]\n\n"
            "[bright_green]•[/bright_green] Use [cyan]vLLM[/cyan] backend for faster inference\n"
            "[bright_green]•[/bright_green] Models auto-detect from filesystem\n"
            "[bright_green]•[/bright_green] Download shows real-time progress\n"
            "[bright_green]•[/bright_green] Type [cyan]clear[/cyan] anytime for clean view",
            border_style="dim bright_white",
            padding=(1, 2),
            title="[dim]💡 Additional Information[/dim]",
            title_align="center"
        )
        self.console.print(footer_panel)

        # Add a beautiful separator
        separator = Text()
        separator.append("─" * 100, style="dim bright_cyan")
        self.console.print(Align.center(separator))
        self.console.print()

    async def _show_command_help(self, command: str):
        """Show detailed help for a specific command"""
        command = command.lower()

        # Detailed command help information
        detailed_help = {
            "models": {
                "description": "List and manage available AI models",
                "usage": "models",
                "options": [],
                "examples": ["models"],
                "notes": "Shows both local and downloadable models with status indicators"
            },
            "download": {
                "description": "Download AI models from HuggingFace using Git clone for reliable model acquisition",
                "usage": "download <model-key | huggingface-url>",
                "options": [
                    "default - Download Jan-nano-128k (default model, ~2GB)",
                    "llama-7b - Download Llama 2 7B Chat (~13GB)",
                    "mistral-7b - Download Mistral 7B Instruct (~14GB)",
                    "codellama-7b - Download Code Llama 7B (~13GB)",
                    "phi-3-mini - Download Microsoft Phi-3 Mini (~2GB)",
                    "gemma-7b - Download Google Gemma 7B (~14GB)",
                    "<hf-url> - Download from any HuggingFace URL"
                ],
                "examples": [
                    "download default",
                    "download llama-7b",
                    "download mistral-7b",
                    "download https://huggingface.co/microsoft/DialoGPT-medium",
                    "download https://huggingface.co/Qwen/Qwen2-7B-Instruct"
                ],
                "notes": "Uses Git clone for reliable downloads. Models are cached locally. Use 'models popular' to see all available model keys."
            },
            "load": {
                "description": "Load an AI model into memory for inference with device selection",
                "usage": "load <model-name> [backend] [--gpu|--cpu|--device <device>]",
                "options": [
                    "<model-name> - Name of the model to load",
                    "backend: transformers (default) | vllm - Inference backend",
                    "--gpu/--cuda - Force GPU/CUDA device",
                    "--cpu - Force CPU device",
                    "--device <device> - Specify device (auto/cpu/gpu/cuda/mps)"
                ],
                "examples": [
                    "load Jan-nano-128k",
                    "load Jan-nano-128k vllm --gpu",
                    "load llama-7b transformers --cpu",
                    "load mistral-7b vllm --device cuda"
                ],
                "notes": "vLLM backend provides faster inference on GPU. Device selection optimizes performance and memory usage."
            },
            "exec": {
                "description": "Execute system commands and shell scripts",
                "usage": "exec <command> [arguments]",
                "options": [
                    "<command> - System command to execute",
                    "[arguments] - Command arguments and flags"
                ],
                "examples": [
                    "exec ls -la",
                    "exec nvidia-smi",
                    "exec python --version",
                    "exec git status"
                ],
                "notes": "Commands run with 30-second timeout. Use with caution."
            },
            "api": {
                "description": "Test and interact with server API endpoints",
                "usage": "api <endpoint> [method] [data]",
                "options": [
                    "<endpoint> - API endpoint path (e.g., /health, /models)",
                    "[method] - HTTP method (GET, POST, PUT, DELETE)",
                    "[data] - JSON data for POST/PUT requests"
                ],
                "examples": [
                    "api /health",
                    "api /models GET",
                    "api /chat POST",
                    "api /models/load POST"
                ],
                "notes": "Useful for testing API functionality and debugging"
            },
            "plugins": {
                "description": "Manage server plugins and extensions",
                "usage": "plugins <action> [plugin-name]",
                "options": [
                    "list - Show all available plugins",
                    "load <name> - Load a specific plugin",
                    "unload <name> - Unload a plugin",
                    "reload <name> - Reload a plugin"
                ],
                "examples": [
                    "plugins list",
                    "plugins load core-tools",
                    "plugins unload debug-helper"
                ],
                "notes": "Plugins extend server functionality dynamically"
            },
            "test": {
                "description": "Run comprehensive system tests and AI functionality tests with multiline input support",
                "usage": "test <type> [args]",
                "options": [
                    "chat [message] - Test chat functionality (supports multiline input)",
                    "agent [task] - Test agent capabilities (supports multiline input)",
                    "models - Test model system functionality",
                    "api - Test API endpoints",
                    "memory - Test memory system",
                    "basic - Run basic system tests"
                ],
                "examples": [
                    "test chat 'Hello, how are you?'",
                    "test chat (then enter multiline mode)",
                    "test agent 'Create a Python script'",
                    "test agent (then enter multiline mode)",
                    "test models"
                ],
                "notes": "Chat and agent tests support multiline input when no message is provided. Use 'END' to finish input, 'CANCEL' to abort. Features beautiful console formatting."
            }
        }

        if command in detailed_help:
            info = detailed_help[command]

            # Create detailed help panel
            help_content = f"[bold cyan]{command.upper()}[/bold cyan]\n\n"
            help_content += f"[bold]Description:[/bold]\n{info['description']}\n\n"
            help_content += f"[bold]Usage:[/bold]\n[yellow]{info['usage']}[/yellow]\n\n"

            if info['options']:
                help_content += f"[bold]Options:[/bold]\n"
                for option in info['options']:
                    help_content += f"  • {option}\n"
                help_content += "\n"

            help_content += f"[bold]Examples:[/bold]\n"
            for example in info['examples']:
                help_content += f"  [green]reverie>[/green] [cyan]{example}[/cyan]\n"

            if info.get('notes'):
                help_content += f"\n[bold]Notes:[/bold]\n[dim]{info['notes']}[/dim]"

            panel = Panel(
                help_content,
                title=f"📖 Command Help: {command}",
                border_style="blue",
                padding=(1, 2)
            )
            self.console.print(panel)
        else:
            self.console.print(f"[red]No detailed help available for command: {command}[/red]")
            self.console.print("[dim]Use 'help' to see all available commands[/dim]")

    async def cmd_status(self, args):
        """Show server status"""
        if not self.app:
            self.console.print("[red]App not available[/red]")
            return
        
        model_manager = getattr(self.app.state, 'model_manager', None)
        plugin_manager = getattr(self.app.state, 'plugin_manager', None)
        
        status_info = []
        status_info.append(f"[green]✅ Server Running[/green]")
        status_info.append(f"Host: {settings.server.host}:{settings.server.port}")
        
        if model_manager:
            if model_manager.is_model_loaded():
                model = model_manager.current_model
                status_info.append(f"[green]🤖 Model: {model.name} ({model.backend.value})[/green]")
            else:
                status_info.append("[yellow]🤖 No model loaded[/yellow]")
        
        if plugin_manager:
            active_plugins = len(plugin_manager.list_active_plugins())
            status_info.append(f"🔌 Plugins: {active_plugins} active")
        
        panel = Panel("\n".join(status_info), title="Server Status", border_style="blue")
        self.console.print(panel)
    
    async def cmd_models(self, args):
        """List available models with enhanced display"""

        # Handle different subcommands
        if args and args[0].lower() == "popular":
            await self._show_popular_models()
            return
        elif args and args[0].lower() == "available":
            await self._show_available_models()
            return
        else:
            # Show both popular and available models
            await self._show_popular_models()
            self.console.print()
            await self._show_available_models()
            return

    async def _show_popular_models(self):
        """Show popular predefined models"""
        from core.config import settings

        popular_table = Table(title="🌟 Popular Models (Quick Download)", border_style="yellow")
        popular_table.add_column("Key", style="cyan bold", width=15)
        popular_table.add_column("Model Name", style="white", width=25)
        popular_table.add_column("Description", style="dim", width=30)
        popular_table.add_column("Size", style="yellow", width=8)
        popular_table.add_column("Backend", style="green", width=12)

        for key, info in settings.models.popular_models.items():
            popular_table.add_row(
                key,
                info["name"],
                info["description"],
                info["size"],
                info["backend"]
            )

        self.console.print(popular_table)
        self.console.print("[dim]💡 Use 'download <key>' to download any of these models[/dim]")

    async def _show_available_models(self):
        """Show locally available models with enhanced filesystem detection"""
        if not self.app:
            self.console.print("[red]App not available[/red]")
            return

        model_manager = getattr(self.app.state, 'model_manager', None)
        if not model_manager:
            self.console.print("[red]Model manager not available[/red]")
            return

        cached_models = model_manager.list_cached_models()
        current_model = model_manager.current_model.name if model_manager.current_model else None

        # Show LLM models
        if cached_models["llm"]:
            llm_table = Table(title="🤖 LLM Models (./models/llm/)", border_style="blue")
            llm_table.add_column("Model Name", style="cyan bold", width=25)
            llm_table.add_column("Type", style="magenta", width=12)
            llm_table.add_column("Status", style="white", width=15)
            llm_table.add_column("Size", style="yellow", width=10)
            llm_table.add_column("Backend", style="green", width=10)
            llm_table.add_column("Actions", style="dim", width=20)

            for model in cached_models["llm"]:
                model_info = model_manager.get_model_info_from_disk(model, "llm")

                if model == current_model:
                    status = "🟢 Loaded"
                    actions = "unload | info"
                else:
                    status = "⚪ Available"
                    actions = "load | info | delete"

                size_str = f"{model_info['size_gb']:.1f}GB" if model_info else "Unknown"
                model_type = model_info['architecture'] if model_info else "Unknown"

                # Get backend from optimizations
                backend = "transformers"  # default
                if model in model_manager.model_optimizations:
                    backend = model_manager.model_optimizations[model]["recommended_backend"].value

                llm_table.add_row(model, model_type, status, size_str, backend, actions)

            self.console.print(llm_table)
            self.console.print(f"[dim]💡 Found {len(cached_models['llm'])} LLM model(s) in ./models/llm/[/dim]")

        # Show T2I models
        if cached_models["t2i"]:
            self.console.print()  # Add spacing
            t2i_table = Table(title="🎨 Text-to-Image Models (./models/t2i/)", border_style="purple")
            t2i_table.add_column("Model Name", style="cyan bold", width=25)
            t2i_table.add_column("Type", style="magenta", width=12)
            t2i_table.add_column("Status", style="white", width=15)
            t2i_table.add_column("Size", style="yellow", width=10)
            t2i_table.add_column("Backend", style="green", width=10)
            t2i_table.add_column("Actions", style="dim", width=20)

            for model in cached_models["t2i"]:
                model_info = model_manager.get_model_info_from_disk(model, "t2i")

                status = "⚪ Available"
                actions = "info | delete"
                size_str = f"{model_info['size_gb']:.1f}GB" if model_info else "Unknown"
                model_type = model_info['architecture'] if model_info else "Diffusion"

                t2i_table.add_row(model, model_type, status, size_str, "diffusers", actions)

            self.console.print(t2i_table)
            self.console.print(f"[dim]💡 Found {len(cached_models['t2i'])} T2I model(s) in ./models/t2i/[/dim]")

        # Show help if no models found
        if not cached_models["llm"] and not cached_models["t2i"]:
            no_models_panel = Panel(
                "[yellow]No models found in local directories[/yellow]\n\n"
                "[bold]Quick Start:[/bold]\n"
                "  • [cyan]download default[/cyan] - Download Jan-nano-128k (~4GB, vLLM)\n"
                "  • [cyan]models popular[/cyan] - See all available models\n"
                "  • [cyan]download llama-7b[/cyan] - Download Llama 2 7B Chat\n\n"
                "[bold]Directories:[/bold]\n"
                "  • [dim]./models/llm/[/dim] - Language models\n"
                "  • [dim]./models/t2i/[/dim] - Text-to-image models",
                title="📦 No Local Models",
                border_style="yellow",
                padding=(0, 1)
            )
            self.console.print(no_models_panel)
    
    async def cmd_download(self, args):
        """Download a model with enhanced support for popular models and HuggingFace URLs"""
        if not args:
            self.console.print("[red]Usage: download <model-key | huggingface-url> [--type llm|t2i][/red]")
            self.console.print("[yellow]Popular models:[/yellow]")
            self.console.print("  • [cyan]default[/cyan] - Jan-nano-128k (~4GB, vLLM)")
            self.console.print("  • [cyan]llama-7b[/cyan] - Llama 2 7B Chat (~13GB)")
            self.console.print("  • [cyan]mistral-7b[/cyan] - Mistral 7B Instruct (~14GB)")
            self.console.print("  • [cyan]codellama-7b[/cyan] - Code Llama 7B (~13GB)")
            self.console.print("  • [cyan]phi-3-mini[/cyan] - Microsoft Phi-3 Mini (~2GB)")
            self.console.print("  • [cyan]gemma-7b[/cyan] - Google Gemma 7B (~14GB)")
            self.console.print("[dim]Or use any HuggingFace URL: https://huggingface.co/model/name[/dim]")
            self.console.print("[dim]Add --type t2i for text-to-image models (saves to ./models/t2i/)[/dim]")
            return

        from core.config import settings

        # Parse arguments for model type
        model_type = "llm"  # default
        model_args = []

        for arg in args:
            if arg.startswith("--type"):
                if "=" in arg:
                    model_type = arg.split("=")[1].lower()
                else:
                    # Next argument should be the type
                    type_idx = args.index(arg)
                    if type_idx + 1 < len(args):
                        model_type = args[type_idx + 1].lower()
            elif not arg.startswith("--"):
                model_args.append(arg)

        if not model_args:
            self.console.print("[red]No model specified[/red]")
            return

        model_input = model_args[0].lower()
        model_url = None
        display_name = None
        model_info = None

        # Check if it's a popular model key
        if model_input in settings.models.popular_models:
            model_info = settings.models.popular_models[model_input]
            model_url = model_info["url"]
            display_name = f"{model_info['name']} ({model_info['description']})"

            # Show model information
            info_panel = Panel(
                f"[bold cyan]{model_info['name']}[/bold cyan]\n"
                f"[dim]{model_info['description']}[/dim]\n\n"
                f"[yellow]Size:[/yellow] {model_info['size']}\n"
                f"[yellow]Recommended Backend:[/yellow] {model_info['backend']}\n"
                f"[yellow]Source:[/yellow] {model_info['url']}",
                title="📦 Model Information",
                border_style="blue",
                padding=(0, 1)
            )
            self.console.print(info_panel)

        elif args[0].startswith(('http://', 'https://')):
            # Direct HuggingFace URL
            model_url = args[0]
            display_name = model_url.split('/')[-1]
        else:
            # Treat as model name and construct HuggingFace URL
            model_url = f"https://huggingface.co/{args[0]}"
            display_name = args[0]

        # Confirm download for large models
        if model_info and "GB" in model_info.get("size", ""):
            size_gb = float(model_info["size"].replace("~", "").replace("GB", ""))
            if size_gb > 5:
                self.console.print(f"[yellow]⚠️  This is a large model ({model_info['size']}). Download may take significant time.[/yellow]")

        self.console.print(f"[blue]📥 Starting download: {display_name}[/blue]")
        self.console.print(f"[dim]Using Git clone for reliable download...[/dim]")

        if not self.app:
            self.console.print("[red]App not available[/red]")
            return

        model_manager = getattr(self.app.state, 'model_manager', None)
        if not model_manager:
            self.console.print("[red]Model manager not available[/red]")
            return

        try:
            # Show progress indicator with model type info
            status_msg = f"[blue]Downloading {model_type.upper()} model to ./models/{model_type}/[/blue]"
            with self.console.status(status_msg, spinner="dots"):
                model_path = await model_manager.download_model(model_url, model_type=model_type)

            # Success message with next steps
            target_dir = f"./models/{model_type}/"
            success_panel = Panel(
                f"[green]✅ Download completed successfully![/green]\n\n"
                f"[yellow]Model Type:[/yellow] {model_type.upper()}\n"
                f"[yellow]Model Location:[/yellow] {target_dir}{Path(model_path).name}\n"
                f"[yellow]Next Steps:[/yellow]\n"
                f"  • Use [cyan]load {Path(model_path).name}[/cyan] to load the model (LLM only)\n"
                f"  • Use [cyan]models[/cyan] to see all available models\n"
                f"  • Use [cyan]info {Path(model_path).name}[/cyan] for model details",
                title="🎉 Download Complete",
                border_style="green",
                padding=(0, 1)
            )
            self.console.print(success_panel)

        except Exception as e:
            error_panel = Panel(
                f"[red]❌ Download failed: {str(e)}[/red]\n\n"
                f"[yellow]Troubleshooting:[/yellow]\n"
                f"  • Check your internet connection\n"
                f"  • Verify the model URL is correct\n"
                f"  • Ensure sufficient disk space\n"
                f"  • Try again with a different model",
                title="💥 Download Error",
                border_style="red",
                padding=(0, 1)
            )
            self.console.print(error_panel)

    async def cmd_delete(self, args):
        """Delete a model from disk"""
        if not args:
            self.console.print("[red]Usage: delete <model_name> [--type llm|t2i][/red]")
            self.console.print("[dim]Example: delete Jan-nano-128k[/dim]")
            self.console.print("[dim]Example: delete stable-diffusion --type t2i[/dim]")
            return

        # Parse arguments for model type
        model_type = "llm"  # default
        model_args = []

        for arg in args:
            if arg.startswith("--type"):
                if "=" in arg:
                    model_type = arg.split("=")[1].lower()
                else:
                    # Next argument should be the type
                    type_idx = args.index(arg)
                    if type_idx + 1 < len(args):
                        model_type = args[type_idx + 1].lower()
            elif not arg.startswith("--"):
                model_args.append(arg)

        if not model_args:
            self.console.print("[red]No model specified[/red]")
            return

        model_name = model_args[0]

        if not self.app:
            self.console.print("[red]App not available[/red]")
            return

        model_manager = getattr(self.app.state, 'model_manager', None)
        if not model_manager:
            self.console.print("[red]Model manager not available[/red]")
            return

        # Check if model exists
        model_info = model_manager.get_model_info_from_disk(model_name, model_type)
        if not model_info:
            self.console.print(f"[red]❌ Model '{model_name}' not found in {model_type} directory[/red]")
            return

        # Confirm deletion
        self.console.print(f"[yellow]⚠️  About to delete:[/yellow]")
        self.console.print(f"  Model: [cyan]{model_name}[/cyan]")
        self.console.print(f"  Type: [cyan]{model_type.upper()}[/cyan]")
        self.console.print(f"  Size: [cyan]{model_info['size_gb']:.1f}GB[/cyan]")
        self.console.print(f"  Path: [dim]{model_info['path']}[/dim]")

        confirm = input("\nType 'yes' to confirm deletion: ").strip().lower()
        if confirm != 'yes':
            self.console.print("[yellow]Deletion cancelled[/yellow]")
            return

        try:
            success = model_manager.delete_model(model_name, model_type)
            if success:
                self.console.print(f"[green]✅ Successfully deleted {model_name} from {model_type} directory[/green]")
            else:
                self.console.print(f"[red]❌ Failed to delete {model_name}[/red]")
        except Exception as e:
            self.console.print(f"[red]❌ Delete failed: {e}[/red]")

    async def cmd_load(self, args):
        """Load a model with optional device specification"""
        if not args:
            self.console.print("[red]Usage: load <model_name> [backend] [--gpu|--cpu|--device <device>][/red]")
            self.console.print("[dim]Examples:[/dim]")
            self.console.print("[dim]  load Jan-nano-128k transformers[/dim]")
            self.console.print("[dim]  load Jan-nano-128k vllm --gpu[/dim]")
            self.console.print("[dim]  load Jan-nano-128k transformers --cpu[/dim]")
            return

        # Parse arguments
        model_name = args[0]
        backend = "transformers"
        device = None

        # Parse remaining arguments
        i = 1
        while i < len(args):
            arg = args[i].lower()
            if arg in ["transformers", "vllm"]:
                backend = arg
            elif arg in ["--gpu", "--cuda"]:
                device = "gpu"
            elif arg == "--cpu":
                device = "cpu"
            elif arg == "--device" and i + 1 < len(args):
                device = args[i + 1]
                i += 1  # Skip next argument
            i += 1

        device_info = f" on {device}" if device else ""
        self.console.print(f"[blue]🚀 Loading: {model_name} with {backend}{device_info}[/blue]")
        
        if not self.app:
            self.console.print("[red]App not available[/red]")
            return
        
        model_manager = getattr(self.app.state, 'model_manager', None)
        if not model_manager:
            self.console.print("[red]Model manager not available[/red]")
            return
        
        try:
            from models.manager import ModelBackend
            backend_enum = ModelBackend.TRANSFORMERS if backend.lower() == "transformers" else ModelBackend.VLLM

            model_info = await model_manager.load_model(model_name, backend_enum, False, device)
            self.console.print(f"[green]✅ Model loaded: {model_info.name}[/green]")
            self.console.print(f"[dim]Backend: {model_info.backend.value}, Context: {model_info.context_length}[/dim]")
            if device:
                self.console.print(f"[dim]Device: {device}[/dim]")
        except Exception as e:
            self.console.print(f"[red]❌ Load failed: {e}[/red]")
    
    async def cmd_unload(self, args):
        """Unload current model"""
        if not self.app:
            self.console.print("[red]App not available[/red]")
            return
        
        model_manager = getattr(self.app.state, 'model_manager', None)
        if not model_manager:
            self.console.print("[red]Model manager not available[/red]")
            return
        
        if not model_manager.is_model_loaded():
            self.console.print("[yellow]No model currently loaded[/yellow]")
            return
        
        try:
            model_name = model_manager.current_model.name
            await model_manager.unload_model()
            self.console.print(f"[green]✅ Model unloaded: {model_name}[/green]")
        except Exception as e:
            self.console.print(f"[red]❌ Unload failed: {e}[/red]")
    
    async def cmd_info(self, args):
        """Show model information (current loaded or specific model)"""
        if not self.app:
            self.console.print("[red]App not available[/red]")
            return

        model_manager = getattr(self.app.state, 'model_manager', None)
        if not model_manager:
            self.console.print("[red]Model manager not available[/red]")
            return

        # If no args, show current loaded model
        if not args:
            if not model_manager.is_model_loaded():
                self.console.print("[yellow]No model currently loaded[/yellow]")
                self.console.print("[dim]Use 'info <model-name>' to see information about a specific model[/dim]")
                return

            model = model_manager.current_model
            info_table = Table(title="🤖 Currently Loaded Model", border_style="green")
            info_table.add_column("Property", style="cyan bold", width=20)
            info_table.add_column("Value", style="white", width=50)

            info_table.add_row("Name", model.name)
            info_table.add_row("Backend", model.backend.value)
            info_table.add_row("Path", model.model_path)
            info_table.add_row("Context Length", str(model.context_length))
            info_table.add_row("Load Time", f"{getattr(model, 'load_time', 0):.2f}s")
            info_table.add_row("Status", "🟢 Loaded" if model.loaded else "🔴 Not Loaded")

            self.console.print(info_table)
            return

        # Show info for specific model
        model_name = args[0]
        model_type = args[1] if len(args) > 1 else "llm"  # default to llm

        # Try both types if not specified
        model_info = model_manager.get_model_info_from_disk(model_name, model_type)
        if not model_info and model_type == "llm":
            model_info = model_manager.get_model_info_from_disk(model_name, "t2i")
            model_type = "t2i"

        if not model_info:
            self.console.print(f"[red]❌ Model '{model_name}' not found in local directories[/red]")
            self.console.print("[dim]Use 'models' to see available models[/dim]")
            return

        # Display detailed model information
        info_table = Table(title=f"📊 Model Information: {model_name}", border_style="blue")
        info_table.add_column("Property", style="cyan bold", width=25)
        info_table.add_column("Value", style="white", width=45)

        info_table.add_row("Model Name", model_info["name"])
        info_table.add_row("Type", model_type.upper())
        info_table.add_row("Architecture", model_info["architecture"])
        info_table.add_row("Size (GB)", f"{model_info['size_gb']:.2f}")
        info_table.add_row("Size (Bytes)", f"{model_info['size_bytes']:,}")
        info_table.add_row("Path", model_info["path"])

        if model_type == "llm":
            info_table.add_row("Vocab Size", f"{model_info['vocab_size']:,}" if model_info['vocab_size'] else "Unknown")
            info_table.add_row("Hidden Size", str(model_info['hidden_size']) if model_info['hidden_size'] else "Unknown")
            info_table.add_row("Layers", str(model_info['num_layers']) if model_info['num_layers'] else "Unknown")
            info_table.add_row("Max Context", f"{model_info['max_position_embeddings']:,}" if model_info['max_position_embeddings'] else "Unknown")

            # Show recommended backend
            if model_name in model_manager.model_optimizations:
                backend = model_manager.model_optimizations[model_name]["recommended_backend"].value
                info_table.add_row("Recommended Backend", backend)

        self.console.print(info_table)

        # Show configuration details if available
        if "config" in model_info and model_info["config"]:
            config = model_info["config"]
            self.console.print()

            config_table = Table(title="⚙️ Model Configuration", border_style="yellow")
            config_table.add_column("Parameter", style="yellow bold", width=25)
            config_table.add_column("Value", style="white", width=45)

            # Show key config parameters
            key_params = [
                ("model_type", "Model Type"),
                ("torch_dtype", "Data Type"),
                ("transformers_version", "Transformers Version"),
                ("use_cache", "Use Cache"),
                ("pad_token_id", "Pad Token ID"),
                ("eos_token_id", "EOS Token ID"),
                ("bos_token_id", "BOS Token ID")
            ]

            for param, display_name in key_params:
                if param in config:
                    config_table.add_row(display_name, str(config[param]))

            self.console.print(config_table)
    
    async def cmd_logs(self, args):
        """Show recent logs"""
        lines = int(args[0]) if args and args[0].isdigit() else 10
        self.console.print(f"[dim]Showing last {lines} log entries...[/dim]")
        # This would need integration with the logging system
        self.console.print("[yellow]Log viewing not yet implemented[/yellow]")
    
    async def cmd_config(self, args):
        """Show configuration"""
        config_table = Table(title="⚙️ Server Configuration")
        config_table.add_column("Setting", style="cyan")
        config_table.add_column("Value", style="white")
        
        config_table.add_row("Host", settings.server.host)
        config_table.add_row("Port", str(settings.server.port))
        config_table.add_row("Debug", str(settings.server.debug))
        config_table.add_row("Default Model", settings.models.default_model)
        config_table.add_row("Max Tokens", str(settings.models.max_tokens))
        config_table.add_row("Temperature", str(settings.models.temperature))
        config_table.add_row("Log Level", settings.logging.level)
        
        self.console.print(config_table)
    
    async def cmd_memory(self, args):
        """Show memory usage"""
        import psutil
        import torch
        
        # System memory
        memory = psutil.virtual_memory()
        
        memory_table = Table(title="💾 Memory Usage")
        memory_table.add_column("Type", style="cyan")
        memory_table.add_column("Used", style="white")
        memory_table.add_column("Total", style="white")
        memory_table.add_column("Percentage", style="yellow")
        
        memory_table.add_row(
            "System RAM",
            f"{memory.used / (1024**3):.1f} GB",
            f"{memory.total / (1024**3):.1f} GB",
            f"{memory.percent:.1f}%"
        )
        
        # GPU memory if available
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.memory_allocated() / (1024**3)
            gpu_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            gpu_percent = (gpu_memory / gpu_total) * 100
            
            memory_table.add_row(
                "GPU Memory",
                f"{gpu_memory:.1f} GB",
                f"{gpu_total:.1f} GB",
                f"{gpu_percent:.1f}%"
            )
        
        self.console.print(memory_table)
    
    async def cmd_plugins(self, args):
        """Manage plugins"""
        if not self.app:
            self.console.print("[red]App not available[/red]")
            return
        
        plugin_manager = getattr(self.app.state, 'plugin_manager', None)
        if not plugin_manager:
            self.console.print("[red]Plugin manager not available[/red]")
            return
        
        if not args or args[0] == "list":
            active_plugins = plugin_manager.list_active_plugins()
            loaded_plugins = plugin_manager.list_loaded_plugins()
            
            if active_plugins or loaded_plugins:
                plugin_table = Table(title="🔌 Plugins")
                plugin_table.add_column("Name", style="cyan")
                plugin_table.add_column("Status", style="white")
                
                for plugin in loaded_plugins:
                    status = "🟢 Active" if plugin in active_plugins else "🟡 Loaded"
                    plugin_table.add_row(plugin, status)
                
                self.console.print(plugin_table)
            else:
                self.console.print("[yellow]No plugins loaded[/yellow]")
        else:
            self.console.print("[red]Usage: plugins [list][/red]")
    
    async def cmd_clear(self, args):
        """Clear console"""
        self.console.clear()
    
    async def cmd_stop(self, args):
        """Stop server"""
        self.console.print("[yellow]⚠️ Stopping server...[/yellow]")
        log_operation("console", "Server stop requested from console")
        # This would need integration with the server shutdown
        import os
        os._exit(0)
    
    async def cmd_restart(self, args):
        """Restart server"""
        self.console.print("[yellow]🔄 Server restart not implemented[/yellow]")
        self.console.print("[dim]Use 'stop' and manually restart[/dim]")
    
    async def cmd_exec(self, args):
        """Execute system command"""
        if not args:
            self.console.print("[red]Usage: exec <command>[/red]")
            self.console.print("[dim]Example: exec ls -la[/dim]")
            return

        command = " ".join(args)
        self.console.print(f"[blue]🔧 Executing: {command}[/blue]")

        try:
            import subprocess
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.stdout:
                self.console.print("[green]Output:[/green]")
                self.console.print(result.stdout)

            if result.stderr:
                self.console.print("[red]Error:[/red]")
                self.console.print(result.stderr)

            self.console.print(f"[dim]Exit code: {result.returncode}[/dim]")

        except subprocess.TimeoutExpired:
            self.console.print("[red]❌ Command timed out (30s limit)[/red]")
        except Exception as e:
            self.console.print(f"[red]❌ Execution failed: {e}[/red]")

    async def cmd_eval(self, args):
        """Evaluate Python expression"""
        if not args:
            self.console.print("[red]Usage: eval <expression>[/red]")
            self.console.print("[dim]Example: eval 2+2[/dim]")
            return

        expression = " ".join(args)
        self.console.print(f"[blue]🐍 Evaluating: {expression}[/blue]")

        try:
            # Safe evaluation with limited scope
            safe_dict = {
                "__builtins__": {},
                "abs": abs, "round": round, "min": min, "max": max,
                "len": len, "sum": sum, "sorted": sorted,
                "range": range, "enumerate": enumerate,
                "str": str, "int": int, "float": float, "bool": bool,
                "list": list, "dict": dict, "set": set, "tuple": tuple
            }

            result = eval(expression, safe_dict)
            self.console.print(f"[green]Result: {result}[/green]")
            self.console.print(f"[dim]Type: {type(result).__name__}[/dim]")

        except Exception as e:
            self.console.print(f"[red]❌ Evaluation failed: {e}[/red]")

    async def cmd_history(self, args):
        """Show command history"""
        lines = int(args[0]) if args and args[0].isdigit() else len(self.command_history)
        lines = min(lines, len(self.command_history))

        if not self.command_history:
            self.console.print("[yellow]No command history[/yellow]")
            return

        history_table = Table(title=f"📜 Command History (last {lines})")
        history_table.add_column("#", style="dim", width=4)
        history_table.add_column("Command", style="cyan")

        recent_history = self.command_history[-lines:]
        for i, cmd in enumerate(recent_history, 1):
            history_table.add_row(str(i), cmd)

        self.console.print(history_table)

    async def cmd_api(self, args):
        """Test API endpoints"""
        if not args:
            self.console.print("[red]Usage: api <endpoint>[/red]")
            self.console.print("[dim]Example: api /health[/dim]")
            return

        endpoint = args[0]
        if not endpoint.startswith('/'):
            endpoint = '/' + endpoint

        self.console.print(f"[blue]🌐 Testing API: {endpoint}[/blue]")

        try:
            import httpx
            url = f"http://{settings.server.host}:{settings.server.port}{endpoint}"

            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=10.0)

                status_color = "green" if 200 <= response.status_code < 300 else "red"
                self.console.print(f"[{status_color}]Status: {response.status_code}[/{status_color}]")

                if response.headers.get('content-type', '').startswith('application/json'):
                    try:
                        json_data = response.json()
                        self.console.print("[green]Response:[/green]")
                        self.console.print_json(data=json_data)
                    except:
                        self.console.print(response.text[:500])
                else:
                    self.console.print(response.text[:500])

        except Exception as e:
            self.console.print(f"[red]❌ API test failed: {e}[/red]")

    async def cmd_test(self, args):
        """Run system tests"""
        if not args:
            self.console.print("[red]Usage: test <type> [options][/red]")
            self.console.print("[dim]Available tests: chat, agent, models, api, memory, basic[/dim]")
            return

        test_type = args[0].lower()
        test_args = args[1:] if len(args) > 1 else []

        if test_type == "chat":
            await self._test_chat(test_args)
        elif test_type == "agent":
            await self._test_agent(test_args)
        elif test_type == "models":
            await self._test_models()
        elif test_type == "api":
            await self._test_api()
        elif test_type == "memory":
            await self._test_memory()
        elif test_type == "basic":
            await self._test_basic()
        else:
            self.console.print(f"[red]Unknown test type: {test_type}[/red]")
            self.console.print("[dim]Available tests: chat, agent, models, api, memory, basic[/dim]")

    async def _test_basic(self):
        """Run basic system tests"""
        tests = [
            ("Server Status", lambda: self.app is not None),
            ("Model Manager", lambda: hasattr(self.app.state, 'model_manager')),
            ("Plugin Manager", lambda: hasattr(self.app.state, 'plugin_manager')),
            ("Console Active", lambda: self.running)
        ]

        test_table = Table(title="🧪 Basic System Tests")
        test_table.add_column("Test", style="cyan")
        test_table.add_column("Result", style="white")

        for test_name, test_func in tests:
            try:
                result = test_func()
                status = "✅ PASS" if result else "❌ FAIL"
            except Exception as e:
                status = f"❌ ERROR: {e}"

            test_table.add_row(test_name, status)

        self.console.print(test_table)

    async def _test_chat(self, args):
        """Test chat functionality with the loaded model"""
        if not args:
            # Use multiline input for chat
            message = get_simple_multiline_input("💬 Enter your chat message")
            if not message:
                return
        else:
            # Join all arguments to form the message
            message = " ".join(args)

        # Remove quotes if present
        if message.startswith('"') and message.endswith('"'):
            message = message[1:-1]
        elif message.startswith("'") and message.endswith("'"):
            message = message[1:-1]

        if not self.app:
            self.console.print("[red]❌ Server app not available[/red]")
            return

        model_manager = getattr(self.app.state, 'model_manager', None)
        if not model_manager:
            self.console.print("[red]❌ Model manager not available[/red]")
            return

        if not model_manager.is_model_loaded():
            self.console.print("[red]❌ No model is currently loaded[/red]")
            self.console.print("[dim]Load a model first using: load <model_name>[/dim]")
            return

        # Display beautiful test header
        self.console.print("\n" + "═" * 80)
        self.console.print("[bold bright_magenta]💬 Chat Test Session[/bold bright_magenta]")
        self.console.print("═" * 80)

        # Show test details
        current_model = model_manager.current_model
        self.console.print(f"[dim]🤖 Model:[/dim] [cyan]{current_model.name}[/cyan]")
        self.console.print(f"[dim]⚙️ Backend:[/dim] [yellow]{current_model.backend.value}[/yellow]")
        self.console.print(f"[dim]📝 Message:[/dim] [white]{message}[/white]")
        self.console.print("─" * 80)

        try:
            # Generate response
            self.console.print("[bright_magenta]🔄 Generating response...[/bright_magenta]")
            self.console.print()

            # Initialize response accumulator
            response_text = ""
            live_display = Live("", refresh_per_second=10)

            with live_display:
                async for chunk in model_manager.generate_text(
                    message,
                    max_tokens=120000,  # Use full context capacity
                    temperature=0.7,
                    stream=True
                ):
                    response_text += chunk
                    # Update live display with full response so far
                    live_display.update(Panel(
                        response_text,
                        title="[bright_magenta]🤖 Response[/bright_magenta]",
                        border_style="bright_magenta",
                        padding=(1, 2),
                        expand=True
                    ))

            # Display final response with beautiful formatting
            self.console.print("\n[bright_magenta]🤖 Final Response:[/bright_magenta]")
            self.console.print(Panel(
                response_text,
                border_style="bright_magenta",
                padding=(1, 2),
                expand=True
            ))

            self.console.print(f"[dim]✨ Response generated successfully[/dim]")

        except Exception as e:
            self.console.print(f"[red]❌ Chat test failed: {e}[/red]")

        self.console.print("═" * 80 + "\n")

    async def _test_agent(self, args):
        """Test agent functionality with comprehensive tool calling"""
        if not args:
            # Use multiline input for agent tasks
            task_description = get_simple_multiline_input("🤖 Enter your agent task description")
            if not task_description:
                return
        else:
            # Join all arguments to form the task description
            task_description = " ".join(args)

        # Remove quotes if present
        if task_description.startswith('"') and task_description.endswith('"'):
            task_description = task_description[1:-1]
        elif task_description.startswith("'") and task_description.endswith("'"):
            task_description = task_description[1:-1]

        if not self.app:
            self.console.print("[red]❌ Server app not available[/red]")
            return

        # Get required services
        model_manager = getattr(self.app.state, 'model_manager', None)
        agent_engine = getattr(self.app.state, 'agent_engine', None)
        tool_manager = getattr(self.app.state, 'tool_manager', None)

        if not model_manager:
            self.console.print("[red]❌ Model manager not available[/red]")
            return

        if not agent_engine:
            self.console.print("[red]❌ Agent engine not available[/red]")
            return

        if not tool_manager:
            self.console.print("[red]❌ Tool manager not available[/red]")
            return

        if not model_manager.is_model_loaded():
            self.console.print("[red]❌ No model is currently loaded[/red]")
            self.console.print("[dim]Load a model first using: load <model_name>[/dim]")
            return

        # Display beautiful agent test header
        self.console.print("\n" + "═" * 90)
        self.console.print("[bold bright_magenta]🤖 Agent Test Session[/bold bright_magenta]")
        self.console.print("═" * 90)

        # Show test details
        current_model = model_manager.current_model
        self.console.print(f"[dim]🤖 Model:[/dim] [cyan]{current_model.name}[/cyan]")
        self.console.print(f"[dim]⚙️ Backend:[/dim] [yellow]{current_model.backend.value}[/yellow]")
        self.console.print(f"[dim]📋 Task:[/dim] [white]{task_description}[/white]")
        self.console.print("─" * 90)

        try:
            # Create and execute agent task
            self.console.print("[bright_magenta]🚀 Creating agent task...[/bright_magenta]")

            task = await agent_engine.create_task(
                description=task_description,
                user_id="console_test"
            )

            self.console.print(f"[green]✅ Task created with ID: {task.task_id}[/green]")

            # Execute the task with streaming output
            self.console.print("[bright_magenta]⚡ Executing agent task...[/bright_magenta]")
            self.console.print()

            # Initialize response accumulator
            response_text = ""
            live_display = Live("", refresh_per_second=10)

            with live_display:
                async for chunk in agent_engine.execute_task_stream(task.task_id):
                    if isinstance(chunk, dict) and 'type' in chunk:
                        if chunk['type'] == 'status':
                            live_display.update(f"[yellow]{chunk['status']}...[/yellow]")
                        elif chunk['type'] == 'tool_call':
                            live_display.update(Panel(
                                f"[cyan]Using tool:[/cyan] {chunk['tool']}\n[dim]{chunk['args']}[/dim]",
                                title="[bright_cyan]🔧 Tool Execution[/bright_cyan]",
                                border_style="bright_cyan",
                                padding=(1, 2)
                            ))
                        elif chunk['type'] == 'tool_result':
                            live_display.update(Panel(
                                chunk['result'],
                                title="[bright_green]✅ Tool Result[/bright_green]",
                                border_style="bright_green",
                                padding=(1, 2)
                            ))
                    elif isinstance(chunk, str):
                        response_text += chunk
                        live_display.update(Panel(
                            response_text,
                            title="[bright_magenta]🤖 Agent Response[/bright_magenta]",
                            border_style="bright_magenta",
                            padding=(1, 2),
                            expand=True
                        ))

            # Display final response with beautiful formatting
            self.console.print("\n[bright_magenta]🤖 Final Response:[/bright_magenta]")
            self.console.print(Panel(
                response_text,
                border_style="bright_magenta",
                padding=(1, 2),
                expand=True
            ))

            self.console.print(f"[dim]✨ Task completed successfully[/dim]")

        except Exception as e:
            self.console.print(f"[red]❌ Agent test failed: {e}[/red]")

        self.console.print("═" * 90 + "\n")

    async def _display_agent_results(self, task):
        """Display agent task results with beautiful formatting and light purple aesthetic"""

        # Create beautiful header with gradient effect
        header_panel = Panel(
            "[bold bright_magenta]🤖 AGENT EXECUTION RESULTS[/bold bright_magenta]\n"
            "[dim bright_magenta]Comprehensive AI Agent Task Analysis[/dim bright_magenta]",
            border_style="bright_magenta",
            padding=(1, 2),
            title="[bold bright_yellow]⚡ REVERIE AGENT ⚡[/bold bright_yellow]",
            title_align="center"
        )
        self.console.print(header_panel)

        # Task summary with enhanced styling
        status_color = "green" if task.status.value == "completed" else "red" if task.status.value == "failed" else "yellow"
        status_emoji = "✅" if task.status.value == "completed" else "❌" if task.status.value == "failed" else "⏳"

        summary_table = Table(
            title="[bold bright_cyan]📊 Task Summary[/bold bright_cyan]",
            border_style="bright_cyan",
            show_header=False,
            expand=True,
            padding=(0, 1)
        )
        summary_table.add_column("Property", style="dim", width=15)
        summary_table.add_column("Value", style="bright_white", width=50)

        summary_table.add_row(f"{status_emoji} Status", f"[{status_color}]{task.status.value.upper()}[/{status_color}]")
        summary_table.add_row("🔢 Steps", f"[cyan]{len(task.steps)}[/cyan]")
        summary_table.add_row("📈 Progress", f"[yellow]{task.progress:.1f}%[/yellow]")
        summary_table.add_row("⏱️ Duration", f"[dim]{(task.completed_at - task.started_at).total_seconds():.2f}s[/dim]" if task.completed_at and task.started_at else "[dim]N/A[/dim]")

        self.console.print(summary_table)

        # Step details with enhanced formatting
        if task.steps:
            self.console.print()

            steps_table = Table(
                title="[bold bright_green]📝 Step Execution Details[/bold bright_green]",
                border_style="bright_green",
                show_header=True,
                expand=True,
                padding=(0, 1)
            )
            steps_table.add_column("#", style="dim", width=3, justify="center")
            steps_table.add_column("Step Description", style="bright_white", width=35)
            steps_table.add_column("Type", style="cyan", width=15)
            steps_table.add_column("Status", style="white", width=12)
            steps_table.add_column("Tools Used", style="yellow", width=20)

            for i, step in enumerate(task.steps, 1):
                step_status_color = "green" if step.status.value == "completed" else "red" if step.status.value == "failed" else "yellow"
                step_status_emoji = "✅" if step.status.value == "completed" else "❌" if step.status.value == "failed" else "⏳"

                # Extract tool information
                tools_used = []
                if step.output_data and "tool_results" in step.output_data:
                    for tool_result in step.output_data["tool_results"]:
                        tool_name = tool_result.get("tool", "unknown")
                        tools_used.append(f"🔧 {tool_name}")

                tools_display = "\n".join(tools_used) if tools_used else "[dim]None[/dim]"

                steps_table.add_row(
                    str(i),
                    step.description[:35] + "..." if len(step.description) > 35 else step.description,
                    step.step_type.value,
                    f"{step_status_emoji} [{step_status_color}]{step.status.value}[/{step_status_color}]",
                    tools_display
                )

            self.console.print(steps_table)

            # Detailed tool usage section
            tool_usage_found = False
            for step in task.steps:
                if step.output_data and "tool_results" in step.output_data:
                    if not tool_usage_found:
                        self.console.print()
                        tool_panel = Panel(
                            self._format_tool_usage(task.steps),
                            title="[bold bright_yellow]🔧 Tool Usage Analysis[/bold bright_yellow]",
                            border_style="bright_yellow",
                            padding=(1, 2)
                        )
                        self.console.print(tool_panel)
                        tool_usage_found = True
                    break

        # Show final result if available
        if task.result:
            self.console.print()
            result_panel = Panel(
                str(task.result),
                title="[bold bright_cyan]🎯 Final Result[/bold bright_cyan]",
                border_style="bright_cyan",
                padding=(1, 2)
            )
            self.console.print(result_panel)

    def _format_tool_usage(self, steps):
        """Format tool usage information with beautiful hierarchy"""
        content = ""

        for i, step in enumerate(steps, 1):
            if step.output_data and "tool_results" in step.output_data:
                content += f"[bold bright_cyan]Step {i}:[/bold bright_cyan] {step.description[:50]}...\n"

                for tool_result in step.output_data["tool_results"]:
                    tool_name = tool_result.get("tool", "unknown")
                    parameters = tool_result.get("parameters", {})

                    # Tool header with emoji
                    tool_emoji = self._get_tool_emoji(tool_name)
                    content += f"  {tool_emoji} [yellow]{tool_name}[/yellow]\n"

                    # Show specific information based on tool type
                    if tool_name in ["file_read", "file_write", "create_file", "delete_file"]:
                        file_path = parameters.get("path", "unknown")
                        content += f"    📁 [dim]File:[/dim] [white]{file_path}[/white]\n"

                        if tool_name == "file_write" and "content" in parameters:
                            content_preview = parameters["content"][:50] + "..." if len(parameters["content"]) > 50 else parameters["content"]
                            content += f"    💻 [dim]Content:[/dim] [bright_white]{content_preview}[/bright_white]\n"

                    elif tool_name == "web_search":
                        query = parameters.get("query", "unknown")
                        max_results = parameters.get("max_results", 5)
                        content += f"    🌐 [dim]Query:[/dim] [white]{query}[/white]\n"
                        content += f"    🔢 [dim]Max Results:[/dim] [cyan]{max_results}[/cyan]\n"

                    elif tool_name == "command_execute":
                        command = parameters.get("command", "unknown")
                        content += f"    💻 [dim]Command:[/dim] [white]{command}[/white]\n"

                    # Show result preview
                    result = tool_result.get("result", {})
                    if isinstance(result, dict) and "success" in result:
                        success_emoji = "✅" if result["success"] else "❌"
                        content += f"    {success_emoji} [dim]Status:[/dim] {'Success' if result['success'] else 'Failed'}\n"

                    content += "\n"

        return content.strip() if content else "[dim]No tool usage detected[/dim]"

    def _get_tool_emoji(self, tool_name):
        """Get appropriate emoji for tool type"""
        emoji_map = {
            "file_read": "📖",
            "file_write": "✏️",
            "create_file": "📝",
            "delete_file": "🗑️",
            "web_search": "🌐",
            "command_execute": "💻",
            "code_analyze": "🔍",
            "find_in_files": "🔎",
            "project_analyze": "📊"
        }
        return emoji_map.get(tool_name, "🔧")

    async def _test_models(self):
        """Test model system"""
        if not self.app:
            self.console.print("[red]App not available[/red]")
            return

        model_manager = getattr(self.app.state, 'model_manager', None)
        if not model_manager:
            self.console.print("[red]Model manager not available[/red]")
            return

        tests = [
            ("Model Manager Available", lambda: model_manager is not None),
            ("Cache Directory Exists", lambda: model_manager.models_cache_dir.exists()),
            ("Models Listed", lambda: len(model_manager.list_cached_models()) >= 0),
            ("Current Model Status", lambda: model_manager.current_model is not None)
        ]

        test_table = Table(title="🤖 Model System Tests")
        test_table.add_column("Test", style="cyan")
        test_table.add_column("Result", style="white")

        for test_name, test_func in tests:
            try:
                result = test_func()
                status = "✅ PASS" if result else "⚠️ SKIP"
            except Exception as e:
                status = f"❌ ERROR: {e}"

            test_table.add_row(test_name, status)

        self.console.print(test_table)

    async def _test_api(self):
        """Test API endpoints"""
        endpoints = ["/health", "/api/v1/models", "/api/v1/status"]

        test_table = Table(title="🌐 API Endpoint Tests")
        test_table.add_column("Endpoint", style="cyan")
        test_table.add_column("Status", style="white")

        for endpoint in endpoints:
            try:
                import httpx
                url = f"http://{settings.server.host}:{settings.server.port}{endpoint}"

                async with httpx.AsyncClient() as client:
                    response = await client.get(url, timeout=5.0)

                    if 200 <= response.status_code < 300:
                        status = f"✅ {response.status_code}"
                    else:
                        status = f"⚠️ {response.status_code}"

            except Exception as e:
                status = f"❌ ERROR"

            test_table.add_row(endpoint, status)

        self.console.print(test_table)

    async def _test_memory(self):
        """Test memory usage"""
        import psutil

        memory = psutil.virtual_memory()

        tests = [
            ("System Memory Available", memory.available > 1024**3),  # > 1GB
            ("Memory Usage < 90%", memory.percent < 90),
            ("Swap Usage < 50%", psutil.swap_memory().percent < 50)
        ]

        test_table = Table(title="💾 Memory Tests")
        test_table.add_column("Test", style="cyan")
        test_table.add_column("Result", style="white")

        for test_name, result in tests:
            status = "✅ PASS" if result else "⚠️ WARNING"
            test_table.add_row(test_name, status)

        self.console.print(test_table)

    async def cmd_exit(self, args):
        """Exit console"""
        self.console.print("[yellow]👋 Exiting interactive console...[/yellow]")
        self.running = False
