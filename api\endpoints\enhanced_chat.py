"""
Enhanced Agent API endpoints with comprehensive Augment-equivalent capabilities
Supports OpenAI, <PERSON>, <PERSON><PERSON><PERSON>, and local server with unified interface and tool calling
"""

import asyncio
import json
from typing import List, Optional, Dict, Any, AsyncGenerator, Union
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from datetime import datetime
import time
import uuid

from models.manager import ModelManager
from agent.context_engine import ReverieContextEngine, ContextType
from agent.engine import AgentEngine
from tools.manager import ToolManager
from core.config import settings
from core.logging import logger
from core.exceptions import ModelException

# Server-side API provider implementations
import openai
import anthropic
from zhipuai import ZhipuAI
import httpx

router = APIRouter()


class AgentMessage(BaseModel):
    """Agent message model with tool calling support"""
    role: str  # "user", "assistant", "system", "tool"
    content: str
    timestamp: Optional[str] = None
    provider: Optional[str] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None
    name: Optional[str] = None  # For tool messages


class ChatRequest(BaseModel):
    """Basic chat request model for simple conversations with web engine support"""
    messages: List[AgentMessage]
    provider: str = "local"  # "openai", "claude", "zhipu", "local"
    model: Optional[str] = "Jan-nano-128k"
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    temperature: float = 0.7
    max_tokens: int = 120000  # Set to model's full context capacity
    top_p: float = 0.9
    stream: bool = True
    enable_tools: bool = True  # Enable web search and other tools
    context: Optional[Dict[str, Any]] = None


class AgentRequest(BaseModel):
    """Comprehensive agent request model"""
    messages: List[AgentMessage]
    provider: str = "local"  # "openai", "claude", "zhipu", "local"
    model: Optional[str] = None
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    temperature: float = 0.7
    max_tokens: int = 120000  # Set to model's full context capacity
    stream: bool = True
    enable_tools: bool = True
    tools: Optional[List[Dict[str, Any]]] = None
    user_id: str = "default"
    session_id: Optional[str] = None


class AgentResponse(BaseModel):
    """Agent response model"""
    message: AgentMessage
    provider: str
    model: str
    usage: Optional[Dict[str, Any]] = None
    tool_results: Optional[List[Dict[str, Any]]] = None
    context_used: Optional[List[str]] = None


class ToolCallResult(BaseModel):
    """Tool call execution result"""
    tool_call_id: str
    tool_name: str
    result: Any
    success: bool
    error: Optional[str] = None
    execution_time: float


# Server-side API provider classes
class ServerAPIProvider:
    """Base class for server-side API providers"""

    def __init__(self, api_key: str = None, base_url: str = None):
        self.api_key = api_key
        self.base_url = base_url
        self.client = None

    async def initialize(self):
        """Initialize the provider"""
        pass

    async def get_models(self) -> List[str]:
        """Get available models"""
        return []

    async def chat(self, messages: List[Dict[str, Any]], **kwargs) -> AsyncGenerator[str, None]:
        """Chat completion with streaming"""
        yield ""


class OpenAIProvider(ServerAPIProvider):
    """OpenAI API provider"""

    def __init__(self, api_key: str, base_url: str = None):
        super().__init__(api_key, base_url)
        self.client = openai.AsyncOpenAI(
            api_key=api_key,
            base_url=base_url or "https://api.openai.com/v1"
        )

    async def get_models(self) -> List[str]:
        try:
            models = await self.client.models.list()
            return [model.id for model in models.data if "gpt" in model.id.lower()]
        except Exception as e:
            logger.error(f"Failed to fetch OpenAI models: {e}")
            return ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"]

    async def chat(self, messages: List[Dict[str, Any]], **kwargs) -> AsyncGenerator[str, None]:
        try:
            stream = await self.client.chat.completions.create(
                messages=messages,
                model=kwargs.get("model", "gpt-4"),
                stream=True,
                **{k: v for k, v in kwargs.items() if k not in ["model"]}
            )
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
        except Exception as e:
            logger.error(f"OpenAI chat error: {e}")
            yield f"Error: {str(e)}"


class ClaudeProvider(ServerAPIProvider):
    """Claude API provider"""

    def __init__(self, api_key: str, base_url: str = None):
        super().__init__(api_key, base_url)
        self.client = anthropic.AsyncAnthropic(
            api_key=api_key,
            base_url=base_url
        )

    async def get_models(self) -> List[str]:
        return ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307", "claude-3-opus-20240229"]

    async def chat(self, messages: List[Dict[str, Any]], **kwargs) -> AsyncGenerator[str, None]:
        try:
            # Extract system message
            system_message = kwargs.get("system", "")
            filtered_messages = [msg for msg in messages if msg["role"] != "system"]

            stream = await self.client.messages.create(
                model=kwargs.get("model", "claude-3-5-sonnet-20241022"),
                messages=filtered_messages,
                system=system_message,
                stream=True,
                **{k: v for k, v in kwargs.items() if k not in ["model", "system"]}
            )
            async for chunk in stream:
                if chunk.type == "content_block_delta" and hasattr(chunk.delta, 'text'):
                    yield chunk.delta.text
        except Exception as e:
            logger.error(f"Claude chat error: {e}")
            yield f"Error: {str(e)}"


# Global instances
_providers: Dict[str, ServerAPIProvider] = {}
_context_engine = None
_agent_engine = None
_tool_manager = None


def get_context_engine():
    """Get or create context engine"""
    global _context_engine
    if _context_engine is None:
        from agent.memory import MemoryManager
        memory_manager = MemoryManager()
        _context_engine = ReverieContextEngine(memory_manager)
    return _context_engine


def get_agent_engine():
    """Get or create agent engine"""
    global _agent_engine
    if _agent_engine is None:
        model_manager = ModelManager()
        _agent_engine = AgentEngine(model_manager)
    return _agent_engine


def get_tool_manager():
    """Get or create tool manager"""
    global _tool_manager
    if _tool_manager is None:
        _tool_manager = ToolManager()
    return _tool_manager


async def get_provider(provider_name: str, api_key: str = None, base_url: str = None) -> ServerAPIProvider:
    """Get or create API provider"""
    provider_key = f"{provider_name}:{api_key or 'default'}"

    if provider_key not in _providers:
        if provider_name == "openai":
            if not api_key:
                raise HTTPException(status_code=400, detail="OpenAI API key required")
            _providers[provider_key] = OpenAIProvider(api_key, base_url)
        elif provider_name == "claude":
            if not api_key:
                raise HTTPException(status_code=400, detail="Claude API key required")
            _providers[provider_key] = ClaudeProvider(api_key, base_url)
        elif provider_name == "zhipu":
            if not api_key:
                raise HTTPException(status_code=400, detail="Zhipu API key required")
            _providers[provider_key] = ZhipuProvider(api_key, base_url)
        elif provider_name in ["server", "local"]:
            _providers[provider_key] = LocalServerProvider(base_url or "http://127.0.0.1:8000")
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported provider: {provider_name}")

    return _providers[provider_key]


class ZhipuProvider(ServerAPIProvider):
    """Zhipu AI provider"""

    def __init__(self, api_key: str, base_url: str = None):
        super().__init__(api_key, base_url)
        self.client = ZhipuAI(api_key=api_key)

    async def get_models(self) -> List[str]:
        return ["glm-4-plus", "glm-4", "glm-4-air", "glm-4-airx", "glm-4-flash"]

    async def chat(self, messages: List[Dict[str, Any]], **kwargs) -> AsyncGenerator[str, None]:
        try:
            response = self.client.chat.completions.create(
                model=kwargs.get("model", "glm-4-plus"),
                messages=messages,
                stream=True,
                **{k: v for k, v in kwargs.items() if k not in ["model"]}
            )
            for chunk in response:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
        except Exception as e:
            logger.error(f"Zhipu chat error: {e}")
            yield f"Error: {str(e)}"


class LocalServerProvider(ServerAPIProvider):
    """Local server provider that directly uses the model manager"""

    def __init__(self, base_url: str):
        super().__init__(base_url=base_url)
        self.model_manager = None

    def get_model_manager(self):
        """Get model manager instance"""
        if self.model_manager is None:
            from models.manager import ModelManager
            self.model_manager = ModelManager()
        return self.model_manager

    async def get_models(self) -> List[str]:
        try:
            model_manager = self.get_model_manager()
            if model_manager.is_model_loaded():
                return [model_manager.current_model.name]
            return ["Jan-nano-128k"]
        except Exception as e:
            logger.error(f"Failed to fetch local models: {e}")
            return ["Jan-nano-128k"]

    async def chat(self, messages: List[Dict[str, Any]], **kwargs) -> AsyncGenerator[str, None]:
        try:
            model_manager = self.get_model_manager()

            if not model_manager.is_model_loaded():
                yield "Error: No model is currently loaded. Please load a model first."
                return

            # Check if tools are enabled
            enable_tools = kwargs.get("tools") is not None or kwargs.get("enable_tools", False)

            # Get user message for intelligent search detection
            user_message = ""
            for msg in messages:
                if msg.get("role") == "user":
                    user_message = msg.get("content", "")
                    break

            # Use intelligent search if tools are enabled
            if enable_tools and user_message:
                from core.intelligent_search import IntelligentSearchProcessor
                from tools.manager import ToolManager

                search_processor = IntelligentSearchProcessor()
                tool_manager = ToolManager()

                # Create model response generator
                async def model_response_generator():
                    # Convert messages to prompt
                    prompt_parts = []
                    for msg in messages:
                        role = msg.get("role", "user")
                        content = msg.get("content", "")
                        if role == "system":
                            prompt_parts.append(f"System: {content}")
                        elif role == "user":
                            prompt_parts.append(f"User: {content}")
                        elif role == "assistant":
                            prompt_parts.append(f"Assistant: {content}")

                    prompt = "\n".join(prompt_parts) + "\nAssistant:"

                    # Extract parameters
                    max_tokens = kwargs.get("max_tokens", 120000)
                    temperature = kwargs.get("temperature", 0.7)
                    top_p = kwargs.get("top_p", 0.9)
                    stream = kwargs.get("stream", True)

                    # Generate response
                    async for chunk in model_manager.generate_text(
                        prompt=prompt,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        top_p=top_p,
                        stream=stream
                    ):
                        yield chunk

                # Process with intelligent search
                async for chunk in search_processor.process_message_with_search(
                    user_message,
                    model_response_generator(),
                    tool_manager,
                    messages
                ):
                    yield chunk
            else:
                # Fallback to simple generation without search
                prompt_parts = []
                for msg in messages:
                    role = msg.get("role", "user")
                    content = msg.get("content", "")
                    if role == "system":
                        prompt_parts.append(f"System: {content}")
                    elif role == "user":
                        prompt_parts.append(f"User: {content}")
                    elif role == "assistant":
                        prompt_parts.append(f"Assistant: {content}")

                prompt = "\n".join(prompt_parts) + "\nAssistant:"

                # Extract parameters
                max_tokens = kwargs.get("max_tokens", 120000)
                temperature = kwargs.get("temperature", 0.7)
                top_p = kwargs.get("top_p", 0.9)
                stream = kwargs.get("stream", True)

                # Generate response
                async for chunk in model_manager.generate_text(
                    prompt=prompt,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    top_p=top_p,
                    stream=stream
                ):
                    yield chunk

        except Exception as e:
            logger.error(f"Local server chat error: {e}")
            yield f"Error: {str(e)}"


def get_augment_system_prompt(context: Dict[str, Any] = None) -> str:
    """Get the Augment Agent system prompt with context"""

    base_prompt = """You are Augment Agent developed by Augment Code, an agentic coding AI assistant with access to the developer's codebase through Augment's world-leading context engine and integrations.

You can read from and write to the codebase using the provided tools. You have comprehensive development capabilities including:

**Core Capabilities:**
- File operations and codebase management
- Command execution and development tools
- Code analysis, generation, and refactoring
- Project workflow assistance
- Technical problem solving and debugging
- Web search and research capabilities
- Multi-step task planning and execution

**Available Tools:**
- `web_search(query)` - Search the web for information
- `file_read(path)` - Read file contents
- `file_write(path, content)` - Write content to files
- `file_list(path)` - List directory contents
- `command_execute(command)` - Execute shell commands
- `code_analyze(code, language)` - Analyze code structure
- `project_analyze(path)` - Analyze project structure
- `find_in_files(pattern, path)` - Search text in files

**Your Role:**
Provide expert development assistance, understand complex codebases, and help developers accomplish their goals efficiently. Always use tools when needed to gather information, make changes, or execute tasks. Be proactive in suggesting improvements and solutions."""

    # Add context information
    if context:
        context_info = []
        if "current_file" in context:
            context_info.append(f"Current file: {context['current_file']}")
        if "selected_text" in context:
            context_info.append(f"Selected text:\n```\n{context['selected_text']}\n```")
        if "project_context" in context:
            context_info.append(f"Project context: {context['project_context']}")
        if "workspace_path" in context:
            context_info.append(f"Workspace: {context['workspace_path']}")

        if context_info:
            base_prompt += "\n\n**Current Development Context:**\n" + "\n".join(context_info)

    return base_prompt


async def format_messages_for_provider(messages: List[AgentMessage], provider: str, context: Dict[str, Any] = None) -> tuple:
    """Format messages for specific provider with tool support"""

    formatted_messages = []
    system_message = ""

    # Extract system messages
    for msg in messages:
        if msg.role == "system":
            system_message += msg.content + "\n"

    # Add Augment Agent identity if no system message exists
    if not system_message.strip():
        system_message = get_augment_system_prompt(context)

    # Format based on provider
    if provider == "claude":
        # Claude handles system messages separately
        for msg in messages:
            if msg.role != "system":
                message_dict = {
                    "role": msg.role,
                    "content": msg.content
                }
                if msg.tool_calls:
                    message_dict["tool_calls"] = msg.tool_calls
                if msg.tool_call_id:
                    message_dict["tool_call_id"] = msg.tool_call_id
                formatted_messages.append(message_dict)
        return formatted_messages, system_message

    else:
        # OpenAI, Zhipu, and server format
        if system_message.strip():
            formatted_messages.append({
                "role": "system",
                "content": system_message.strip()
            })

        for msg in messages:
            if msg.role != "system":
                message_dict = {
                    "role": msg.role,
                    "content": msg.content
                }
                if msg.tool_calls:
                    message_dict["tool_calls"] = msg.tool_calls
                if msg.tool_call_id:
                    message_dict["tool_call_id"] = msg.tool_call_id
                if msg.name:
                    message_dict["name"] = msg.name
                formatted_messages.append(message_dict)

        return formatted_messages, None


async def execute_tool_calls(tool_calls: List[Dict[str, Any]], tool_manager: ToolManager) -> List[ToolCallResult]:
    """Execute tool calls and return results"""
    results = []

    for tool_call in tool_calls:
        start_time = datetime.now()
        try:
            tool_name = tool_call.get("function", {}).get("name")
            tool_args = json.loads(tool_call.get("function", {}).get("arguments", "{}"))
            tool_call_id = tool_call.get("id", f"call_{len(results)}")

            logger.info(f"Executing tool: {tool_name} with args: {tool_args}")

            # Execute the tool
            result = await tool_manager.execute_tool(tool_name, **tool_args)

            execution_time = (datetime.now() - start_time).total_seconds()

            results.append(ToolCallResult(
                tool_call_id=tool_call_id,
                tool_name=tool_name,
                result=result,
                success=True,
                execution_time=execution_time
            ))

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Tool execution failed: {e}")

            results.append(ToolCallResult(
                tool_call_id=tool_call.get("id", f"call_{len(results)}"),
                tool_name=tool_call.get("function", {}).get("name", "unknown"),
                result=None,
                success=False,
                error=str(e),
                execution_time=execution_time
            ))

    return results


def get_tool_definitions() -> List[Dict[str, Any]]:
    """Get comprehensive OpenAI-compatible tool definitions"""
    return [
        # Core file operations
        {
            "type": "function",
            "function": {
                "name": "file_read",
                "description": "Read contents of a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "File path to read"}
                    },
                    "required": ["path"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "file_write",
                "description": "Write content to a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "File path to write"},
                        "content": {"type": "string", "description": "Content to write"}
                    },
                    "required": ["path", "content"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "file_list",
                "description": "List files and directories",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Directory path to list"}
                    },
                    "required": ["path"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "copy_file",
                "description": "Copy a file from source to destination",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "source": {"type": "string", "description": "Source file path"},
                        "destination": {"type": "string", "description": "Destination file path"}
                    },
                    "required": ["source", "destination"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "move_file",
                "description": "Move or rename a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "source": {"type": "string", "description": "Source file path"},
                        "destination": {"type": "string", "description": "Destination file path"}
                    },
                    "required": ["source", "destination"]
                }
            }
        },
        # Search and analysis
        {
            "type": "function",
            "function": {
                "name": "web_search",
                "description": "Search the web for information with content extraction",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Search query"},
                        "max_results": {"type": "integer", "description": "Maximum number of results", "default": 5}
                    },
                    "required": ["query"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "find_in_files",
                "description": "Search for text patterns in files",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "pattern": {"type": "string", "description": "Search pattern"},
                        "path": {"type": "string", "description": "Directory path to search"},
                        "case_sensitive": {"type": "boolean", "description": "Case sensitive search", "default": False}
                    },
                    "required": ["pattern", "path"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "regex_search",
                "description": "Search using regular expressions",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "pattern": {"type": "string", "description": "Regex pattern"},
                        "path": {"type": "string", "description": "File or directory path"},
                        "case_sensitive": {"type": "boolean", "description": "Case sensitive search", "default": False}
                    },
                    "required": ["pattern", "path"]
                }
            }
        },
        # Code analysis
        {
            "type": "function",
            "function": {
                "name": "code_analyze",
                "description": "Analyze code structure and patterns",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "code": {"type": "string", "description": "Code to analyze"},
                        "language": {"type": "string", "description": "Programming language"}
                    },
                    "required": ["code", "language"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "project_analyze",
                "description": "Analyze project structure and dependencies",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Project path to analyze"}
                    },
                    "required": ["path"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "file_analyze",
                "description": "Analyze individual file properties and content",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "File path to analyze"}
                    },
                    "required": ["path"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "dependency_analysis",
                "description": "Analyze project dependencies from requirements and package files",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Project path to analyze"}
                    },
                    "required": ["path"]
                }
            }
        },
        # Command execution
        {
            "type": "function",
            "function": {
                "name": "command_execute",
                "description": "Execute a shell command (use with caution)",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {"type": "string", "description": "Command to execute"},
                        "timeout": {"type": "integer", "description": "Timeout in seconds", "default": 30}
                    },
                    "required": ["command"]
                }
            }
        },
        # Git operations
        {
            "type": "function",
            "function": {
                "name": "git_status",
                "description": "Get Git repository status",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Repository path", "default": "."}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "git_diff",
                "description": "Get Git diff information",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Repository path", "default": "."},
                        "staged": {"type": "boolean", "description": "Show staged changes", "default": False}
                    }
                }
            }
        },
        # System information
        {
            "type": "function",
            "function": {
                "name": "system_info",
                "description": "Get detailed system information",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "process_info",
                "description": "Get information about running processes",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string", "description": "Process name filter (optional)"}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "disk_usage",
                "description": "Get disk usage information",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Path to check", "default": "."}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "network_info",
                "description": "Get network interface and connectivity information",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            }
        },
        # Project management
        {
            "type": "function",
            "function": {
                "name": "create_project_structure",
                "description": "Create a new project structure from template",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Project directory path"},
                        "template": {"type": "string", "description": "Project template", "enum": ["python", "javascript"], "default": "python"}
                    },
                    "required": ["path"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "generate_documentation",
                "description": "Generate documentation for a project",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Project path"},
                        "format": {"type": "string", "description": "Documentation format", "enum": ["markdown"], "default": "markdown"}
                    },
                    "required": ["path"]
                }
            }
        },
        # Task management
        {
            "type": "function",
            "function": {
                "name": "task_tracker_create",
                "description": "Create a new task in the task tracker",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string", "description": "Task title"},
                        "description": {"type": "string", "description": "Task description", "default": ""},
                        "priority": {"type": "string", "description": "Task priority", "enum": ["low", "medium", "high"], "default": "medium"},
                        "project": {"type": "string", "description": "Project name", "default": "default"}
                    },
                    "required": ["title"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "task_tracker_list",
                "description": "List tasks from the task tracker",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "project": {"type": "string", "description": "Filter by project"},
                        "status": {"type": "string", "description": "Filter by status", "enum": ["todo", "in_progress", "completed", "cancelled"]}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "task_tracker_update",
                "description": "Update a task in the task tracker",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "task_id": {"type": "string", "description": "Task ID to update"},
                        "status": {"type": "string", "description": "New status", "enum": ["todo", "in_progress", "completed", "cancelled"]},
                        "title": {"type": "string", "description": "New title"},
                        "description": {"type": "string", "description": "New description"},
                        "priority": {"type": "string", "description": "New priority", "enum": ["low", "medium", "high"]}
                    },
                    "required": ["task_id"]
                }
            }
        },
        # Code formatting
        {
            "type": "function",
            "function": {
                "name": "code_format",
                "description": "Format code using various formatters",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "File path to format"},
                        "formatter": {"type": "string", "description": "Formatter to use", "enum": ["black", "autopep8"], "default": "black"}
                    },
                    "required": ["path"]
                }
            }
        },
        # Text operations
        {
            "type": "function",
            "function": {
                "name": "text_replace",
                "description": "Replace text in a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "File path"},
                        "old_text": {"type": "string", "description": "Text to replace"},
                        "new_text": {"type": "string", "description": "Replacement text"},
                        "backup": {"type": "boolean", "description": "Create backup", "default": True}
                    },
                    "required": ["path", "old_text", "new_text"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "directory_tree",
                "description": "Generate directory tree structure",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Directory path"},
                        "max_depth": {"type": "integer", "description": "Maximum depth", "default": 3}
                    },
                    "required": ["path"]
                }
            }
        }
    ]


# Basic chat streaming endpoint for CLI testing with web engine support
@router.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    """Handle streaming chat requests for basic conversation with web engine support"""
    try:
        # Get services
        provider = await get_provider(request.provider, request.api_key, request.base_url)
        context_engine = get_context_engine()
        tool_manager = get_tool_manager()

        # Format messages with appropriate system prompt
        from core.prompts import format_messages_with_system_prompt

        formatted_messages, system_message = format_messages_with_system_prompt(
            [{"role": msg.role, "content": msg.content} for msg in request.messages],
            mode="chat",
            provider=request.provider
        )

        # API parameters
        api_params = {
            "model": request.model,
            "max_tokens": request.max_tokens or 120000,  # Default to 120K
            "temperature": request.temperature or 0.7,
            "top_p": request.top_p or 0.9,
            "stream": True
        }

        # Add tools if enabled
        if request.enable_tools and request.provider in ["openai", "claude"]:
            api_params["tools"] = get_tool_definitions()

        if system_message and request.provider == "claude":
            api_params["system"] = system_message
        elif system_message:
            # For other providers, add system message to the beginning
            formatted_messages.insert(0, {"role": "system", "content": system_message})

        async def generate():
            try:
                # Add user messages to context
                for msg in request.messages:
                    if msg.role == "user":
                        await context_engine.add_conversation_context(
                            msg.content, msg.role,
                            metadata=request.context or {},
                            provider=request.provider
                        )

                # Stream response with intelligent search
                response_text = ""

                # Create provider response generator
                async def provider_response():
                    async for chunk in provider.chat(formatted_messages, **api_params):
                        yield chunk

                # Use intelligent search for local provider
                if request.provider == "local" and request.enable_tools:
                    from core.intelligent_search import IntelligentSearchProcessor

                    search_processor = IntelligentSearchProcessor()
                    user_message = ""
                    for msg in request.messages:
                        if msg.role == "user":
                            user_message = msg.content
                            break

                    # Process with intelligent search
                    async for chunk in search_processor.process_message_with_search(
                        user_message,
                        provider_response(),
                        tool_manager,
                        [{"role": msg.role, "content": msg.content} for msg in request.messages]
                    ):
                        response_text += chunk
                        # Send chunk in SSE format with proper escaping
                        chunk_data = {
                            'choices': [{
                                'delta': {'content': chunk},
                                'finish_reason': None
                            }],
                            'created': int(time.time()),
                            'id': f'chatcmpl-{uuid.uuid4()}',
                            'model': request.model,
                            'object': 'chat.completion.chunk'
                        }
                        yield f"data: {json.dumps(chunk_data)}\n\n"
                else:
                    # Standard streaming for other providers
                    async for chunk in provider.chat(formatted_messages, **api_params):
                        response_text += chunk
                        # Send chunk in SSE format with proper escaping
                        chunk_data = {
                            'choices': [{
                                'delta': {'content': chunk},
                                'finish_reason': None
                            }],
                            'created': int(time.time()),
                            'id': f'chatcmpl-{uuid.uuid4()}',
                            'model': request.model,
                            'object': 'chat.completion.chunk'
                        }
                        yield f"data: {json.dumps(chunk_data)}\n\n"

                # Send final completion message
                yield f"data: {json.dumps({'choices': [{'delta': {}, 'finish_reason': 'stop'}]})}\n\n"
                yield "data: [DONE]\n\n"

            except Exception as e:
                logger.error(f"Error in chat stream: {e}")
                error_data = {
                    'error': {
                        'message': str(e),
                        'type': 'stream_error',
                        'code': 500
                    }
                }
                yield f"data: {json.dumps(error_data)}\n\n"
                yield "data: [DONE]\n\n"

        return StreamingResponse(generate(), media_type="text/event-stream")

    except Exception as e:
        logger.error(f"Chat stream error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/agent", response_model=AgentResponse)
async def agent_chat(request: AgentRequest):
    """Handle agent chat requests with tool calling support"""
    try:
        # Get services
        provider = await get_provider(request.provider, request.api_key, request.base_url)
        context_engine = get_context_engine()
        tool_manager = get_tool_manager()

        # Format messages with appropriate system prompt for agent mode
        from core.prompts import format_messages_with_system_prompt

        formatted_messages, system_message = format_messages_with_system_prompt(
            [{"role": msg.role, "content": msg.content} for msg in request.messages],
            mode="agent",
            provider=request.provider
        )

        # Add conversation context
        for msg in request.messages:
            if msg.role == "user":
                await context_engine.add_conversation_context(
                    msg.content, msg.role,
                    metadata=request.context or {},
                    provider=request.provider
                )

        # Prepare API parameters
        api_params = {
            "temperature": request.temperature,
            "max_tokens": request.max_tokens,
            "stream": False
        }

        # Add tools if enabled
        if request.enable_tools and request.provider in ["openai", "claude"]:
            api_params["tools"] = request.tools or get_tool_definitions()

        if system_message and request.provider == "claude":
            api_params["system"] = system_message

        # Generate response
        response_text = ""
        tool_calls = []

        async for chunk in provider.chat(formatted_messages, **api_params):
            response_text += chunk

        # Check for tool calls in response (simplified - would need proper parsing)
        tool_results = []
        if request.enable_tools and "tool_calls" in response_text:
            # This is a simplified example - real implementation would parse tool calls from response
            pass

        # Add assistant response to context
        await context_engine.add_conversation_context(
            response_text, "assistant",
            metadata={"model": request.model or "default"},
            provider=request.provider
        )

        # Create response
        response_message = AgentMessage(
            role="assistant",
            content=response_text.strip(),
            provider=request.provider,
            tool_calls=tool_calls if tool_calls else None
        )

        return AgentResponse(
            message=response_message,
            provider=request.provider,
            model=request.model or "default",
            tool_results=tool_results if tool_results else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Agent chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/agent/stream")
async def agent_chat_stream(request: AgentRequest):
    """Handle streaming agent chat requests with tool calling"""
    try:
        # Get services
        provider = await get_provider(request.provider, request.api_key, request.base_url)
        context_engine = get_context_engine()
        tool_manager = get_tool_manager()

        async def generate():
            try:
                # Add user messages to context
                for msg in request.messages:
                    if msg.role == "user":
                        await context_engine.add_conversation_context(
                            msg.content, msg.role,
                            metadata=request.context or {},
                            provider=request.provider
                        )

                # Format messages for provider
                formatted_messages, system_message = await format_messages_for_provider(
                    request.messages, request.provider, request.context
                )

                # Prepare API parameters with 120K max tokens
                api_params = {
                    "temperature": request.temperature or 0.7,
                    "max_tokens": request.max_tokens or 120000,  # Default to 120K
                    "stream": True
                }

                # Add tools if enabled
                if request.enable_tools and request.provider in ["openai", "claude"]:
                    api_params["tools"] = request.tools or get_tool_definitions()

                if system_message and request.provider == "claude":
                    api_params["system"] = system_message

                # Stream response
                response_text = ""
                tool_calls = []

                # Send initial status
                status_data = {
                    'type': 'status',
                    'status': 'generating',
                    'provider': request.provider,
                    'model': request.model,
                    'timestamp': int(time.time())
                }
                yield f"data: {json.dumps(status_data)}\n\n"

                # Use intelligent search for local provider in agent mode
                if request.provider == "local" and request.enable_tools:
                    from core.intelligent_search import IntelligentSearchProcessor

                    search_processor = IntelligentSearchProcessor()
                    user_message = ""
                    for msg in request.messages:
                        if msg.role == "user":
                            user_message = msg.content
                            break

                    # Create provider response generator
                    async def provider_response():
                        async for chunk in provider.chat(formatted_messages, **api_params):
                            yield chunk

                    # Process with intelligent search
                    async for chunk in search_processor.process_message_with_search(
                        user_message,
                        provider_response(),
                        tool_manager,
                        [{"role": msg.role, "content": msg.content} for msg in request.messages]
                    ):
                        response_text += chunk
                        # Send chunk with proper metadata
                        chunk_data = {
                            'choices': [{
                                'delta': {'content': chunk},
                                'finish_reason': None
                            }],
                            'created': int(time.time()),
                            'id': f'agentcmpl-{uuid.uuid4()}',
                            'model': request.model,
                            'object': 'agent.completion.chunk'
                        }
                        yield f"data: {json.dumps(chunk_data)}\n\n"
                else:
                    # Standard streaming for other providers
                    async for chunk in provider.chat(formatted_messages, **api_params):
                        response_text += chunk
                        # Send chunk with proper metadata
                        chunk_data = {
                            'choices': [{
                                'delta': {'content': chunk},
                                'finish_reason': None
                            }],
                            'created': int(time.time()),
                            'id': f'agentcmpl-{uuid.uuid4()}',
                            'model': request.model,
                            'object': 'agent.completion.chunk'
                        }
                        yield f"data: {json.dumps(chunk_data)}\n\n"

                # Send completion message
                completion_data = {
                    'choices': [{
                        'delta': {},
                        'finish_reason': 'stop'
                    }],
                    'created': int(time.time()),
                    'id': f'agentcmpl-{uuid.uuid4()}',
                    'model': request.model,
                    'object': 'agent.completion.final'
                }
                yield f"data: {json.dumps(completion_data)}\n\n"
                yield "data: [DONE]\n\n"

            except Exception as e:
                logger.error(f"Error in agent stream: {e}")
                error_data = {
                    'error': {
                        'message': str(e),
                        'type': 'stream_error',
                        'code': 500
                    }
                }
                yield f"data: {json.dumps(error_data)}\n\n"
                yield "data: [DONE]\n\n"

        return StreamingResponse(generate(), media_type="text/event-stream")

    except Exception as e:
        logger.error(f"Agent stream error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/providers")
async def get_available_providers():
    """Get available API providers and their capabilities"""
    providers_info = {
        "openai": {
            "name": "OpenAI",
            "description": "OpenAI GPT models with function calling",
            "requires_api_key": True,
            "supports_tools": True,
            "supports_streaming": True,
            "default_models": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"]
        },
        "claude": {
            "name": "Claude (Anthropic)",
            "description": "Anthropic Claude models with tool use",
            "requires_api_key": True,
            "supports_tools": True,
            "supports_streaming": True,
            "default_models": ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307", "claude-3-opus-20240229"]
        },
        "zhipu": {
            "name": "Zhipu AI",
            "description": "Zhipu GLM models",
            "requires_api_key": True,
            "supports_tools": False,
            "supports_streaming": True,
            "default_models": ["glm-4-plus", "glm-4", "glm-4-air", "glm-4-flash"]
        },
        "server": {
            "name": "Local Server",
            "description": "Local model server",
            "requires_api_key": False,
            "supports_tools": True,
            "supports_streaming": True,
            "default_models": ["Jan-nano-128k"]
        }
    }

    return providers_info


@router.get("/providers/{provider}/models")
async def get_provider_models(provider: str, api_key: str = None, base_url: str = None):
    """Get available models for a specific provider"""
    try:
        provider_instance = await get_provider(provider, api_key, base_url)
        models = await provider_instance.get_models()
        return {"models": models}
    except Exception as e:
        logger.error(f"Failed to get models for {provider}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/providers/{provider}/test")
async def test_provider(provider: str, api_key: str = None, base_url: str = None):
    """Test connection to a specific provider"""
    try:
        provider_instance = await get_provider(provider, api_key, base_url)

        # Test with a simple message
        test_messages = [
            {"role": "user", "content": "Hello, this is a connection test. Please respond with 'Connection successful'."}
        ]

        response_text = ""
        async for chunk in provider_instance.chat(test_messages, max_tokens=50, temperature=0):
            response_text += chunk
            if len(response_text) > 100:  # Limit test response
                break

        return {
            "success": True,
            "provider": provider,
            "response": response_text.strip(),
            "message": "Connection test successful"
        }

    except Exception as e:
        logger.error(f"Provider test failed for {provider}: {e}")
        return {
            "success": False,
            "provider": provider,
            "error": str(e),
            "message": "Connection test failed"
        }


@router.get("/tools")
async def get_available_tools():
    """Get available tools for agent use"""
    tool_manager = get_tool_manager()
    tools = get_tool_definitions()

    return {
        "tools": tools,
        "count": len(tools),
        "descriptions": tool_manager.get_tools_description()
    }


@router.post("/context/search")
async def search_context(query: str, max_results: int = 10):
    """Search context engine for relevant information"""
    try:
        context_engine = get_context_engine()
        results = await context_engine.search_context(query, max_results)
        return {"results": results, "query": query}
    except Exception as e:
        logger.error(f"Context search failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Enhanced Streaming Communication Protocol

@router.websocket("/agent/ws")
async def agent_websocket(websocket):
    """WebSocket endpoint for real-time agent communication"""
    await websocket.accept()

    try:
        # Initialize agent services
        agent_engine = get_agent_engine()
        tool_manager = get_tool_manager()
        context_engine = get_context_engine()

        # Register event callbacks for real-time updates
        async def send_event(event_type: str, data: Dict[str, Any]):
            await websocket.send_json({
                "type": event_type,
                "data": data,
                "timestamp": datetime.now().isoformat()
            })

        # Register callbacks
        agent_engine.register_event_callback("task_created",
            lambda data: send_event("task_created", data))
        agent_engine.register_event_callback("task_updated",
            lambda data: send_event("task_updated", data))
        agent_engine.register_event_callback("step_started",
            lambda data: send_event("step_started", data))
        agent_engine.register_event_callback("step_completed",
            lambda data: send_event("step_completed", data))
        agent_engine.register_event_callback("tool_called",
            lambda data: send_event("tool_executed", data))
        agent_engine.register_event_callback("workflow_started",
            lambda data: send_event("workflow_started", data))
        agent_engine.register_event_callback("workflow_completed",
            lambda data: send_event("workflow_completed", data))
        agent_engine.register_event_callback("error_recovery",
            lambda data: send_event("error_recovery", data))

        # Main message loop
        while True:
            try:
                # Receive message from client
                message = await websocket.receive_json()
                message_type = message.get("type")

                if message_type == "chat":
                    await handle_websocket_chat(websocket, message, agent_engine, send_event)
                elif message_type == "create_task":
                    await handle_websocket_task_creation(websocket, message, agent_engine, send_event)
                elif message_type == "execute_workflow":
                    await handle_websocket_workflow(websocket, message, agent_engine, send_event)
                elif message_type == "search_context":
                    await handle_websocket_context_search(websocket, message, context_engine, send_event)
                elif message_type == "ping":
                    await websocket.send_json({"type": "pong", "timestamp": datetime.now().isoformat()})
                else:
                    await websocket.send_json({
                        "type": "error",
                        "error": f"Unknown message type: {message_type}"
                    })

            except Exception as e:
                logger.error(f"WebSocket message handling error: {e}")
                await websocket.send_json({
                    "type": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })

    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
    finally:
        try:
            await websocket.close()
        except:
            pass


async def handle_websocket_chat(websocket, message: Dict[str, Any], agent_engine, send_event):
    """Handle real-time chat through WebSocket"""
    try:
        user_message = message.get("content", "")
        provider = message.get("provider", "server")
        model = message.get("model", "Jan-nano-128k")
        enable_tools = message.get("enable_tools", True)
        context = message.get("context", {})

        # Send acknowledgment
        await send_event("chat_started", {
            "message": user_message,
            "provider": provider,
            "model": model
        })

        # Get provider and format messages
        provider_instance = await get_provider(provider,
                                             message.get("api_key"),
                                             message.get("base_url"))

        # Create messages list
        messages = [{"role": "user", "content": user_message}]
        formatted_messages, system_message = await format_messages_for_provider(
            [AgentMessage(role="user", content=user_message)], provider, context
        )

        # Prepare API parameters
        api_params = {
            "temperature": message.get("temperature", 0.7),
            "max_tokens": message.get("max_tokens", 120000),  # Use full context capacity
            "stream": True
        }

        if enable_tools and provider in ["openai", "claude"]:
            api_params["tools"] = get_tool_definitions()

        if system_message and provider == "claude":
            api_params["system"] = system_message

        # Stream response
        response_content = ""
        tool_calls = []

        try:
            async for chunk in provider_instance.chat(formatted_messages, **api_params):
                response_content += chunk

                # Send chunk to client
                await send_event("chat_chunk", {
                    "content": chunk,
                    "provider": provider
                })

                # Check for tool calls (simplified - real implementation would parse properly)
                if enable_tools and "function_call" in chunk:
                    await send_event("tool_call_detected", {
                        "content": chunk
                    })

            # Send completion
            await send_event("chat_completed", {
                "content": response_content,
                "provider": provider,
                "model": model,
                "tool_calls": tool_calls
            })

        except Exception as e:
            await send_event("chat_error", {
                "error": str(e),
                "provider": provider
            })

    except Exception as e:
        logger.error(f"WebSocket chat handling failed: {e}")
        await send_event("error", {"error": str(e)})


async def handle_websocket_task_creation(websocket, message: Dict[str, Any], agent_engine, send_event):
    """Handle task creation through WebSocket"""
    try:
        description = message.get("description", "")
        user_id = message.get("user_id", "default")
        metadata = message.get("metadata", {})

        # Create task
        task_id = await agent_engine.create_task(description, user_id, metadata)

        await send_event("task_creation_completed", {
            "task_id": task_id,
            "description": description
        })

        # Optionally execute immediately
        if message.get("execute_immediately", False):
            await send_event("task_execution_started", {"task_id": task_id})

            # Execute in background
            asyncio.create_task(execute_task_with_updates(agent_engine, task_id, send_event))

    except Exception as e:
        logger.error(f"WebSocket task creation failed: {e}")
        await send_event("error", {"error": str(e)})


async def handle_websocket_workflow(websocket, message: Dict[str, Any], agent_engine, send_event):
    """Handle workflow execution through WebSocket"""
    try:
        workflow_name = message.get("workflow_name", "")
        tasks = message.get("tasks", [])
        dependencies = message.get("dependencies", {})
        user_id = message.get("user_id", "default")

        # Create workflow
        workflow_id = await agent_engine.create_workflow(workflow_name, tasks, dependencies, user_id)

        await send_event("workflow_creation_completed", {
            "workflow_id": workflow_id,
            "workflow_name": workflow_name,
            "task_count": len(tasks)
        })

        # Execute workflow
        if message.get("execute_immediately", True):
            await send_event("workflow_execution_started", {"workflow_id": workflow_id})

            # Execute in background
            asyncio.create_task(execute_workflow_with_updates(agent_engine, workflow_id, send_event))

    except Exception as e:
        logger.error(f"WebSocket workflow handling failed: {e}")
        await send_event("error", {"error": str(e)})


async def handle_websocket_context_search(websocket, message: Dict[str, Any], context_engine, send_event):
    """Handle context search through WebSocket"""
    try:
        query = message.get("query", "")
        max_results = message.get("max_results", 20)
        context_types = message.get("context_types", [])

        # Perform semantic search
        results = await context_engine.semantic_search(
            query=query,
            context_types=[ContextType(ct) for ct in context_types] if context_types else None,
            max_results=max_results
        )

        await send_event("context_search_completed", {
            "query": query,
            "results": [item.to_dict() for item in results],
            "result_count": len(results)
        })

    except Exception as e:
        logger.error(f"WebSocket context search failed: {e}")
        await send_event("error", {"error": str(e)})


async def execute_task_with_updates(agent_engine, task_id: str, send_event):
    """Execute task with real-time updates"""
    try:
        result = await agent_engine.execute_task_with_retry(task_id)

        await send_event("task_execution_completed", {
            "task_id": task_id,
            "status": result.status.value,
            "result": result.result,
            "error": result.error
        })

    except Exception as e:
        logger.error(f"Task execution with updates failed: {e}")
        await send_event("task_execution_error", {
            "task_id": task_id,
            "error": str(e)
        })


async def execute_workflow_with_updates(agent_engine, workflow_id: str, send_event):
    """Execute workflow with real-time updates"""
    try:
        result = await agent_engine.execute_workflow(workflow_id)

        await send_event("workflow_execution_completed", {
            "workflow_id": workflow_id,
            "status": result.get("status"),
            "result": result.get("result"),
            "completed_tasks": result.get("completed_tasks", 0),
            "failed_tasks": result.get("failed_tasks", 0)
        })

    except Exception as e:
        logger.error(f"Workflow execution with updates failed: {e}")
        await send_event("workflow_execution_error", {
            "workflow_id": workflow_id,
            "error": str(e)
        })


# Server-Sent Events endpoint for clients that don't support WebSocket
@router.get("/agent/events")
async def agent_events_stream(request):
    """Server-Sent Events endpoint for real-time updates"""

    async def event_generator():
        # Initialize agent services
        agent_engine = get_agent_engine()

        # Event queue for this client
        event_queue = asyncio.Queue()

        # Register event callbacks
        async def queue_event(event_type: str, data: Dict[str, Any]):
            await event_queue.put({
                "event": event_type,
                "data": json.dumps(data),
                "id": str(uuid.uuid4()),
                "retry": 3000
            })

        agent_engine.register_event_callback("task_updated",
            lambda data: queue_event("task_updated", data))
        agent_engine.register_event_callback("tool_called",
            lambda data: queue_event("tool_executed", data))
        agent_engine.register_event_callback("workflow_completed",
            lambda data: queue_event("workflow_completed", data))

        # Send initial connection event
        yield f"data: {json.dumps({'type': 'connected', 'timestamp': datetime.now().isoformat()})}\n\n"

        try:
            while True:
                # Wait for events with timeout
                try:
                    event = await asyncio.wait_for(event_queue.get(), timeout=30.0)
                    yield f"event: {event['event']}\n"
                    yield f"data: {event['data']}\n"
                    yield f"id: {event['id']}\n"
                    yield f"retry: {event['retry']}\n\n"
                except asyncio.TimeoutError:
                    # Send keepalive
                    yield f"data: {json.dumps({'type': 'keepalive', 'timestamp': datetime.now().isoformat()})}\n\n"

        except Exception as e:
            logger.error(f"SSE stream error: {e}")
            yield f"data: {json.dumps({'type': 'error', 'error': str(e)})}\n\n"

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )
