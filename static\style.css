/* Rilance Code Studio - Web Interface Styles */

:root {
    /* Purple gradient theme */
    --primary-purple: #9d4edd;
    --secondary-purple: #c77dff;
    --light-purple: #e0aaff;
    --dark-purple: #7b2cbf;
    --accent-purple: #a663e8;
    
    /* Neutral colors */
    --bg-dark: #0d1117;
    --bg-secondary: #161b22;
    --bg-tertiary: #21262d;
    --text-primary: #f0f6fc;
    --text-secondary: #8b949e;
    --border-color: #30363d;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-purple), var(--secondary-purple));
    --gradient-text: linear-gradient(135deg, var(--primary-purple), var(--light-purple));
    
    /* Fonts */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background: var(--bg-dark);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
}

.logo-text {
    font-size: 2rem;
    font-weight: 700;
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-subtitle {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: -5px;
}

.nav {
    display: flex;
    gap: 30px;
    align-items: center;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--text-primary);
}

.nav-link.primary {
    background: var(--gradient-primary);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    transition: transform 0.3s ease;
}

.nav-link.primary:hover {
    transform: translateY(-2px);
    color: white;
}

/* Hero Section */
.hero {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    padding: 80px 0;
    min-height: 70vh;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 20px;
}

.gradient-text {
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 30px;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(157, 78, 221, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    border-color: var(--primary-purple);
    color: var(--primary-purple);
}

/* Code Preview */
.code-preview {
    background: var(--bg-secondary);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.code-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
}

.code-dots {
    display: flex;
    gap: 6px;
    margin-right: 12px;
}

.code-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--text-secondary);
}

.code-dots span:nth-child(1) { background: #ff5f56; }
.code-dots span:nth-child(2) { background: #ffbd2e; }
.code-dots span:nth-child(3) { background: #27ca3f; }

.code-title {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.code-content {
    padding: 20px;
    font-family: var(--font-mono);
    font-size: 0.9rem;
    line-height: 1.6;
}

.code-content .keyword { color: #ff7b72; }
.code-content .function { color: #d2a8ff; }
.code-content .param { color: #ffa657; }
.code-content .comment { color: #8b949e; }
.code-content .number { color: #79c0ff; }

/* Sections */
.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
}

.section-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
}

/* Features */
.features {
    padding: 100px 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.feature-card {
    background: var(--bg-secondary);
    padding: 30px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease, border-color 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-purple);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 12px;
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* API Section */
.api-section {
    padding: 100px 0;
    background: var(--bg-secondary);
}

.api-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.api-card {
    background: var(--bg-dark);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.api-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.api-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 12px;
}

.api-endpoint {
    display: flex;
    align-items: center;
    gap: 8px;
}

.method {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    font-family: var(--font-mono);
}

.method.get { background: #238636; color: white; }
.method.post { background: #1f6feb; color: white; }

.url {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.api-example {
    background: var(--bg-dark);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.api-example h3 {
    margin-bottom: 12px;
}

.api-example pre {
    background: var(--bg-tertiary);
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    font-family: var(--font-mono);
    font-size: 0.9rem;
}

/* Status Section */
.status-section {
    padding: 60px 0;
}

.status-card {
    background: var(--bg-secondary);
    padding: 30px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    max-width: 400px;
    margin: 0 auto;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.status-label {
    color: var(--text-secondary);
}

.status-value {
    font-weight: 600;
}

.status-value.online {
    color: #238636;
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    padding: 60px 0 20px;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    margin-bottom: 16px;
    color: var(--text-primary);
}

.footer-section a {
    display: block;
    color: var(--text-secondary);
    text-decoration: none;
    margin-bottom: 8px;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-purple);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

/* Responsive */
@media (max-width: 768px) {
    .hero {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .nav {
        display: none;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .api-grid {
        grid-template-columns: 1fr;
    }
}
