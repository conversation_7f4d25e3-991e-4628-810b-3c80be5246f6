# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
from .embed_by_type_response_embeddings import EmbedByTypeResponseEmbeddings
import pydantic
import typing
from .image import Image
from .api_meta import ApiMeta
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class EmbedByTypeResponse(UncheckedBaseModel):
    id: str
    embeddings: EmbedByTypeResponseEmbeddings = pydantic.Field()
    """
    An object with different embedding types. The length of each embedding type array will be the same as the length of the original `texts` array.
    """

    texts: typing.List[str] = pydantic.Field()
    """
    The text entries for which embeddings were returned.
    """

    images: typing.Optional[typing.List[Image]] = pydantic.Field(default=None)
    """
    The image entries for which embeddings were returned.
    """

    meta: typing.Optional[ApiMeta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
