"""Unicode Properties from Unicode version 13.0.0 (autogen)."""
from __future__ import annotations

unicode_alias: dict[str, dict[str, str]] = {
    "_": {
        "age": "age",
        "bc": "bidiclass",
        "blk": "block",
        "bpt": "bidipairedbrackettype",
        "ccc": "canonicalcombiningclass",
        "dt": "decompositiontype",
        "ea": "eastasianwidth",
        "gc": "generalcategory",
        "gcb": "graphemeclusterbreak",
        "hst": "hangulsyllabletype",
        "inpc": "indicpositionalcategory",
        "insc": "indicsyllabiccategory",
        "jg": "joininggroup",
        "jt": "joiningtype",
        "lb": "linebreak",
        "nfcqc": "nfcquickcheck",
        "nfdqc": "nfdquickcheck",
        "nfkcqc": "nfkcquickcheck",
        "nfkdqc": "nfkdquickcheck",
        "nt": "numerictype",
        "nv": "numericvalue",
        "sb": "sentencebreak",
        "sc": "script",
        "scx": "scriptextensions",
        "vo": "verticalorientation",
        "wb": "wordbreak"
    },
    "age": {
        "unassigned": "na",
        "v100": "10.0",
        "v11": "1.1",
        "v110": "11.0",
        "v120": "12.0",
        "v121": "12.1",
        "v130": "13.0",
        "v20": "2.0",
        "v21": "2.1",
        "v30": "3.0",
        "v31": "3.1",
        "v32": "3.2",
        "v40": "4.0",
        "v41": "4.1",
        "v50": "5.0",
        "v51": "5.1",
        "v52": "5.2",
        "v60": "6.0",
        "v61": "6.1",
        "v62": "6.2",
        "v63": "6.3",
        "v70": "7.0",
        "v80": "8.0",
        "v90": "9.0"
    },
    "bidiclass": {
        "arabicletter": "al",
        "arabicnumber": "an",
        "boundaryneutral": "bn",
        "commonseparator": "cs",
        "europeannumber": "en",
        "europeanseparator": "es",
        "europeanterminator": "et",
        "firststrongisolate": "fsi",
        "lefttoright": "l",
        "lefttorightembedding": "lre",
        "lefttorightisolate": "lri",
        "lefttorightoverride": "lro",
        "nonspacingmark": "nsm",
        "otherneutral": "on",
        "paragraphseparator": "b",
        "popdirectionalformat": "pdf",
        "popdirectionalisolate": "pdi",
        "righttoleft": "r",
        "righttoleftembedding": "rle",
        "righttoleftisolate": "rli",
        "righttoleftoverride": "rlo",
        "segmentseparator": "s",
        "whitespace": "ws"
    },
    "bidipairedbrackettype": {
        "close": "c",
        "none": "n",
        "open": "o"
    },
    "binary": {
        "ahex": "asciihexdigit",
        "alpha": "alphabetic",
        "bidic": "bidicontrol",
        "bidim": "bidimirrored",
        "blank": "posixblank",
        "cased": "cased",
        "ce": "compositionexclusion",
        "ci": "caseignorable",
        "compex": "fullcompositionexclusion",
        "cwcf": "changeswhencasefolded",
        "cwcm": "changeswhencasemapped",
        "cwkcf": "changeswhennfkccasefolded",
        "cwl": "changeswhenlowercased",
        "cwt": "changeswhentitlecased",
        "cwu": "changeswhenuppercased",
        "dash": "dash",
        "dep": "deprecated",
        "di": "defaultignorablecodepoint",
        "dia": "diacritic",
        "ebase": "emojimodifierbase",
        "ecomp": "emojicomponent",
        "emod": "emojimodifier",
        "emoji": "emoji",
        "epres": "emojipresentation",
        "ext": "extender",
        "extpict": "extendedpictographic",
        "graph": "posixgraph",
        "grbase": "graphemebase",
        "grext": "graphemeextend",
        "grlink": "graphemelink",
        "h": "horizspace",
        "hex": "hexdigit",
        "hyphen": "hyphen",
        "idc": "idcontinue",
        "ideo": "ideographic",
        "ids": "idstart",
        "idsb": "idsbinaryoperator",
        "idst": "idstrinaryoperator",
        "joinc": "joincontrol",
        "loe": "logicalorderexception",
        "lower": "lowercase",
        "math": "math",
        "nchar": "noncharactercodepoint",
        "oalpha": "otheralphabetic",
        "odi": "otherdefaultignorablecodepoint",
        "ogrext": "othergraphemeextend",
        "oidc": "otheridcontinue",
        "oids": "otheridstart",
        "olower": "otherlowercase",
        "omath": "othermath",
        "oupper": "otheruppercase",
        "patsyn": "patternsyntax",
        "patws": "patternwhitespace",
        "pcm": "prependedconcatenationmark",
        "posixalpha": "alphabetic",
        "posixlower": "lowercase",
        "posixspace": "whitespace",
        "posixupper": "uppercase",
        "print": "posixprint",
        "qmark": "quotationmark",
        "radical": "radical",
        "ri": "regionalindicator",
        "sd": "softdotted",
        "space": "whitespace",
        "sterm": "sentenceterminal",
        "term": "terminalpunctuation",
        "uideo": "unifiedideograph",
        "upper": "uppercase",
        "v": "vertspace",
        "vs": "variationselector",
        "wspace": "whitespace",
        "xidc": "xidcontinue",
        "xids": "xidstart"
    },
    "block": {
        "alchemical": "alchemicalsymbols",
        "alphabeticpf": "alphabeticpresentationforms",
        "ancientgreekmusic": "ancientgreekmusicalnotation",
        "arabicexta": "arabicextendeda",
        "arabicmath": "arabicmathematicalalphabeticsymbols",
        "arabicpfa": "arabicpresentationformsa",
        "arabicpfb": "arabicpresentationformsb",
        "arabicsup": "arabicsupplement",
        "ascii": "basiclatin",
        "bamumsup": "bamumsupplement",
        "bopomofoext": "bopomofoextended",
        "braille": "braillepatterns",
        "byzantinemusic": "byzantinemusicalsymbols",
        "canadiansyllabics": "unifiedcanadianaboriginalsyllabics",
        "cherokeesup": "cherokeesupplement",
        "cjk": "cjkunifiedideographs",
        "cjkcompat": "cjkcompatibility",
        "cjkcompatforms": "cjkcompatibilityforms",
        "cjkcompatideographs": "cjkcompatibilityideographs",
        "cjkcompatideographssup": "cjkcompatibilityideographssupplement",
        "cjkexta": "cjkunifiedideographsextensiona",
        "cjkextb": "cjkunifiedideographsextensionb",
        "cjkextc": "cjkunifiedideographsextensionc",
        "cjkextd": "cjkunifiedideographsextensiond",
        "cjkexte": "cjkunifiedideographsextensione",
        "cjkextf": "cjkunifiedideographsextensionf",
        "cjkextg": "cjkunifiedideographsextensiong",
        "cjkradicalssup": "cjkradicalssupplement",
        "cjksymbols": "cjksymbolsandpunctuation",
        "combiningmarksforsymbols": "combiningdiacriticalmarksforsymbols",
        "compatjamo": "hangulcompatibilityjamo",
        "countingrod": "countingrodnumerals",
        "cuneiformnumbers": "cuneiformnumbersandpunctuation",
        "cyrillicexta": "cyrillicextendeda",
        "cyrillicextb": "cyrillicextendedb",
        "cyrillicextc": "cyrillicextendedc",
        "cyrillicsup": "cyrillicsupplement",
        "cyrillicsupplementary": "cyrillicsupplement",
        "devanagariext": "devanagariextended",
        "diacriticals": "combiningdiacriticalmarks",
        "diacriticalsext": "combiningdiacriticalmarksextended",
        "diacriticalsforsymbols": "combiningdiacriticalmarksforsymbols",
        "diacriticalssup": "combiningdiacriticalmarkssupplement",
        "domino": "dominotiles",
        "enclosedalphanum": "enclosedalphanumerics",
        "enclosedalphanumsup": "enclosedalphanumericsupplement",
        "enclosedcjk": "enclosedcjklettersandmonths",
        "enclosedideographicsup": "enclosedideographicsupplement",
        "ethiopicext": "ethiopicextended",
        "ethiopicexta": "ethiopicextendeda",
        "ethiopicsup": "ethiopicsupplement",
        "geometricshapesext": "geometricshapesextended",
        "georgianext": "georgianextended",
        "georgiansup": "georgiansupplement",
        "glagoliticsup": "glagoliticsupplement",
        "greek": "greekandcoptic",
        "greekext": "greekextended",
        "halfandfullforms": "halfwidthandfullwidthforms",
        "halfmarks": "combininghalfmarks",
        "hangul": "hangulsyllables",
        "highpusurrogates": "highprivateusesurrogates",
        "idc": "ideographicdescriptioncharacters",
        "ideographicsymbols": "ideographicsymbolsandpunctuation",
        "indicnumberforms": "commonindicnumberforms",
        "ipaext": "ipaextensions",
        "jamo": "hanguljamo",
        "jamoexta": "hanguljamoextendeda",
        "jamoextb": "hanguljamoextendedb",
        "kanaexta": "kanaextendeda",
        "kanasup": "kanasupplement",
        "kangxi": "kangxiradicals",
        "katakanaext": "katakanaphoneticextensions",
        "latin1": "latin1supplement",
        "latin1sup": "latin1supplement",
        "latinexta": "latinextendeda",
        "latinextadditional": "latinextendedadditional",
        "latinextb": "latinextendedb",
        "latinextc": "latinextendedc",
        "latinextd": "latinextendedd",
        "latinexte": "latinextendede",
        "lisusup": "lisusupplement",
        "mahjong": "mahjongtiles",
        "mathalphanum": "mathematicalalphanumericsymbols",
        "mathoperators": "mathematicaloperators",
        "meeteimayekext": "meeteimayekextensions",
        "miscarrows": "miscellaneoussymbolsandarrows",
        "miscmathsymbolsa": "miscellaneousmathematicalsymbolsa",
        "miscmathsymbolsb": "miscellaneousmathematicalsymbolsb",
        "miscpictographs": "miscellaneoussymbolsandpictographs",
        "miscsymbols": "miscellaneoussymbols",
        "misctechnical": "miscellaneoustechnical",
        "modifierletters": "spacingmodifierletters",
        "mongoliansup": "mongoliansupplement",
        "music": "musicalsymbols",
        "myanmarexta": "myanmarextendeda",
        "myanmarextb": "myanmarextendedb",
        "nb": "noblock",
        "ocr": "opticalcharacterrecognition",
        "phaistos": "phaistosdisc",
        "phoneticext": "phoneticextensions",
        "phoneticextsup": "phoneticextensionssupplement",
        "privateuse": "privateusearea",
        "pua": "privateusearea",
        "punctuation": "generalpunctuation",
        "rumi": "ruminumeralsymbols",
        "smallforms": "smallformvariants",
        "smallkanaext": "smallkanaextension",
        "sundanesesup": "sundanesesupplement",
        "suparrowsa": "supplementalarrowsa",
        "suparrowsb": "supplementalarrowsb",
        "suparrowsc": "supplementalarrowsc",
        "superandsub": "superscriptsandsubscripts",
        "supmathoperators": "supplementalmathematicaloperators",
        "suppuaa": "supplementaryprivateuseareaa",
        "suppuab": "supplementaryprivateuseareab",
        "suppunctuation": "supplementalpunctuation",
        "supsymbolsandpictographs": "supplementalsymbolsandpictographs",
        "symbolsandpictographsexta": "symbolsandpictographsextendeda",
        "syriacsup": "syriacsupplement",
        "taixuanjing": "taixuanjingsymbols",
        "tamilsup": "tamilsupplement",
        "tangutsup": "tangutsupplement",
        "transportandmap": "transportandmapsymbols",
        "ucas": "unifiedcanadianaboriginalsyllabics",
        "ucasext": "unifiedcanadianaboriginalsyllabicsextended",
        "vedicext": "vedicextensions",
        "vs": "variationselectors",
        "vssup": "variationselectorssupplement",
        "yijing": "yijinghexagramsymbols"
    },
    "canonicalcombiningclass": {
        "a": "230",
        "above": "230",
        "aboveleft": "228",
        "aboveright": "232",
        "al": "228",
        "ar": "232",
        "ata": "214",
        "atar": "216",
        "atb": "202",
        "atbl": "200",
        "attachedabove": "214",
        "attachedaboveright": "216",
        "attachedbelow": "202",
        "attachedbelowleft": "200",
        "b": "220",
        "below": "220",
        "belowleft": "218",
        "belowright": "222",
        "bl": "218",
        "br": "222",
        "ccc10": "10",
        "ccc103": "103",
        "ccc107": "107",
        "ccc11": "11",
        "ccc118": "118",
        "ccc12": "12",
        "ccc122": "122",
        "ccc129": "129",
        "ccc13": "13",
        "ccc130": "130",
        "ccc132": "132",
        "ccc133": "133",
        "ccc14": "14",
        "ccc15": "15",
        "ccc16": "16",
        "ccc17": "17",
        "ccc18": "18",
        "ccc19": "19",
        "ccc20": "20",
        "ccc21": "21",
        "ccc22": "22",
        "ccc23": "23",
        "ccc24": "24",
        "ccc25": "25",
        "ccc26": "26",
        "ccc27": "27",
        "ccc28": "28",
        "ccc29": "29",
        "ccc30": "30",
        "ccc31": "31",
        "ccc32": "32",
        "ccc33": "33",
        "ccc34": "34",
        "ccc35": "35",
        "ccc36": "36",
        "ccc84": "84",
        "ccc91": "91",
        "da": "234",
        "db": "233",
        "doubleabove": "234",
        "doublebelow": "233",
        "hanr": "6",
        "hanreading": "6",
        "iotasubscript": "240",
        "is": "240",
        "kanavoicing": "8",
        "kv": "8",
        "l": "224",
        "left": "224",
        "nk": "7",
        "notreordered": "0",
        "nr": "0",
        "nukta": "7",
        "ov": "1",
        "overlay": "1",
        "r": "226",
        "right": "226",
        "virama": "9",
        "vr": "9"
    },
    "decompositiontype": {
        "can": "canonical",
        "com": "compat",
        "enc": "circle",
        "fin": "final",
        "fra": "fraction",
        "init": "initial",
        "iso": "isolated",
        "med": "medial",
        "nar": "narrow",
        "nb": "nobreak",
        "sml": "small",
        "sqr": "square",
        "sup": "super",
        "vert": "vertical"
    },
    "eastasianwidth": {
        "ambiguous": "a",
        "fullwidth": "f",
        "halfwidth": "h",
        "narrow": "na",
        "neutral": "n",
        "wide": "w"
    },
    "generalcategory": {
        "casedletter": "lc",
        "closepunctuation": "pe",
        "cntrl": "cc",
        "combiningmark": "m",
        "connectorpunctuation": "pc",
        "control": "cc",
        "currencysymbol": "sc",
        "dashpunctuation": "pd",
        "decimalnumber": "nd",
        "digit": "nd",
        "enclosingmark": "me",
        "finalpunctuation": "pf",
        "format": "cf",
        "initialpunctuation": "pi",
        "letter": "l",
        "letternumber": "nl",
        "lineseparator": "zl",
        "lowercaseletter": "ll",
        "mark": "m",
        "mathsymbol": "sm",
        "modifierletter": "lm",
        "modifiersymbol": "sk",
        "nonspacingmark": "mn",
        "number": "n",
        "openpunctuation": "ps",
        "other": "c",
        "otherletter": "lo",
        "othernumber": "no",
        "otherpunctuation": "po",
        "othersymbol": "so",
        "paragraphseparator": "zp",
        "privateuse": "co",
        "punct": "p",
        "punctuation": "p",
        "separator": "z",
        "spaceseparator": "zs",
        "spacingmark": "mc",
        "surrogate": "cs",
        "symbol": "s",
        "titlecaseletter": "lt",
        "unassigned": "cn",
        "uppercaseletter": "lu"
    },
    "graphemeclusterbreak": {
        "cn": "control",
        "eb": "ebase",
        "ebg": "ebasegaz",
        "em": "emodifier",
        "ex": "extend",
        "gaz": "glueafterzwj",
        "pp": "prepend",
        "ri": "regionalindicator",
        "sm": "spacingmark",
        "xx": "other"
    },
    "hangulsyllabletype": {
        "leadingjamo": "l",
        "lvsyllable": "lv",
        "lvtsyllable": "lvt",
        "notapplicable": "na",
        "trailingjamo": "t",
        "voweljamo": "v"
    },
    "indicpositionalcategory": {
    },
    "indicsyllabiccategory": {
    },
    "joininggroup": {
        "hamzaonhehgoal": "tehmarbutagoal"
    },
    "joiningtype": {
        "dualjoining": "d",
        "joincausing": "c",
        "leftjoining": "l",
        "nonjoining": "u",
        "rightjoining": "r",
        "transparent": "t"
    },
    "linebreak": {
        "alphabetic": "al",
        "ambiguous": "ai",
        "breakafter": "ba",
        "breakbefore": "bb",
        "breakboth": "b2",
        "breaksymbols": "sy",
        "carriagereturn": "cr",
        "closeparenthesis": "cp",
        "closepunctuation": "cl",
        "combiningmark": "cm",
        "complexcontext": "sa",
        "conditionaljapanesestarter": "cj",
        "contingentbreak": "cb",
        "ebase": "eb",
        "emodifier": "em",
        "exclamation": "ex",
        "glue": "gl",
        "hebrewletter": "hl",
        "hyphen": "hy",
        "ideographic": "id",
        "infixnumeric": "is",
        "inseparable": "in",
        "inseperable": "in",
        "linefeed": "lf",
        "mandatorybreak": "bk",
        "nextline": "nl",
        "nonstarter": "ns",
        "numeric": "nu",
        "openpunctuation": "op",
        "postfixnumeric": "po",
        "prefixnumeric": "pr",
        "quotation": "qu",
        "regionalindicator": "ri",
        "space": "sp",
        "surrogate": "sg",
        "unknown": "xx",
        "wordjoiner": "wj",
        "zwspace": "zw"
    },
    "nfcquickcheck": {
        "maybe": "m",
        "no": "n",
        "yes": "y"
    },
    "nfdquickcheck": {
        "no": "n",
        "yes": "y"
    },
    "nfkcquickcheck": {
        "maybe": "m",
        "no": "n",
        "yes": "y"
    },
    "nfkdquickcheck": {
        "no": "n",
        "yes": "y"
    },
    "numerictype": {
        "de": "decimal",
        "di": "digit",
        "nu": "numeric"
    },
    "numericvalue": {
    },
    "script": {
        "adlm": "adlam",
        "aghb": "caucasianalbanian",
        "arab": "arabic",
        "armi": "imperialaramaic",
        "armn": "armenian",
        "avst": "avestan",
        "bali": "balinese",
        "bamu": "bamum",
        "bass": "bassavah",
        "batk": "batak",
        "beng": "bengali",
        "bhks": "bhaiksuki",
        "bopo": "bopomofo",
        "brah": "brahmi",
        "brai": "braille",
        "bugi": "buginese",
        "buhd": "buhid",
        "cakm": "chakma",
        "cans": "canadianaboriginal",
        "cari": "carian",
        "cher": "cherokee",
        "chrs": "chorasmian",
        "copt": "coptic",
        "cprt": "cypriot",
        "cyrl": "cyrillic",
        "deva": "devanagari",
        "diak": "divesakuru",
        "dogr": "dogra",
        "dsrt": "deseret",
        "dupl": "duployan",
        "egyp": "egyptianhieroglyphs",
        "elba": "elbasan",
        "elym": "elymaic",
        "ethi": "ethiopic",
        "geor": "georgian",
        "glag": "glagolitic",
        "gong": "gunjalagondi",
        "gonm": "masaramgondi",
        "goth": "gothic",
        "gran": "grantha",
        "grek": "greek",
        "gujr": "gujarati",
        "guru": "gurmukhi",
        "hang": "hangul",
        "hani": "han",
        "hano": "hanunoo",
        "hatr": "hatran",
        "hebr": "hebrew",
        "hira": "hiragana",
        "hluw": "anatolianhieroglyphs",
        "hmng": "pahawhhmong",
        "hmnp": "nyiakengpuachuehmong",
        "hrkt": "katakanaorhiragana",
        "hung": "oldhungarian",
        "ital": "olditalic",
        "java": "javanese",
        "kali": "kayahli",
        "kana": "katakana",
        "khar": "kharoshthi",
        "khmr": "khmer",
        "khoj": "khojki",
        "kits": "khitansmallscript",
        "knda": "kannada",
        "kthi": "kaithi",
        "lana": "taitham",
        "laoo": "lao",
        "latn": "latin",
        "lepc": "lepcha",
        "limb": "limbu",
        "lina": "lineara",
        "linb": "linearb",
        "lyci": "lycian",
        "lydi": "lydian",
        "mahj": "mahajani",
        "maka": "makasar",
        "mand": "mandaic",
        "mani": "manichaean",
        "marc": "marchen",
        "medf": "medefaidrin",
        "mend": "mendekikakui",
        "merc": "meroiticcursive",
        "mero": "meroitichieroglyphs",
        "mlym": "malayalam",
        "mong": "mongolian",
        "mroo": "mro",
        "mtei": "meeteimayek",
        "mult": "multani",
        "mymr": "myanmar",
        "nand": "nandinagari",
        "narb": "oldnortharabian",
        "nbat": "nabataean",
        "nkoo": "nko",
        "nshu": "nushu",
        "ogam": "ogham",
        "olck": "olchiki",
        "orkh": "oldturkic",
        "orya": "oriya",
        "osge": "osage",
        "osma": "osmanya",
        "palm": "palmyrene",
        "pauc": "paucinhau",
        "perm": "oldpermic",
        "phag": "phagspa",
        "phli": "inscriptionalpahlavi",
        "phlp": "psalterpahlavi",
        "phnx": "phoenician",
        "plrd": "miao",
        "prti": "inscriptionalparthian",
        "qaac": "coptic",
        "qaai": "inherited",
        "rjng": "rejang",
        "rohg": "hanifirohingya",
        "runr": "runic",
        "samr": "samaritan",
        "sarb": "oldsoutharabian",
        "saur": "saurashtra",
        "sgnw": "signwriting",
        "shaw": "shavian",
        "shrd": "sharada",
        "sidd": "siddham",
        "sind": "khudawadi",
        "sinh": "sinhala",
        "sogd": "sogdian",
        "sogo": "oldsogdian",
        "sora": "sorasompeng",
        "soyo": "soyombo",
        "sund": "sundanese",
        "sylo": "sylotinagri",
        "syrc": "syriac",
        "tagb": "tagbanwa",
        "takr": "takri",
        "tale": "taile",
        "talu": "newtailue",
        "taml": "tamil",
        "tang": "tangut",
        "tavt": "taiviet",
        "telu": "telugu",
        "tfng": "tifinagh",
        "tglg": "tagalog",
        "thaa": "thaana",
        "tibt": "tibetan",
        "tirh": "tirhuta",
        "ugar": "ugaritic",
        "vaii": "vai",
        "wara": "warangciti",
        "wcho": "wancho",
        "xpeo": "oldpersian",
        "xsux": "cuneiform",
        "yezi": "yezidi",
        "yiii": "yi",
        "zanb": "zanabazarsquare",
        "zinh": "inherited",
        "zyyy": "common",
        "zzzz": "unknown"
    },
    "scriptextensions": {
    },
    "sentencebreak": {
        "at": "aterm",
        "cl": "close",
        "ex": "extend",
        "fo": "format",
        "le": "oletter",
        "lo": "lower",
        "nu": "numeric",
        "sc": "scontinue",
        "se": "sep",
        "st": "sterm",
        "up": "upper",
        "xx": "other"
    },
    "verticalorientation": {
        "rotated": "r",
        "transformedrotated": "tr",
        "transformedupright": "tu",
        "upright": "u"
    },
    "wordbreak": {
        "dq": "doublequote",
        "eb": "ebase",
        "ebg": "ebasegaz",
        "em": "emodifier",
        "ex": "extendnumlet",
        "fo": "format",
        "gaz": "glueafterzwj",
        "hl": "hebrewletter",
        "ka": "katakana",
        "le": "aletter",
        "mb": "midnumlet",
        "ml": "midletter",
        "mn": "midnum",
        "nl": "newline",
        "nu": "numeric",
        "ri": "regionalindicator",
        "sq": "singlequote",
        "xx": "other"
    }
}
