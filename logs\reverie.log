2025-07-05 00:30:16.745 | SUCCESS  | core.logging:setup_logging:169 | 🎨 Enhanced logging system initialized
2025-07-05 00:30:16.747 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 00:30:16.764 | INFO     | __main__:main:321 | 🌟 Starting server on http://127.0.0.1:8000
2025-07-05 00:30:16.827 | INFO     | core.logging:log_operation:176 | 🚀 Starting Reverie Code Studio Server
2025-07-05 00:30:16.827 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 00:30:16.828 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 00:30:16.828 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 00:30:16.828 | INFO     | core.logging:log_operation:176 | 🔌 Initializing plugin system
2025-07-05 00:30:16.828 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 00:30:16.830 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 00:30:16.830 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 00:30:16.830 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 00:30:16.831 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 00:30:16.831 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 00:30:16.831 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 00:30:16.831 | INFO     | core.logging:log_operation:176 | 🔌 Plugin system initialized successfully
2025-07-05 00:30:16.832 | INFO     | core.logging:log_operation:176 | 🤖 Initializing model manager
2025-07-05 00:30:16.909 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 00:30:16.910 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 00:30:16.910 | INFO     | core.logging:log_operation:176 | 🤖 Model manager ready (no auto-loading)
2025-07-05 00:30:16.910 | INFO     | core.logging:log_operation:176 | 📝 Initializing interactive console
2025-07-05 00:30:16.911 | INFO     | core.logging:log_operation:176 | 📝 Interactive console started
2025-07-05 00:30:16.913 | INFO     | core.logging:log_operation:176 | 🚀 Server startup complete
2025-07-05 00:30:16.913 | INFO     | __main__:lifespan:78 | 🎮 Interactive Console Ready!
2025-07-05 00:30:16.914 | INFO     | __main__:lifespan:79 | ┌─ Quick Start Commands ──────────────────────────────────────────────┐
2025-07-05 00:30:16.914 | INFO     | __main__:lifespan:80 | │ 🤖 Models:    models popular | download default | load \<model>      │
2025-07-05 00:30:16.914 | INFO     | __main__:lifespan:81 | │              download llama-7b | download \<hf-url>                 │
2025-07-05 00:30:16.915 | INFO     | __main__:lifespan:82 | │ 🚀 Server:    status | config | memory | logs                       │
2025-07-05 00:30:16.915 | INFO     | __main__:lifespan:83 | │ 🔧 Dev:       exec \<cmd> | eval \<expr> | api \<endpoint>           │
2025-07-05 00:30:16.915 | INFO     | __main__:lifespan:84 | │ 🛠️  Utils:     help [cmd] | history | clear | exit                   │
2025-07-05 00:30:16.916 | INFO     | __main__:lifespan:85 | │                                                                      │
2025-07-05 00:30:16.916 | INFO     | __main__:lifespan:86 | │ 💡 Type 'help' for detailed command reference                       │
2025-07-05 00:30:16.916 | INFO     | __main__:lifespan:87 | │ 🌟 Popular: default, llama-7b, mistral-7b, codellama-7b, phi-3-mini │
2025-07-05 00:30:16.917 | INFO     | __main__:lifespan:88 | └──────────────────────────────────────────────────────────────────────┘
2025-07-05 00:31:02.081 | INFO     | core.logging:log_model_operation:192 | 🤖 Model download: Jan-nano-128k from https://huggingface.co/Menlo/Jan-nano-128k
2025-07-05 00:31:02.081 | INFO     | models.manager:download_model:151 | 🤗 Attempting HuggingFace Hub download...
2025-07-05 00:31:02.082 | INFO     | models.manager:_download_with_huggingface_hub:970 | ✅ HuggingFace Hub library available
2025-07-05 00:31:02.082 | INFO     | models.manager:_download_with_huggingface_hub:992 | 🤗 Starting HuggingFace Hub download
2025-07-05 00:31:02.082 | INFO     | models.manager:_download_with_huggingface_hub:993 | 📍 Repository: Menlo/Jan-nano-128k
2025-07-05 00:31:02.082 | INFO     | models.manager:_download_with_huggingface_hub:994 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 00:41:52.346 | ERROR    | models.manager:_download_with_huggingface_hub:1014 | Download error: ('Connection broken: IncompleteRead(3573144667 bytes read, 1394070693 more expected)', IncompleteRead(3573144667 bytes read, 1394070693 more expected))
2025-07-05 00:41:52.348 | INFO     | models.manager:_download_with_git:871 | ✅ Git library available
2025-07-05 00:41:52.399 | INFO     | models.manager:_download_with_git:883 | ✅ Git command available: git version 2.47.0.windows.1
2025-07-05 00:41:52.400 | INFO     | core.logging:log_git_operation:197 | 🔄 Git clone: Starting Git download of Jan-nano-128k
2025-07-05 00:41:52.400 | INFO     | models.manager:_download_with_git:920 | 📍 Source: https://huggingface.co/Menlo/Jan-nano-128k
2025-07-05 00:41:52.400 | INFO     | models.manager:_download_with_git:921 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 00:41:52.471 | ERROR    | models.manager:_download_with_git:946 | Git command error: Cmd('git') failed due to: exit code(128)
  cmdline: git clone -v --depth=1 --single-branch --progress -- https://huggingface.co/Menlo/Jan-nano-128k H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 01:00:11.842 | INFO     | models.manager:_download_with_http:1038 | 🌐 Starting HTTP download of Jan-nano-128k
2025-07-05 01:00:11.842 | INFO     | models.manager:_download_with_http:1039 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 01:00:11.846 | ERROR    | models.manager:_download_with_http:1080 | ❌ No files could be downloaded via HTTP
2025-07-05 01:00:11.847 | ERROR    | models.manager:download_model:190 | Failed to download model Jan-nano-128k: All download methods failed. Please check your internet connection and try again.
2025-07-05 01:01:00.567 | SUCCESS  | core.logging:setup_logging:169 | 🎨 Enhanced logging system initialized
2025-07-05 01:01:00.570 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 01:01:00.588 | INFO     | __main__:main:321 | 🌟 Starting server on http://127.0.0.1:8000
2025-07-05 01:01:00.647 | INFO     | core.logging:log_operation:176 | 🚀 Starting Reverie Code Studio Server
2025-07-05 01:01:00.648 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 01:01:00.649 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 01:01:00.649 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 01:01:00.649 | INFO     | core.logging:log_operation:176 | 🔌 Initializing plugin system
2025-07-05 01:01:00.650 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 01:01:00.650 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 01:01:00.650 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 01:01:00.651 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 01:01:00.651 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 01:01:00.651 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 01:01:00.651 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 01:01:00.652 | INFO     | core.logging:log_operation:176 | 🔌 Plugin system initialized successfully
2025-07-05 01:01:00.652 | INFO     | core.logging:log_operation:176 | 🤖 Initializing model manager
2025-07-05 01:01:00.683 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 01:01:00.683 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 01:01:00.684 | INFO     | core.logging:log_operation:176 | 🤖 Model manager ready (no auto-loading)
2025-07-05 01:01:00.684 | INFO     | core.logging:log_operation:176 | 📝 Initializing interactive console
2025-07-05 01:01:00.685 | INFO     | core.logging:log_operation:176 | 📝 Interactive console started
2025-07-05 01:01:00.686 | INFO     | core.logging:log_operation:176 | 🚀 Server startup complete
2025-07-05 01:01:00.686 | INFO     | __main__:lifespan:78 | 🎮 Interactive Console Ready!
2025-07-05 01:01:00.686 | INFO     | __main__:lifespan:79 | ┌─ Quick Start Commands ──────────────────────────────────────────────┐
2025-07-05 01:01:00.687 | INFO     | __main__:lifespan:80 | │ 🤖 Models:    models popular | download default | load \<model>      │
2025-07-05 01:01:00.687 | INFO     | __main__:lifespan:81 | │              download llama-7b | download \<hf-url>                 │
2025-07-05 01:01:00.687 | INFO     | __main__:lifespan:82 | │ 🚀 Server:    status | config | memory | logs                       │
2025-07-05 01:01:00.687 | INFO     | __main__:lifespan:83 | │ 🔧 Dev:       exec \<cmd> | eval \<expr> | api \<endpoint>           │
2025-07-05 01:01:00.687 | INFO     | __main__:lifespan:84 | │ 🛠️  Utils:     help [cmd] | history | clear | exit                   │
2025-07-05 01:01:00.687 | INFO     | __main__:lifespan:85 | │                                                                      │
2025-07-05 01:01:00.688 | INFO     | __main__:lifespan:86 | │ 💡 Type 'help' for detailed command reference                       │
2025-07-05 01:01:00.688 | INFO     | __main__:lifespan:87 | │ 🌟 Popular: default, llama-7b, mistral-7b, codellama-7b, phi-3-mini │
2025-07-05 01:01:00.688 | INFO     | __main__:lifespan:88 | └──────────────────────────────────────────────────────────────────────┘
2025-07-05 01:24:18.935 | SUCCESS  | core.logging:setup_logging:172 | 🎨 Enhanced logging system initialized
2025-07-05 01:24:18.954 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 01:24:18.969 | INFO     | __main__:main:393 | 🌟 Starting server on http://127.0.0.1:8000
2025-07-05 01:24:19.038 | INFO     | core.logging:log_operation:179 | 🚀 Starting Reverie Code Studio Server
2025-07-05 01:24:19.039 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 01:24:19.040 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 01:24:19.041 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 01:24:19.041 | INFO     | core.logging:log_operation:179 | 🔌 Initializing plugin system
2025-07-05 01:24:19.042 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 01:24:19.043 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 01:24:19.043 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 01:24:19.043 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 01:24:19.045 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 01:24:19.045 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 01:24:19.045 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 01:24:19.046 | INFO     | core.logging:log_operation:179 | 🔌 Plugin system initialized successfully
2025-07-05 01:24:19.047 | INFO     | core.logging:log_operation:179 | 🤖 Initializing model manager
2025-07-05 01:24:19.101 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 01:24:19.102 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 01:24:19.102 | INFO     | core.logging:log_operation:179 | 🤖 Model manager ready (no auto-loading)
2025-07-05 01:24:19.103 | INFO     | core.logging:log_operation:179 | 📝 Initializing interactive console
2025-07-05 01:24:19.103 | INFO     | core.logging:log_operation:179 | 📝 Interactive console started
2025-07-05 01:24:19.105 | INFO     | core.logging:log_operation:179 | 🚀 Server startup complete
2025-07-05 01:24:19.106 | INFO     | __main__:lifespan:78 | 🎮 Interactive Console Ready!
2025-07-05 01:24:19.106 | INFO     | __main__:lifespan:79 | ┌─ Quick Start Commands ──────────────────────────────────────────────┐
2025-07-05 01:24:19.107 | INFO     | __main__:lifespan:80 | │ 🤖 Models:    models popular | download default | load \<model>      │
2025-07-05 01:24:19.108 | INFO     | __main__:lifespan:81 | │              download llama-7b | download \<hf-url>                 │
2025-07-05 01:24:19.108 | INFO     | __main__:lifespan:82 | │ 🚀 Server:    status | config | memory | logs                       │
2025-07-05 01:24:19.109 | INFO     | __main__:lifespan:83 | │ 🔧 Dev:       exec \<cmd> | eval \<expr> | api \<endpoint>           │
2025-07-05 01:24:19.109 | INFO     | __main__:lifespan:84 | │ 🛠️  Utils:     help [cmd] | history | clear | exit                   │
2025-07-05 01:24:19.110 | INFO     | __main__:lifespan:85 | │                                                                      │
2025-07-05 01:24:19.110 | INFO     | __main__:lifespan:86 | │ 💡 Type 'help' for detailed command reference                       │
2025-07-05 01:24:19.111 | INFO     | __main__:lifespan:87 | │ 🌟 Popular: default, llama-7b, mistral-7b, codellama-7b, phi-3-mini │
2025-07-05 01:24:19.111 | INFO     | __main__:lifespan:88 | └──────────────────────────────────────────────────────────────────────┘
2025-07-05 01:44:32.009 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 01:44:32.029 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 01:44:32.045 | INFO     | __main__:main:346 | 🌟 Starting server on http://127.0.0.1:8000
2025-07-05 01:44:32.118 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 01:44:32.120 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 01:44:32.121 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 01:44:32.121 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 01:44:32.122 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 01:44:32.123 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 01:44:32.123 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 01:44:32.124 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 01:44:32.125 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 01:44:32.125 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 01:44:32.126 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 01:44:32.126 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 01:44:32.127 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 01:44:32.128 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 01:44:32.205 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 01:44:32.206 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 01:44:32.207 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 01:44:32.207 | INFO     | core.logging:log_operation:178 | 📝 Initializing interactive console
2025-07-05 01:44:32.209 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 01:44:32.210 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 01:44:32.211 | INFO     | __main__:lifespan:78 | 🎮 Interactive Console Ready!
2025-07-05 01:44:32.212 | INFO     | __main__:lifespan:79 | ┌─ Quick Start Commands ──────────────────────────────────────────────┐
2025-07-05 01:44:32.212 | INFO     | __main__:lifespan:80 | │ 🤖 Models:    models popular | download default | load \<model>      │
2025-07-05 01:44:32.214 | INFO     | __main__:lifespan:81 | │              download llama-7b | download \<hf-url>                 │
2025-07-05 01:44:32.214 | INFO     | __main__:lifespan:82 | │ 🚀 Server:    status | config | memory | logs                       │
2025-07-05 01:44:32.215 | INFO     | __main__:lifespan:83 | │ 🔧 Dev:       exec \<cmd> | eval \<expr> | api \<endpoint>           │
2025-07-05 01:44:32.215 | INFO     | __main__:lifespan:84 | │ 🛠️  Utils:     help [cmd] | history | clear | exit                   │
2025-07-05 01:44:32.216 | INFO     | __main__:lifespan:85 | │                                                                      │
2025-07-05 01:44:32.217 | INFO     | __main__:lifespan:86 | │ 💡 Type 'help' for detailed command reference                       │
2025-07-05 01:44:32.217 | INFO     | __main__:lifespan:87 | │ 🌟 Popular: default, llama-7b, mistral-7b, codellama-7b, phi-3-mini │
2025-07-05 01:44:32.217 | INFO     | __main__:lifespan:88 | └──────────────────────────────────────────────────────────────────────┘
2025-07-05 01:53:01.383 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 01:53:01.385 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 01:53:01.404 | INFO     | __main__:main:346 | 🌟 Starting server on http://127.0.0.1:8000
2025-07-05 01:53:01.434 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 01:53:01.435 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 01:53:01.436 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 01:53:01.436 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 01:53:01.436 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 01:53:01.437 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 01:53:01.437 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 01:53:01.438 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 01:53:01.438 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 01:53:01.438 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 01:53:01.439 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 01:53:01.439 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 01:53:01.439 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 01:53:01.439 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 01:53:01.467 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 01:53:01.468 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 01:53:01.468 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 01:53:01.469 | INFO     | core.logging:log_operation:178 | 📝 Initializing interactive console
2025-07-05 01:53:01.469 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 01:53:01.470 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 01:53:01.470 | INFO     | __main__:lifespan:78 | 🎮 Interactive Console Ready!
2025-07-05 01:53:01.471 | INFO     | __main__:lifespan:79 | ┌─ Quick Start Commands ──────────────────────────────────────────────┐
2025-07-05 01:53:01.471 | INFO     | __main__:lifespan:80 | │ 🤖 Models:    models popular | download default | load \<model>      │
2025-07-05 01:53:01.471 | INFO     | __main__:lifespan:81 | │              download llama-7b | download \<hf-url>                 │
2025-07-05 01:53:01.471 | INFO     | __main__:lifespan:82 | │ 🚀 Server:    status | config | memory | logs                       │
2025-07-05 01:53:01.472 | INFO     | __main__:lifespan:83 | │ 🔧 Dev:       exec \<cmd> | eval \<expr> | api \<endpoint>           │
2025-07-05 01:53:01.472 | INFO     | __main__:lifespan:84 | │ 🛠️  Utils:     help [cmd] | history | clear | exit                   │
2025-07-05 01:53:01.472 | INFO     | __main__:lifespan:85 | │                                                                      │
2025-07-05 01:53:01.472 | INFO     | __main__:lifespan:86 | │ 💡 Type 'help' for detailed command reference                       │
2025-07-05 01:53:01.473 | INFO     | __main__:lifespan:87 | │ 🌟 Popular: default, llama-7b, mistral-7b, codellama-7b, phi-3-mini │
2025-07-05 01:53:01.473 | INFO     | __main__:lifespan:88 | └──────────────────────────────────────────────────────────────────────┘
2025-07-05 01:53:01.475 | INFO     | core.logging:log_operation:178 | 🛑 Shutting down Reverie Code Studio Server
2025-07-05 01:53:01.475 | INFO     | core.logging:log_operation:178 | 📝 Stopping interactive console
2025-07-05 01:53:02.489 | INFO     | core.logging:log_operation:178 | 📝 Interactive console stopped
2025-07-05 01:53:02.489 | INFO     | core.logging:log_operation:178 | 🔌 Deactivating and unloading plugins
2025-07-05 01:53:02.490 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system shutdown complete
2025-07-05 01:53:02.490 | INFO     | core.logging:log_operation:178 | 🤖 Cleaning up model manager
2025-07-05 01:53:02.491 | INFO     | models.manager:cleanup:664 | ModelManager cleanup complete
2025-07-05 01:53:02.491 | INFO     | core.logging:log_operation:178 | 🛑 Server shutdown complete
2025-07-05 01:57:54.144 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 01:57:54.145 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 01:57:54.162 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-05 01:57:54.193 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 01:57:54.194 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 01:57:54.195 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 01:57:54.195 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 01:57:54.195 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 01:57:54.196 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 01:57:54.196 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 01:57:54.196 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 01:57:54.197 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 01:57:54.197 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 01:57:54.197 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 01:57:54.199 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 01:57:54.199 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 01:57:54.199 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 01:57:54.216 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 01:57:54.216 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 01:57:54.216 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 01:57:54.217 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 01:57:54.220 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 01:58:34.650 | ERROR    | api.endpoints.models:get_current_model:80 | Failed to get current model: No module named 'server.models'
2025-07-05 01:58:34.650 | WARNING  | core.exceptions:http_exception_handler:93 | HTTP exception: 500 - No module named 'server.models'
2025-07-05 01:58:34.985 | WARNING  | core.exceptions:http_exception_handler:93 | HTTP exception: 404 - Not Found
2025-07-05 02:07:08.181 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 02:07:08.182 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 02:07:08.199 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-05 02:07:08.229 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 02:07:08.229 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 02:07:08.230 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 02:07:08.230 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 02:07:08.231 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 02:07:08.231 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 02:07:08.231 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 02:07:08.232 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 02:07:08.232 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 02:07:08.232 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 02:07:08.233 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 02:07:08.233 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 02:07:08.233 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 02:07:08.233 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 02:07:08.251 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 02:07:08.251 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 02:07:08.251 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 02:07:08.252 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 02:07:08.255 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 02:10:16.742 | INFO     | core.logging:log_model_operation:194 | 🤖 Model download: Jan-nano-128k from https://huggingface.co/Menlo/Jan-nano-128k
2025-07-05 02:10:16.742 | INFO     | models.manager:download_model:151 | 🤗 Attempting HuggingFace Hub download...
2025-07-05 02:10:16.742 | INFO     | models.manager:_download_with_huggingface_hub:970 | ✅ HuggingFace Hub library available
2025-07-05 02:10:16.743 | INFO     | models.manager:_download_with_huggingface_hub:992 | 🤗 Starting HuggingFace Hub download
2025-07-05 02:10:16.743 | INFO     | models.manager:_download_with_huggingface_hub:993 | 📍 Repository: Menlo/Jan-nano-128k
2025-07-05 02:10:16.743 | INFO     | models.manager:_download_with_huggingface_hub:994 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 02:25:13.795 | INFO     | models.manager:_download_with_huggingface_hub:1009 | ✅ HuggingFace Hub download completed successfully
2025-07-05 02:25:13.796 | INFO     | models.manager:_download_with_huggingface_hub:1010 | 📂 Downloaded to: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 02:25:13.799 | INFO     | core.logging:log_model_operation:194 | 🤖 Model download_complete: Jan-nano-128k (30 files, 7692.7 MB)
2025-07-05 02:25:13.800 | INFO     | models.manager:download_model:183 | 📂 Location: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 04:30:22.588 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 04:30:22.590 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 04:30:22.605 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-05 04:30:22.691 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 04:30:22.692 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 04:30:22.694 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 04:30:22.694 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 04:30:22.694 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 04:30:22.695 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 04:30:22.695 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 04:30:22.696 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 04:30:22.696 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 04:30:22.696 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 04:30:22.697 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 04:30:22.697 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 04:30:22.697 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 04:30:22.698 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 04:30:22.823 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 04:30:22.824 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 04:30:22.824 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 04:30:22.825 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 04:30:22.828 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 04:30:30.893 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 04:30:30.893 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 04:30:31.192 | WARNING  | core.exceptions:http_exception_handler:93 | HTTP exception: 404 - Not Found
2025-07-05 04:32:10.610 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: transformers
2025-07-05 04:32:10.611 | INFO     | models.manager:_resolve_model_path:397 | 📥 Model not found locally, downloading: Jan-nano-128k
2025-07-05 04:32:10.611 | INFO     | models.manager:download_model:135 | Model Jan-nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 04:32:10.612 | INFO     | models.manager:_validate_model_files:426 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 04:32:10.612 | INFO     | models.manager:_load_transformers_model:533 | Loading model with Transformers backend...
2025-07-05 04:32:10.613 | INFO     | models.manager:_load_transformers_model:563 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-05 04:32:10.614 | INFO     | models.manager:_load_transformers_model:570 | Loading tokenizer...
2025-07-05 04:32:10.932 | INFO     | models.manager:_load_transformers_model:583 | Loading model...
2025-07-05 04:32:10.932 | INFO     | models.manager:_load_transformers_model:599 | Using Flash Attention 2 for improved performance
2025-07-05 04:32:16.160 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_failed: Jan-nano-128k after 5.55s: FlashAttention2 has been toggled on, but it cannot be used due to the following error: the package flash_attn seems to be not installed. Please refer to the documentation of https://huggingface.co/docs/transformers/perf_infer_gpu_one#flashattention-2 to install Flash Attention 2.
2025-07-05 04:40:54.281 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 04:40:54.285 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 04:40:54.302 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-05 04:40:54.350 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 04:40:54.351 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 04:40:54.351 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 04:40:54.352 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 04:40:54.352 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 04:40:54.352 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 04:40:54.353 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 04:40:54.353 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 04:40:54.353 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 04:40:54.353 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 04:40:54.354 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 04:40:54.354 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 04:40:54.354 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 04:40:54.355 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 04:40:54.377 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 04:40:54.378 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 04:40:54.378 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 04:40:54.379 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 04:40:54.382 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 05:04:24.925 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 05:04:24.927 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 05:04:24.943 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-05 05:04:25.010 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 05:04:25.011 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 05:04:25.011 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 05:04:25.012 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 05:04:25.012 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 05:04:25.012 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 05:04:25.013 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 05:04:25.013 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 05:04:25.013 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 05:04:25.014 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 05:04:25.014 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 05:04:25.014 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 05:04:25.014 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 05:04:25.015 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 05:04:25.036 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 05:04:25.037 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 05:04:25.037 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 05:04:25.038 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 05:04:25.041 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 05:04:37.536 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: transformers
2025-07-05 05:04:37.537 | INFO     | models.manager:_resolve_model_path:397 | 📥 Model not found locally, downloading: Jan-nano-128k
2025-07-05 05:04:37.537 | INFO     | models.manager:download_model:135 | Model Jan-nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 05:04:37.538 | INFO     | models.manager:_validate_model_files:426 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 05:04:37.538 | INFO     | models.manager:_load_transformers_model:533 | Loading model with Transformers backend...
2025-07-05 05:04:37.540 | INFO     | models.manager:_load_transformers_model:563 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-05 05:04:37.540 | INFO     | models.manager:_load_transformers_model:570 | Loading tokenizer...
2025-07-05 05:04:37.870 | INFO     | models.manager:_load_transformers_model:583 | Loading model...
2025-07-05 05:04:37.871 | INFO     | models.manager:_load_transformers_model:599 | Using Flash Attention 2 for improved performance
2025-07-05 05:04:38.474 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_failed: Jan-nano-128k after 0.94s: Could not import module 'Qwen3ForCausalLM'. Are this object's requirements defined correctly?
2025-07-05 05:04:51.913 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: vllm
2025-07-05 05:04:51.914 | WARNING  | models.manager:_validate_backend_compatibility:361 | ⚠️ vLLM not installed
2025-07-05 05:06:37.200 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 05:06:37.202 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 05:06:37.217 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-05 05:06:37.243 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 05:06:37.244 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 05:06:37.245 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 05:06:37.245 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 05:06:37.245 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 05:06:37.246 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 05:06:37.246 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 05:06:37.246 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 05:06:37.486 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 05:06:37.486 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 05:06:37.487 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 05:06:37.487 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 05:06:37.488 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 05:06:37.488 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 05:06:37.508 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 05:06:37.508 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 05:06:37.508 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 05:06:37.509 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 05:06:37.512 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 05:06:54.570 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: vllm
2025-07-05 05:06:55.206 | WARNING  | models.manager:_validate_backend_compatibility:361 | ⚠️ vLLM not installed
2025-07-05 05:08:31.638 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 05:08:31.640 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 05:08:31.657 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-05 05:08:31.683 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 05:08:31.684 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 05:08:31.685 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 05:08:31.685 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 05:08:31.685 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 05:08:31.686 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 05:08:31.686 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 05:08:31.686 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 05:08:31.686 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 05:08:31.687 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 05:08:31.687 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 05:08:31.687 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 05:08:31.687 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 05:08:31.688 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 05:08:31.705 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 05:08:31.706 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 05:08:31.706 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 05:08:31.707 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 05:08:31.710 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 05:08:42.427 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: vllm
2025-07-05 05:08:46.251 | WARNING  | models.manager:_validate_backend_compatibility:361 | ⚠️ vLLM not installed
2025-07-05 05:11:26.343 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: transformers
2025-07-05 05:11:26.344 | INFO     | models.manager:_resolve_model_path:397 | 📥 Model not found locally, downloading: Jan-nano-128k
2025-07-05 05:11:26.344 | INFO     | models.manager:download_model:135 | Model Jan-nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 05:11:26.345 | INFO     | models.manager:_validate_model_files:426 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-05 05:11:26.345 | INFO     | models.manager:_load_transformers_model:533 | Loading model with Transformers backend...
2025-07-05 05:11:26.346 | INFO     | models.manager:_load_transformers_model:563 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-05 05:11:26.347 | INFO     | models.manager:_load_transformers_model:570 | Loading tokenizer...
2025-07-05 05:11:26.615 | INFO     | models.manager:_load_transformers_model:583 | Loading model...
2025-07-05 05:11:26.615 | INFO     | models.manager:_load_transformers_model:599 | Using Flash Attention 2 for improved performance
2025-07-05 05:11:53.626 | INFO     | core.logging:log_operation:178 | 🛑 Shutting down Reverie Code Studio Server
2025-07-05 05:11:53.626 | INFO     | core.logging:log_operation:178 | 📝 Stopping interactive console
2025-07-05 05:11:54.635 | INFO     | core.logging:log_operation:178 | 📝 Interactive console stopped
2025-07-05 05:11:54.635 | INFO     | core.logging:log_operation:178 | 🔌 Deactivating and unloading plugins
2025-07-05 05:11:54.636 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system shutdown complete
2025-07-05 05:11:54.636 | INFO     | core.logging:log_operation:178 | 🤖 Cleaning up model manager
2025-07-05 05:11:54.636 | INFO     | models.manager:cleanup:664 | ModelManager cleanup complete
2025-07-05 05:11:54.638 | INFO     | core.logging:log_operation:178 | 🛑 Server shutdown complete
2025-07-05 05:11:54.640 | INFO     | __main__:signal_handler:324 | Received signal 2, shutting down...
2025-07-05 05:25:12.399 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 05:25:12.402 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 05:25:12.419 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-05 05:25:12.464 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 05:25:12.465 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 05:25:12.465 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 05:25:12.466 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 05:25:12.466 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 05:25:12.466 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 05:25:12.467 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 05:25:12.467 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 05:25:12.467 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 05:25:12.467 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 05:25:12.469 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 05:25:12.469 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 05:25:12.469 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 05:25:12.470 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 05:25:12.492 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 05:25:12.493 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 05:25:12.493 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 05:25:12.494 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 05:25:12.496 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 05:31:29.625 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 05:31:29.627 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 05:31:29.643 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-05 05:31:29.668 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 05:31:29.669 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 05:31:29.669 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 05:31:29.670 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 05:31:29.670 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 05:31:29.670 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 05:31:29.671 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 05:31:29.671 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 05:31:29.671 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 05:31:29.672 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 05:31:29.672 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 05:31:29.672 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 05:31:29.673 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 05:31:29.673 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 05:31:29.693 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 05:31:29.693 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 05:31:29.694 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 05:31:29.694 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 05:31:29.697 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 05:36:31.443 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-05 05:36:31.445 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-05 05:36:31.464 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-05 05:36:31.491 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-05 05:36:31.492 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-05 05:36:31.493 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-05 05:36:31.493 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-05 05:36:31.494 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-05 05:36:31.494 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-05 05:36:31.494 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-05 05:36:31.495 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-05 05:36:31.495 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-05 05:36:31.495 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-05 05:36:31.496 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-05 05:36:31.496 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-05 05:36:31.496 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-05 05:36:31.497 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-05 05:36:31.513 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 05:36:31.514 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 05:36:31.514 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-05 05:36:31.515 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-05 05:36:31.518 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-05 05:36:48.332 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-05 05:36:48.333 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-05 05:36:48.678 | WARNING  | core.exceptions:http_exception_handler:93 | HTTP exception: 404 - Not Found
2025-07-06 02:23:59.871 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-06 02:23:59.874 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-06 02:23:59.895 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-06 02:24:00.052 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-06 02:24:00.054 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-06 02:24:00.055 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-06 02:24:00.055 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-06 02:24:00.056 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-06 02:24:00.057 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-06 02:24:00.057 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-06 02:24:00.057 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-06 02:24:00.058 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-06 02:24:00.058 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-06 02:24:00.059 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-06 02:24:00.059 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-06 02:24:00.059 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-06 02:24:00.059 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-06 02:24:00.149 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-06 02:24:00.151 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-06 02:24:00.151 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-06 02:24:00.152 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-06 02:24:00.155 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-06 02:25:06.182 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: transformers
2025-07-06 02:25:06.182 | INFO     | models.manager:_resolve_model_path:397 | 📥 Model not found locally, downloading: Jan-nano-128k
2025-07-06 02:25:06.183 | INFO     | models.manager:download_model:135 | Model Jan-nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-06 02:25:06.183 | INFO     | models.manager:_validate_model_files:426 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-06 02:25:06.184 | INFO     | models.manager:_load_transformers_model:533 | Loading model with Transformers backend...
2025-07-06 02:25:06.186 | INFO     | models.manager:_load_transformers_model:563 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-06 02:25:06.186 | INFO     | models.manager:_load_transformers_model:570 | Loading tokenizer...
2025-07-06 02:25:06.654 | INFO     | models.manager:_load_transformers_model:583 | Loading model...
2025-07-06 02:25:06.655 | INFO     | models.manager:_load_transformers_model:599 | Using Flash Attention 2 for improved performance
2025-07-06 22:09:49.286 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-06 22:09:49.289 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-06 22:09:49.316 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-06 22:09:49.437 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-06 22:09:49.438 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-06 22:09:49.439 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-06 22:09:49.439 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-06 22:09:49.440 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-06 22:09:49.441 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-06 22:09:49.441 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-06 22:09:49.441 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-06 22:09:49.441 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-06 22:09:49.441 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-06 22:09:49.443 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-06 22:09:49.443 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-06 22:09:49.443 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-06 22:09:49.443 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-06 22:09:49.516 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-06 22:09:49.517 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-06 22:09:49.517 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-06 22:09:49.518 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-06 22:09:49.522 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-06 23:00:49.844 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-06 23:00:49.846 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-06 23:00:49.864 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-06 23:00:49.965 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-06 23:00:49.966 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-06 23:00:49.967 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-06 23:00:49.967 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-06 23:00:49.967 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-06 23:00:49.969 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-06 23:00:49.969 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-06 23:00:49.969 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-06 23:00:49.969 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-06 23:00:49.970 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-06 23:00:49.970 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-06 23:00:49.971 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-06 23:00:49.971 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-06 23:00:49.971 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-06 23:00:50.046 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-06 23:00:50.047 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-06 23:00:50.047 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-06 23:00:50.048 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-06 23:00:50.048 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-06 23:09:16.113 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-06 23:09:16.115 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-06 23:09:16.133 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-06 23:09:16.158 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-06 23:09:16.159 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-06 23:09:16.159 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-06 23:09:16.159 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-06 23:09:16.159 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-06 23:09:16.160 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-06 23:09:16.160 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-06 23:09:16.160 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-06 23:09:16.160 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-06 23:09:16.162 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-06 23:09:16.162 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-06 23:09:16.162 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-06 23:09:16.163 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-06 23:09:16.163 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-06 23:09:16.178 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-06 23:09:16.179 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-06 23:09:16.179 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-06 23:09:16.180 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-06 23:09:16.183 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-07 00:45:35.659 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-07 00:45:35.661 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-07 00:45:35.676 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-07 00:45:35.703 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-07 00:45:35.704 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-07 00:45:35.704 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-07 00:45:35.705 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-07 00:45:35.705 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-07 00:45:35.705 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-07 00:45:35.706 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-07 00:45:35.706 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-07 00:45:35.706 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-07 00:45:35.707 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-07 00:45:35.707 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-07 00:45:35.707 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-07 00:45:35.708 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-07 00:45:35.708 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-07 00:45:35.722 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-07 00:45:35.722 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-07 00:45:35.724 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-07 00:45:35.725 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-07 00:45:35.727 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-07 00:45:48.318 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: transformers
2025-07-07 00:45:48.319 | INFO     | models.manager:_resolve_model_path:397 | 📥 Model not found locally, downloading: Jan-nano-128k
2025-07-07 00:45:48.319 | INFO     | models.manager:download_model:135 | Model Jan-nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-07 00:45:48.320 | INFO     | models.manager:_validate_model_files:426 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-07 00:45:48.320 | INFO     | models.manager:_load_transformers_model:533 | Loading model with Transformers backend...
2025-07-07 00:45:48.322 | INFO     | models.manager:_load_transformers_model:563 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-07 00:45:48.322 | INFO     | models.manager:_load_transformers_model:570 | Loading tokenizer...
2025-07-07 00:45:48.686 | INFO     | models.manager:_load_transformers_model:583 | Loading model...
2025-07-07 00:45:48.686 | INFO     | models.manager:_load_transformers_model:599 | Using Flash Attention 2 for improved performance
2025-07-07 00:46:49.058 | INFO     | models.manager:_load_transformers_model:612 | Model loaded on device: cuda
2025-07-07 00:46:49.058 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Jan-nano-128k backend: transformers, context: 131072
2025-07-07 00:46:49.059 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 60.75s model: Jan-nano-128k
2025-07-07 00:46:52.687 | INFO     | core.logging:log_operation:178 | 🛑 Shutting down Reverie Code Studio Server
2025-07-07 00:46:52.688 | INFO     | core.logging:log_operation:178 | 📝 Stopping interactive console
2025-07-07 00:46:52.688 | INFO     | core.logging:log_operation:178 | 📝 Interactive console stopped
2025-07-07 00:46:52.688 | INFO     | core.logging:log_operation:178 | 🔌 Deactivating and unloading plugins
2025-07-07 00:46:52.689 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system shutdown complete
2025-07-07 00:46:52.689 | INFO     | core.logging:log_operation:178 | 🤖 Cleaning up model manager
2025-07-07 00:46:52.689 | INFO     | core.logging:log_model_operation:194 | 🤖 Model unload_start: Jan-nano-128k backend: transformers
2025-07-07 00:46:53.700 | INFO     | models.manager:unload_model:716 | 🔧 GPU Memory: 0.0GB allocated, 0.0GB cached
2025-07-07 00:46:53.700 | INFO     | core.logging:log_model_operation:194 | 🤖 Model unload_complete: Jan-nano-128k cleanup successful
2025-07-07 00:46:53.701 | INFO     | models.manager:cleanup:664 | ModelManager cleanup complete
2025-07-07 00:46:53.701 | INFO     | core.logging:log_operation:178 | 🛑 Server shutdown complete
2025-07-07 00:46:53.703 | INFO     | __main__:signal_handler:324 | Received signal 2, shutting down...
2025-07-07 01:11:53.850 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-07 01:11:53.852 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-07 01:11:53.874 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-07 01:11:53.926 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-07 01:11:53.927 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-07 01:11:53.927 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-07 01:11:53.927 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-07 01:11:53.927 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-07 01:11:53.928 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-07 01:11:53.928 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-07 01:11:53.929 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-07 01:11:53.929 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-07 01:11:53.929 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-07 01:11:53.929 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-07 01:11:53.931 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-07 01:11:53.931 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-07 01:11:53.931 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-07 01:11:53.958 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-07 01:11:53.958 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-07 01:11:53.959 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-07 01:11:53.959 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-07 01:11:53.963 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-07 01:12:37.176 | INFO     | models.manager:_resolve_target_device:137 | ✅ Using CUDA device: NVIDIA GeForce RTX 4060
2025-07-07 01:12:37.176 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: transformers, device: cuda
2025-07-07 01:12:37.177 | INFO     | models.manager:_resolve_model_path:441 | 📥 Model not found locally, downloading: Jan-nano-128k
2025-07-07 01:12:37.177 | INFO     | models.manager:download_model:179 | Model Jan-nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-07 01:12:37.177 | INFO     | models.manager:_validate_model_files:470 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-07 01:12:37.178 | INFO     | models.manager:_load_transformers_model:581 | Loading model with Transformers backend on device: cuda
2025-07-07 01:12:37.179 | INFO     | models.manager:_load_transformers_model:614 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-07 01:12:37.179 | INFO     | models.manager:_load_transformers_model:621 | Loading tokenizer...
2025-07-07 01:12:37.487 | INFO     | models.manager:_load_transformers_model:634 | Loading model...
2025-07-07 01:12:37.487 | INFO     | models.manager:_load_transformers_model:650 | Using Flash Attention 2 for improved performance
2025-07-07 01:12:46.128 | INFO     | core.logging:log_operation:178 | 🛑 Shutting down Reverie Code Studio Server
2025-07-07 01:12:46.129 | INFO     | core.logging:log_operation:178 | 📝 Stopping interactive console
2025-07-07 01:12:47.135 | INFO     | core.logging:log_operation:178 | 📝 Interactive console stopped
2025-07-07 01:12:47.135 | INFO     | core.logging:log_operation:178 | 🔌 Deactivating and unloading plugins
2025-07-07 01:12:47.136 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system shutdown complete
2025-07-07 01:12:47.136 | INFO     | core.logging:log_operation:178 | 🤖 Cleaning up model manager
2025-07-07 01:12:47.137 | INFO     | models.manager:cleanup:721 | ModelManager cleanup complete
2025-07-07 01:12:47.137 | INFO     | core.logging:log_operation:178 | 🛑 Server shutdown complete
2025-07-07 01:12:47.138 | INFO     | __main__:signal_handler:324 | Received signal 2, shutting down...
2025-07-07 05:07:23.382 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-07 05:07:23.386 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-07 05:07:23.406 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-07 05:07:23.465 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-07 05:07:23.466 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-07 05:07:23.466 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-07 05:07:23.466 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-07 05:07:23.466 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-07 05:07:23.467 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-07 05:07:23.467 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-07 05:07:23.468 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-07 05:07:23.468 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-07 05:07:23.470 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-07 05:07:23.470 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-07 05:07:23.470 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-07 05:07:23.471 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-07 05:07:23.471 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-07 05:07:23.532 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-07 05:07:23.534 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-07 05:07:23.534 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-07 05:07:23.535 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-07 05:07:23.537 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-07 05:07:33.321 | INFO     | models.manager:_resolve_target_device:137 | ✅ Using CUDA device: NVIDIA GeForce RTX 4060
2025-07-07 05:07:33.322 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: transformers, device: cuda
2025-07-07 05:07:33.324 | INFO     | models.manager:_resolve_model_path:441 | 📥 Model not found locally, downloading: Jan-nano-128k
2025-07-07 05:07:33.324 | INFO     | models.manager:download_model:179 | Model Jan-nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-07 05:07:33.325 | INFO     | models.manager:_validate_model_files:470 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-07 05:07:33.325 | INFO     | models.manager:_load_transformers_model:581 | Loading model with Transformers backend on device: cuda
2025-07-07 05:07:33.327 | INFO     | models.manager:_load_transformers_model:614 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-07 05:07:33.327 | INFO     | models.manager:_load_transformers_model:621 | Loading tokenizer...
2025-07-07 05:07:33.689 | INFO     | models.manager:_load_transformers_model:634 | Loading model...
2025-07-07 05:07:33.689 | INFO     | models.manager:_load_transformers_model:650 | Using Flash Attention 2 for improved performance
2025-07-07 05:08:28.631 | INFO     | models.manager:_load_transformers_model:663 | Model loaded on device: cuda
2025-07-07 05:08:28.631 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Jan-nano-128k backend: transformers, context: 131072
2025-07-07 05:08:28.631 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 55.30s model: Jan-nano-128k
2025-07-07 05:10:47.032 | INFO     | models.manager:_detect_device:104 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-07 05:10:47.033 | INFO     | models.manager:__init__:70 | ModelManager initialized with device: cuda
2025-07-07 05:10:47.419 | WARNING  | core.exceptions:http_exception_handler:93 | HTTP exception: 404 - Not Found
2025-07-07 07:04:22.479 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-07 07:04:22.483 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-07 07:04:22.501 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-07 07:04:22.619 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-07 07:04:22.620 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-07 07:04:22.621 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-07 07:04:22.621 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-07 07:04:22.621 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-07 07:04:22.622 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-07 07:04:22.622 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-07 07:04:22.624 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-07 07:04:22.624 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-07 07:04:22.625 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-07 07:04:22.625 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-07 07:04:22.625 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-07 07:04:22.626 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-07 07:04:22.626 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-07 07:04:22.713 | INFO     | models.manager:_detect_device:109 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-07 07:04:22.714 | INFO     | models.manager:__init__:75 | ModelManager initialized with device: cuda
2025-07-07 07:04:22.716 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-07 07:04:22.717 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-07 07:04:22.722 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-07 07:08:30.528 | INFO     | models.manager:_detect_device:109 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-07 07:08:30.529 | INFO     | models.manager:__init__:75 | ModelManager initialized with device: cuda
2025-07-07 07:08:31.103 | WARNING  | core.exceptions:http_exception_handler:93 | HTTP exception: 404 - Not Found
2025-07-07 09:30:57.132 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-07 09:30:57.135 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-07 09:30:57.151 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-07 09:30:57.273 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-07 09:30:57.274 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-07 09:30:57.275 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-07 09:30:57.275 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-07 09:30:57.275 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-07 09:30:57.276 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-07 09:30:57.276 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-07 09:30:57.276 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-07 09:30:57.276 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-07 09:30:57.277 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-07 09:30:57.277 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-07 09:30:57.277 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-07 09:30:57.278 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-07 09:30:57.278 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-07 09:30:57.347 | INFO     | models.manager:_detect_device:109 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-07 09:30:57.347 | INFO     | models.manager:__init__:75 | ModelManager initialized with device: cuda
2025-07-07 09:30:57.347 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-07 09:30:57.348 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-07 09:30:57.351 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-07 09:31:22.004 | INFO     | models.manager:_resolve_target_device:142 | ✅ Using CUDA device: NVIDIA GeForce RTX 4060
2025-07-07 09:31:22.005 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: transformers, device: cuda
2025-07-07 09:31:22.005 | INFO     | models.manager:_resolve_model_path:446 | 📥 Model not found locally, downloading: Jan-nano-128k
2025-07-07 09:31:22.006 | INFO     | models.manager:download_model:184 | Model Jan-nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-07 09:31:22.006 | INFO     | models.manager:_validate_model_files:475 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-07 09:31:22.006 | INFO     | models.manager:_load_transformers_model:586 | Loading model with Transformers backend on device: cuda
2025-07-07 09:31:22.009 | INFO     | models.manager:_load_transformers_model:619 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-07 09:31:22.009 | INFO     | models.manager:_load_transformers_model:626 | Loading tokenizer...
2025-07-07 09:31:22.339 | INFO     | models.manager:_load_transformers_model:639 | Loading model...
2025-07-07 09:31:22.340 | INFO     | models.manager:_load_transformers_model:655 | Using Flash Attention 2 for improved performance
2025-07-07 09:32:24.682 | INFO     | models.manager:_load_transformers_model:668 | Model loaded on device: cuda
2025-07-07 09:32:24.683 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Jan-nano-128k backend: transformers, context: 131072
2025-07-07 09:32:24.683 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 62.69s model: Jan-nano-128k
2025-07-09 03:51:17.423 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-09 03:51:17.426 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-09 03:51:17.444 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-09 03:51:17.567 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-09 03:51:17.568 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-09 03:51:17.569 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-09 03:51:17.570 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-09 03:51:17.570 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-09 03:51:17.570 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-09 03:51:17.571 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-09 03:51:17.571 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-09 03:51:17.572 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-09 03:51:17.572 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-09 03:51:17.573 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-09 03:51:17.573 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-09 03:51:17.573 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-09 03:51:17.574 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-09 03:51:18.690 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 03:51:18.691 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 03:51:18.692 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-09 03:51:18.693 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-09 03:51:18.696 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-09 03:51:33.878 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 03:51:33.879 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 03:51:58.536 | WARNING  | core.exceptions:http_exception_handler:93 | HTTP exception: 404 - Not Found
2025-07-09 03:52:35.412 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: transformers, device: cuda
2025-07-09 03:52:35.413 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Jan-nano-128k
2025-07-09 03:52:35.413 | INFO     | models.manager:download_model:191 | Model Jan-nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-09 03:52:35.414 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-09 03:52:35.414 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-09 03:52:35.416 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-09 03:52:35.416 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-09 03:52:35.798 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-09 03:52:35.798 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-09 03:53:51.910 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-09 03:53:51.910 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Jan-nano-128k backend: transformers, context: 131072
2025-07-09 03:53:51.910 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 76.50s model: Jan-nano-128k
2025-07-09 04:51:49.477 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-09 04:51:49.481 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-09 04:51:49.499 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-09 04:51:49.568 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-09 04:51:49.568 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-09 04:51:49.569 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-09 04:51:49.569 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-09 04:51:49.570 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-09 04:51:49.570 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-09 04:51:49.570 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-09 04:51:49.571 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-09 04:51:49.571 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-09 04:51:49.572 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-09 04:51:49.572 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-09 04:51:49.573 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-09 04:51:49.573 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-09 04:51:49.573 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-09 04:51:49.860 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 04:51:49.861 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 04:51:49.861 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-09 04:51:49.862 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-09 04:51:49.864 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-09 04:52:25.349 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: transformers, device: cuda
2025-07-09 04:52:25.350 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Jan-nano-128k
2025-07-09 04:52:25.350 | INFO     | models.manager:download_model:191 | Model Jan-nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-09 04:52:25.351 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-09 04:52:25.351 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-09 04:52:25.352 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-09 04:52:25.353 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-09 04:52:27.046 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-09 04:52:27.046 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-09 04:53:48.455 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-09 04:53:48.455 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Jan-nano-128k backend: transformers, context: 131072
2025-07-09 04:53:48.455 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 83.11s model: Jan-nano-128k
2025-07-09 07:32:52.831 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-09 07:32:52.833 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-09 07:32:52.851 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-09 07:32:52.910 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-09 07:32:52.910 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-09 07:32:52.911 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-09 07:32:52.911 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-09 07:32:52.911 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-09 07:32:52.912 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-09 07:32:52.912 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-09 07:32:52.913 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-09 07:32:52.913 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-09 07:32:52.913 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-09 07:32:52.914 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-09 07:32:52.914 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-09 07:32:52.914 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-09 07:32:52.915 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-09 07:32:52.995 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 07:32:52.995 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 07:32:52.996 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-09 07:32:52.997 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-09 07:32:53.000 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-09 07:33:09.332 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-nano-128k backend: transformers, device: cuda
2025-07-09 07:33:09.333 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Jan-nano-128k
2025-07-09 07:33:09.333 | INFO     | models.manager:download_model:191 | Model Jan-nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-09 07:33:09.334 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-nano-128k
2025-07-09 07:33:09.334 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-09 07:33:09.336 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-09 07:33:09.336 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-09 07:33:09.724 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-09 07:33:09.724 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-09 07:34:18.207 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-09 07:34:18.207 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Jan-nano-128k backend: transformers, context: 131072
2025-07-09 07:34:18.207 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 68.87s model: Jan-nano-128k
2025-07-09 07:47:26.565 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-09 07:47:26.567 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-09 07:47:26.585 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-09 07:47:26.638 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-09 07:47:26.640 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-09 07:47:26.640 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-09 07:47:26.641 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-09 07:47:26.641 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-09 07:47:26.642 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-09 07:47:26.642 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-09 07:47:26.642 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-09 07:47:26.642 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-09 07:47:26.643 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-09 07:47:26.643 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-09 07:47:26.643 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-09 07:47:26.644 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-09 07:47:26.644 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-09 07:47:26.669 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 07:47:26.670 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 07:47:26.670 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-09 07:47:26.672 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-09 07:47:26.676 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-09 07:47:54.419 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Jan-Nano-128k backend: transformers, device: cuda
2025-07-09 07:47:54.419 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Jan-Nano-128k
2025-07-09 07:47:54.420 | INFO     | models.manager:download_model:191 | Model Jan-Nano-128k already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-Nano-128k
2025-07-09 07:47:54.420 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Jan-Nano-128k
2025-07-09 07:47:54.421 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-09 07:47:54.424 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-09 07:47:54.424 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-09 07:47:54.760 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-09 07:47:54.760 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-09 07:48:46.608 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-09 07:48:46.608 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Jan-Nano-128k backend: transformers, context: 131072
2025-07-09 07:48:46.609 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 52.19s model: Jan-Nano-128k
2025-07-09 22:29:09.523 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-09 22:29:09.526 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-09 22:29:09.544 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-09 22:29:09.671 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-09 22:29:09.674 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-09 22:29:09.675 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-09 22:29:09.675 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-09 22:29:09.675 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-09 22:29:09.676 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-09 22:29:09.676 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-09 22:29:09.676 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-09 22:29:09.676 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-09 22:29:09.677 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-09 22:29:09.677 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-09 22:29:09.677 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-09 22:29:09.678 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-09 22:29:09.678 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-09 22:29:09.771 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 22:29:09.772 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 22:29:09.773 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-09 22:29:09.774 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-09 22:29:09.776 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-09 22:29:23.459 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-09 22:29:23.460 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-09 22:29:23.461 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:29:23.461 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:29:23.461 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-09 22:29:23.463 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-09 22:29:23.463 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-09 22:29:24.980 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-09 22:29:24.980 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-09 22:29:50.262 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-09 22:29:50.262 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-09 22:29:50.263 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 26.81s model: Qwen3-0.6B
2025-07-09 22:37:36.099 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-09 22:37:36.102 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-09 22:37:36.125 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-09 22:37:36.185 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-09 22:37:36.186 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-09 22:37:36.187 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-09 22:37:36.187 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-09 22:37:36.187 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-09 22:37:36.188 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-09 22:37:36.188 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-09 22:37:36.188 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-09 22:37:36.189 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-09 22:37:36.189 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-09 22:37:36.190 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-09 22:37:36.190 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-09 22:37:36.190 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-09 22:37:36.190 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-09 22:37:36.262 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 22:37:36.263 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 22:37:36.263 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-09 22:37:36.264 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-09 22:37:36.267 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-09 22:37:44.302 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-09 22:37:44.303 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-09 22:37:44.303 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:37:44.304 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:37:44.304 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-09 22:37:44.306 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-09 22:37:44.307 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-09 22:37:44.722 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-09 22:37:44.723 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-09 22:38:02.578 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-09 22:38:02.579 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-09 22:38:02.579 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 18.28s model: Qwen3-0.6B
2025-07-09 22:42:33.785 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-09 22:42:33.789 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-09 22:42:33.807 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-09 22:42:33.901 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-09 22:42:33.901 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-09 22:42:33.902 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-09 22:42:33.902 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-09 22:42:33.902 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-09 22:42:33.903 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-09 22:42:33.903 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-09 22:42:33.904 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-09 22:42:33.904 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-09 22:42:33.904 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-09 22:42:33.905 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-09 22:42:33.905 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-09 22:42:33.905 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-09 22:42:33.905 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-09 22:42:33.983 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 22:42:33.984 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 22:42:33.984 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-09 22:42:33.985 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-09 22:42:33.987 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-09 22:42:43.352 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-09 22:42:43.352 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-09 22:42:43.353 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:42:43.353 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:42:43.354 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-09 22:42:43.356 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-09 22:42:43.356 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-09 22:42:43.738 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-09 22:42:43.738 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-09 22:43:08.517 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-09 22:43:08.518 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-09 22:43:08.518 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 25.16s model: Qwen3-0.6B
2025-07-09 22:47:45.701 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-09 22:47:45.703 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-09 22:47:45.721 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-09 22:47:45.771 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-09 22:47:45.772 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-09 22:47:45.772 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-09 22:47:45.772 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-09 22:47:45.773 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-09 22:47:45.773 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-09 22:47:45.774 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-09 22:47:45.774 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-09 22:47:45.774 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-09 22:47:45.775 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-09 22:47:45.775 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-09 22:47:45.775 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-09 22:47:45.775 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-09 22:47:45.776 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-09 22:47:45.800 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 22:47:45.801 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 22:47:45.801 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-09 22:47:45.802 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-09 22:47:45.804 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-09 22:47:56.931 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-09 22:47:56.932 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-09 22:47:56.932 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:47:56.933 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:47:56.933 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-09 22:47:56.935 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-09 22:47:56.936 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-09 22:47:57.252 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-09 22:47:57.252 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-09 22:48:08.072 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-09 22:48:08.073 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-09 22:48:08.073 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 11.14s model: Qwen3-0.6B
2025-07-09 22:48:45.835 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-09 22:48:45.838 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-09 22:48:45.856 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-09 22:48:45.885 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-09 22:48:45.886 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-09 22:48:45.887 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-09 22:48:45.887 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-09 22:48:45.888 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-09 22:48:45.888 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-09 22:48:45.889 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-09 22:48:45.889 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-09 22:48:45.890 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-09 22:48:45.890 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-09 22:48:45.891 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-09 22:48:45.891 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-09 22:48:45.891 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-09 22:48:45.892 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-09 22:48:45.911 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 22:48:45.912 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 22:48:45.913 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-09 22:48:45.915 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-09 22:48:45.919 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-09 22:48:54.847 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-09 22:48:54.847 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-09 22:48:54.848 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:48:54.848 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:48:54.849 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-09 22:48:54.850 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-09 22:48:54.851 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-09 22:48:55.114 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-09 22:48:55.115 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-09 22:48:57.592 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-09 22:48:57.592 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-09 22:48:57.592 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 2.75s model: Qwen3-0.6B
2025-07-09 22:57:10.117 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-09 22:57:10.119 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-09 22:57:10.137 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-09 22:57:10.163 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-09 22:57:10.163 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-09 22:57:10.164 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-09 22:57:10.164 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-09 22:57:10.164 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-09 22:57:10.165 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-09 22:57:10.165 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-09 22:57:10.166 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-09 22:57:10.166 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-09 22:57:10.166 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-09 22:57:10.167 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-09 22:57:10.167 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-09 22:57:10.167 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-09 22:57:10.167 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-09 22:57:10.184 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 22:57:10.185 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 22:57:10.185 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-09 22:57:10.186 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-09 22:57:10.188 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-09 22:58:09.455 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-09 22:58:09.456 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-09 22:58:09.456 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:58:09.457 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 22:58:09.457 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-09 22:58:09.458 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-09 22:58:09.459 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-09 22:58:09.732 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-09 22:58:09.733 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-09 22:58:12.144 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-09 22:58:12.144 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-09 22:58:12.145 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 2.69s model: Qwen3-0.6B
2025-07-09 22:59:48.349 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-09 22:59:48.352 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-09 22:59:48.372 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-09 22:59:48.412 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-09 22:59:48.413 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-09 22:59:48.414 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-09 22:59:48.414 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-09 22:59:48.414 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-09 22:59:48.415 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-09 22:59:48.415 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-09 22:59:48.416 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-09 22:59:48.416 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-09 22:59:48.416 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-09 22:59:48.417 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-09 22:59:48.417 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-09 22:59:48.417 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-09 22:59:48.418 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-09 22:59:48.436 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-09 22:59:48.437 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-09 22:59:48.437 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-09 22:59:48.438 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-09 22:59:48.441 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-09 23:00:13.924 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-09 23:00:13.924 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-09 23:00:13.925 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 23:00:13.925 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-09 23:00:13.926 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-09 23:00:13.927 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-09 23:00:13.927 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-09 23:00:14.197 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-09 23:00:14.198 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-09 23:00:16.683 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-09 23:00:16.683 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-09 23:00:16.683 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 2.77s model: Qwen3-0.6B
2025-07-13 08:22:56.623 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-13 08:22:56.626 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-13 08:22:56.642 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-13 08:22:56.669 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-13 08:22:56.670 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-13 08:22:56.672 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-13 08:22:56.672 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-13 08:22:56.672 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-13 08:22:56.673 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-13 08:22:56.673 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-13 08:22:56.674 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-13 08:22:56.675 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-13 08:22:56.675 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-13 08:22:56.676 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-13 08:22:56.676 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-13 08:22:56.676 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-13 08:22:56.676 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-13 08:22:56.763 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-13 08:22:56.764 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-13 08:22:56.764 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-13 08:22:56.766 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-13 08:22:56.770 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-13 08:23:10.361 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-13 08:23:10.362 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-13 08:23:10.363 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-13 08:23:10.363 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-13 08:23:10.363 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-13 08:23:10.365 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-13 08:23:10.365 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-13 08:23:10.695 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-13 08:23:10.695 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-13 08:23:22.751 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-13 08:23:22.752 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-13 08:23:22.752 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 12.39s model: Qwen3-0.6B
2025-07-13 08:23:46.689 | INFO     | core.logging:log_operation:178 | 🛑 Shutting down Reverie Code Studio Server
2025-07-13 08:23:46.689 | INFO     | core.logging:log_operation:178 | 📝 Stopping interactive console
2025-07-13 08:23:47.693 | INFO     | core.logging:log_operation:178 | 📝 Interactive console stopped
2025-07-13 08:23:47.693 | INFO     | core.logging:log_operation:178 | 🔌 Deactivating and unloading plugins
2025-07-13 08:23:47.695 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system shutdown complete
2025-07-13 08:23:47.695 | INFO     | core.logging:log_operation:178 | 🤖 Cleaning up model manager
2025-07-13 08:23:47.695 | INFO     | core.logging:log_model_operation:194 | 🤖 Model unload_start: Qwen3-0.6B backend: transformers
2025-07-13 08:23:47.905 | WARNING  | models.manager:unload_model:886 | ⚠️ Model CPU transfer warning: CUDA error: an illegal memory access was encountered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-07-13 08:23:48.007 | ERROR    | models.manager:unload_model:924 | ❌ Error during model unload: CUDA error: an illegal memory access was encountered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-07-13 08:23:48.033 | INFO     | __main__:signal_handler:324 | Received signal 2, shutting down...
2025-07-13 08:24:01.332 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-13 08:24:01.334 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-13 08:24:01.350 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-13 08:24:01.370 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-13 08:24:01.371 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-13 08:24:01.371 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-13 08:24:01.371 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-13 08:24:01.373 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-13 08:24:01.373 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-13 08:24:01.374 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-13 08:24:01.374 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-13 08:24:01.374 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-13 08:24:01.374 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-13 08:24:01.375 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-13 08:24:01.375 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-13 08:24:01.375 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-13 08:24:01.376 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-13 08:24:01.391 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-13 08:24:01.391 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-13 08:24:01.392 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-13 08:24:01.393 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-13 08:24:01.395 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-13 08:24:27.689 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-13 08:24:27.690 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-13 08:24:27.690 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-13 08:24:27.692 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-13 08:24:27.692 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-13 08:24:27.695 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-13 08:24:27.696 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-13 08:24:27.960 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-13 08:24:27.960 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-13 08:24:30.296 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-13 08:24:30.296 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-13 08:24:30.296 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 2.61s model: Qwen3-0.6B
2025-07-13 08:29:03.306 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-13 08:29:03.309 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-13 08:29:03.326 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-13 08:29:03.348 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-13 08:29:03.348 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-13 08:29:03.349 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-13 08:29:03.349 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-13 08:29:03.350 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-13 08:29:03.350 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-13 08:29:03.350 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-13 08:29:03.351 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-13 08:29:03.351 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-13 08:29:03.351 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-13 08:29:03.353 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-13 08:29:03.353 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-13 08:29:03.353 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-13 08:29:03.353 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-13 08:29:03.369 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-13 08:29:03.370 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-13 08:29:03.370 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-13 08:29:03.371 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-13 08:29:03.374 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-13 08:32:16.943 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-13 08:32:16.944 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-13 08:32:16.944 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-13 08:32:16.944 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-13 08:32:16.944 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-13 08:32:16.946 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-13 08:32:16.946 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-13 08:32:17.201 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-13 08:32:17.202 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-13 08:32:19.497 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-13 08:32:19.497 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-13 08:32:19.497 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 2.56s model: Qwen3-0.6B
2025-07-13 08:43:15.217 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-13 08:43:15.219 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-13 08:43:15.235 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-13 08:43:15.255 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-13 08:43:15.256 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-13 08:43:15.256 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-13 08:43:15.256 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-13 08:43:15.257 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-13 08:43:15.257 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-13 08:43:15.257 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-13 08:43:15.258 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-13 08:43:15.258 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-13 08:43:15.258 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-13 08:43:15.259 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-13 08:43:15.259 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-13 08:43:15.259 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-13 08:43:15.259 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-13 08:43:15.274 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-13 08:43:15.275 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-13 08:43:15.275 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-13 08:43:15.276 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-13 08:43:15.278 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-13 08:43:22.524 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-13 08:43:22.525 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-13 08:43:22.525 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-13 08:43:22.525 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-13 08:43:22.526 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-13 08:43:22.527 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-13 08:43:22.527 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-13 08:43:22.797 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-13 08:43:22.797 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-13 08:43:25.166 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-13 08:43:25.166 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-13 08:43:25.167 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 2.64s model: Qwen3-0.6B
2025-07-13 09:02:43.678 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-13 09:02:43.682 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-13 09:02:43.703 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-13 09:02:43.827 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-13 09:02:43.827 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-13 09:02:43.828 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-13 09:02:43.828 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-13 09:02:43.829 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-13 09:02:43.829 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-13 09:02:43.830 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-13 09:02:43.830 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-13 09:02:43.830 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-13 09:02:43.831 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-13 09:02:43.831 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-13 09:02:43.832 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-13 09:02:43.832 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-13 09:02:43.832 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-13 09:02:43.927 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-13 09:02:43.927 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-13 09:02:43.928 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-13 09:02:43.929 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-13 09:02:43.931 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-13 09:02:52.978 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-13 09:02:52.979 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-13 09:02:52.980 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-13 09:02:52.981 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-13 09:02:52.982 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-13 09:02:52.986 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-13 09:02:52.986 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-13 09:02:53.394 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-13 09:02:53.395 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-13 09:03:07.998 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-13 09:03:07.998 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-13 09:03:07.999 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 15.01s model: Qwen3-0.6B
2025-07-21 23:09:54.908 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-21 23:09:54.910 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-21 23:09:54.927 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-21 23:09:55.025 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-21 23:09:55.025 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-21 23:09:55.025 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-21 23:09:55.026 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-21 23:09:55.027 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-21 23:09:55.027 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-21 23:09:55.027 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-21 23:09:55.029 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-21 23:09:55.030 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-21 23:09:55.030 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-21 23:09:55.030 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-21 23:09:55.031 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-21 23:09:55.031 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-21 23:09:55.031 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-21 23:09:55.108 | INFO     | models.manager:_detect_device:116 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-21 23:09:55.109 | INFO     | models.manager:__init__:82 | ModelManager initialized with device: cuda
2025-07-21 23:09:55.109 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-21 23:09:55.110 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-21 23:09:55.113 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-21 23:10:13.325 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_start: Qwen3-0.6B backend: transformers, device: cuda
2025-07-21 23:10:13.327 | INFO     | models.manager:_resolve_model_path:473 | 📥 Model not found locally, downloading: Qwen3-0.6B
2025-07-21 23:10:13.327 | INFO     | models.manager:download_model:191 | Model Qwen3-0.6B already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-21 23:10:13.327 | INFO     | models.manager:_validate_model_files:502 | ✅ Model files validated: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\Qwen3-0.6B
2025-07-21 23:10:13.328 | INFO     | models.manager:_load_transformers_model:617 | Loading model with Transformers backend on device: cuda
2025-07-21 23:10:13.330 | INFO     | models.manager:_load_transformers_model:650 | Using 4-bit quantization (optimized for Jan-nano-128k)
2025-07-21 23:10:13.330 | INFO     | models.manager:_load_transformers_model:657 | Loading tokenizer...
2025-07-21 23:10:13.643 | INFO     | models.manager:_load_transformers_model:670 | Loading model...
2025-07-21 23:10:13.643 | INFO     | models.manager:_load_transformers_model:686 | Using Flash Attention 2 for improved performance
2025-07-21 23:10:29.170 | INFO     | models.manager:_load_transformers_model:699 | Model loaded on device: cuda
2025-07-21 23:10:29.171 | INFO     | core.logging:log_model_operation:194 | 🤖 Model load_complete: Qwen3-0.6B backend: transformers, context: 131072
2025-07-21 23:10:29.171 | INFO     | core.logging:log_performance:183 | ⚡ Performance: model_loading completed in 15.84s model: Qwen3-0.6B
2025-07-22 10:58:16.390 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-22 10:58:16.394 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-22 10:58:16.409 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-22 10:58:16.505 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-22 10:58:16.505 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-22 10:58:16.506 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-22 10:58:16.506 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-22 10:58:16.507 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-22 10:58:16.507 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-22 10:58:16.507 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-22 10:58:16.508 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-22 10:58:16.508 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-22 10:58:16.509 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-22 10:58:16.509 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-22 10:58:16.509 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-22 10:58:16.509 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-22 10:58:16.509 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-22 10:58:16.542 | INFO     | models.manager:_detect_device:146 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-22 10:58:16.543 | INFO     | models.manager:__init__:100 | ModelManager initialized with device: cuda
2025-07-22 10:58:16.543 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-22 10:58:16.545 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-22 10:58:16.547 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-22 11:45:09.722 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-22 11:45:09.724 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-22 11:45:09.741 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-22 11:45:09.769 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-22 11:45:09.769 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-22 11:45:09.770 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-22 11:45:09.770 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-22 11:45:09.770 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-22 11:45:09.771 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-22 11:45:09.771 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-22 11:45:09.772 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-22 11:45:09.772 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-22 11:45:09.772 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-22 11:45:09.772 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-22 11:45:09.773 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-22 11:45:09.773 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-22 11:45:09.773 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-22 11:45:09.791 | INFO     | models.manager:_detect_device:146 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-22 11:45:09.791 | INFO     | models.manager:__init__:100 | ModelManager initialized with device: cuda
2025-07-22 11:45:09.792 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-22 11:45:09.793 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-22 11:45:09.795 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-22 11:45:50.589 | INFO     | core.logging:log_model_operation:194 | 🤖 Model download: lucy_128k-Q8_0.gguf?download=true from https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:45:50.590 | INFO     | models.manager:download_model:237 | 🤗 Attempting HuggingFace Hub download...
2025-07-22 11:45:50.590 | INFO     | models.manager:_download_with_huggingface_hub:1540 | ✅ HuggingFace Hub library available
2025-07-22 11:45:50.591 | INFO     | models.manager:_download_with_huggingface_hub:1562 | 🤗 Starting HuggingFace Hub download
2025-07-22 11:45:50.591 | INFO     | models.manager:_download_with_huggingface_hub:1563 | 📍 Repository: Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:45:50.592 | INFO     | models.manager:_download_with_huggingface_hub:1564 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:45:50.592 | ERROR    | models.manager:_download_with_huggingface_hub:1584 | Download error: Repo id must be in the form 'repo_name' or 'namespace/repo_name': 'Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true'. Use `repo_type` argument if needed.
2025-07-22 11:45:50.593 | INFO     | models.manager:_download_with_git:1441 | ✅ Git library available
2025-07-22 11:45:50.617 | INFO     | models.manager:_download_with_git:1453 | ✅ Git command available: git version 2.47.0.windows.1
2025-07-22 11:45:50.617 | INFO     | core.logging:log_git_operation:199 | 🔄 Git clone: Starting Git download of lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:45:50.617 | INFO     | models.manager:_download_with_git:1490 | 📍 Source: https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:45:50.618 | INFO     | models.manager:_download_with_git:1491 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:45:50.677 | ERROR    | models.manager:_download_with_git:1516 | Git command error: Cmd('git') failed due to: exit code(128)
  cmdline: git clone -v --depth=1 --single-branch --progress -- https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:45:50.685 | INFO     | models.manager:_download_with_http:1608 | 🌐 Starting HTTP download of lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:45:50.687 | INFO     | models.manager:_download_with_http:1609 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:45:50.687 | ERROR    | models.manager:_download_with_http:1654 | HTTP download failed: [WinError 123] The filename, directory name, or volume label syntax is incorrect: 'H:\\RIL\\Rilance Code Studio\\Rilance Code Studio Augment\\server\\models\\llm\\lucy_128k-Q8_0.gguf?download=true'
2025-07-22 11:45:50.687 | ERROR    | models.manager:download_model:276 | Failed to download model lucy_128k-Q8_0.gguf?download=true: All download methods failed. Please check your internet connection and try again.
2025-07-22 11:54:15.877 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-22 11:54:15.879 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-22 11:54:15.895 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-22 11:54:15.921 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-22 11:54:15.922 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-22 11:54:15.922 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-22 11:54:15.923 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-22 11:54:15.923 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-22 11:54:15.923 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-22 11:54:15.924 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-22 11:54:15.924 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-22 11:54:15.924 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-22 11:54:15.926 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-22 11:54:15.926 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-22 11:54:15.926 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-22 11:54:15.927 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-22 11:54:15.927 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-22 11:54:15.940 | INFO     | models.manager:_detect_device:146 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-22 11:54:15.940 | INFO     | models.manager:__init__:100 | ModelManager initialized with device: cuda
2025-07-22 11:54:15.942 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-22 11:54:15.942 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-22 11:54:15.945 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-22 11:54:15.946 | INFO     | core.logging:log_operation:178 | 🛑 Shutting down Reverie Code Studio Server
2025-07-22 11:54:15.946 | INFO     | core.logging:log_operation:178 | 📝 Stopping interactive console
2025-07-22 11:54:16.952 | INFO     | core.logging:log_operation:178 | 📝 Interactive console stopped
2025-07-22 11:54:16.953 | INFO     | core.logging:log_operation:178 | 🔌 Deactivating and unloading plugins
2025-07-22 11:54:16.954 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system shutdown complete
2025-07-22 11:54:16.954 | INFO     | core.logging:log_operation:178 | 🤖 Cleaning up model manager
2025-07-22 11:54:16.954 | INFO     | models.manager:cleanup:1006 | ModelManager cleanup complete
2025-07-22 11:54:16.954 | INFO     | core.logging:log_operation:178 | 🛑 Server shutdown complete
2025-07-22 11:54:31.112 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-22 11:54:31.115 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-22 11:54:31.132 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-22 11:54:31.156 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-22 11:54:31.156 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-22 11:54:31.157 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-22 11:54:31.157 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-22 11:54:31.157 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-22 11:54:31.158 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-22 11:54:31.158 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-22 11:54:31.158 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-22 11:54:31.158 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-22 11:54:31.159 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-22 11:54:31.159 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-22 11:54:31.159 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-22 11:54:31.160 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-22 11:54:31.160 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-22 11:54:31.176 | INFO     | models.manager:_detect_device:146 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-22 11:54:31.177 | INFO     | models.manager:__init__:100 | ModelManager initialized with device: cuda
2025-07-22 11:54:31.177 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-22 11:54:31.177 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-22 11:54:31.181 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-22 11:55:17.295 | INFO     | core.logging:log_model_operation:194 | 🤖 Model download: lucy_128k-Q8_0.gguf?download=true from https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:55:17.295 | INFO     | models.manager:download_model:237 | 🤗 Attempting HuggingFace Hub download...
2025-07-22 11:55:17.296 | INFO     | models.manager:_download_with_huggingface_hub:1540 | ✅ HuggingFace Hub library available
2025-07-22 11:55:17.296 | INFO     | models.manager:_download_with_huggingface_hub:1562 | 🤗 Starting HuggingFace Hub download
2025-07-22 11:55:17.296 | INFO     | models.manager:_download_with_huggingface_hub:1563 | 📍 Repository: Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:55:17.296 | INFO     | models.manager:_download_with_huggingface_hub:1564 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:55:17.297 | ERROR    | models.manager:_download_with_huggingface_hub:1584 | Download error: Repo id must be in the form 'repo_name' or 'namespace/repo_name': 'Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true'. Use `repo_type` argument if needed.
2025-07-22 11:55:17.297 | INFO     | models.manager:_download_with_git:1441 | ✅ Git library available
2025-07-22 11:55:17.325 | INFO     | models.manager:_download_with_git:1453 | ✅ Git command available: git version 2.47.0.windows.1
2025-07-22 11:55:17.325 | INFO     | core.logging:log_git_operation:199 | 🔄 Git clone: Starting Git download of lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:55:17.325 | INFO     | models.manager:_download_with_git:1490 | 📍 Source: https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:55:17.326 | INFO     | models.manager:_download_with_git:1491 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:55:17.383 | ERROR    | models.manager:_download_with_git:1516 | Git command error: Cmd('git') failed due to: exit code(128)
  cmdline: git clone -v --depth=1 --single-branch --progress -- https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:55:17.387 | INFO     | models.manager:_download_with_http:1608 | 🌐 Starting HTTP download of lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:55:17.388 | INFO     | models.manager:_download_with_http:1609 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 11:55:17.388 | ERROR    | models.manager:_download_with_http:1654 | HTTP download failed: [WinError 123] The filename, directory name, or volume label syntax is incorrect: 'H:\\RIL\\Rilance Code Studio\\Rilance Code Studio Augment\\server\\models\\llm\\lucy_128k-Q8_0.gguf?download=true'
2025-07-22 11:55:17.388 | ERROR    | models.manager:download_model:276 | Failed to download model lucy_128k-Q8_0.gguf?download=true: All download methods failed. Please check your internet connection and try again.
2025-07-22 11:56:08.968 | INFO     | core.logging:log_model_operation:194 | 🤖 Model download: lucy_128k-Q8_0.gguf from https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf
2025-07-22 11:56:08.968 | INFO     | models.manager:download_model:237 | 🤗 Attempting HuggingFace Hub download...
2025-07-22 11:56:08.968 | INFO     | models.manager:_download_with_huggingface_hub:1540 | ✅ HuggingFace Hub library available
2025-07-22 11:56:08.969 | INFO     | models.manager:_download_with_huggingface_hub:1562 | 🤗 Starting HuggingFace Hub download
2025-07-22 11:56:08.969 | INFO     | models.manager:_download_with_huggingface_hub:1563 | 📍 Repository: Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf
2025-07-22 11:56:08.969 | INFO     | models.manager:_download_with_huggingface_hub:1564 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf
2025-07-22 11:56:08.970 | ERROR    | models.manager:_download_with_huggingface_hub:1584 | Download error: Repo id must be in the form 'repo_name' or 'namespace/repo_name': 'Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf'. Use `repo_type` argument if needed.
2025-07-22 11:56:08.970 | INFO     | models.manager:_download_with_git:1441 | ✅ Git library available
2025-07-22 11:56:08.996 | INFO     | models.manager:_download_with_git:1453 | ✅ Git command available: git version 2.47.0.windows.1
2025-07-22 11:56:08.998 | INFO     | core.logging:log_git_operation:199 | 🔄 Git clone: Starting Git download of lucy_128k-Q8_0.gguf
2025-07-22 11:56:08.998 | INFO     | models.manager:_download_with_git:1490 | 📍 Source: https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf
2025-07-22 11:56:08.998 | INFO     | models.manager:_download_with_git:1491 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf
2025-07-22 11:56:10.146 | ERROR    | models.manager:_download_with_git:1516 | Git command error: Cmd('git') failed due to: exit code(128)
  cmdline: git clone -v --depth=1 --single-branch --progress -- https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf
2025-07-22 11:56:10.147 | INFO     | models.manager:_download_with_http:1608 | 🌐 Starting HTTP download of lucy_128k-Q8_0.gguf
2025-07-22 11:56:10.147 | INFO     | models.manager:_download_with_http:1609 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf
2025-07-22 12:08:22.954 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-22 12:08:22.956 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-22 12:08:22.973 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-22 12:08:22.999 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-22 12:08:22.999 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-22 12:08:23.000 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-22 12:08:23.000 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-22 12:08:23.000 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-22 12:08:23.002 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-22 12:08:23.002 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-22 12:08:23.002 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-22 12:08:23.003 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-22 12:08:23.003 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-22 12:08:23.003 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-22 12:08:23.004 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-22 12:08:23.004 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-22 12:08:23.004 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-22 12:08:23.018 | INFO     | models.manager:_detect_device:146 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-22 12:08:23.019 | INFO     | models.manager:__init__:100 | ModelManager initialized with device: cuda
2025-07-22 12:08:23.019 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-22 12:08:23.020 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-22 12:08:23.023 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-22 12:08:25.919 | INFO     | models.manager:download_model:230 | GGUF file lucy_128k-Q8_0.gguf already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf
2025-07-22 12:08:41.976 | INFO     | models.manager:_download_single_gguf_file:1710 | 🌐 Starting direct GGUF file download
2025-07-22 12:08:41.977 | INFO     | models.manager:_download_single_gguf_file:1711 | 📂 Source: https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf
2025-07-22 12:08:41.977 | INFO     | models.manager:_download_single_gguf_file:1712 | 📍 Target: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf
2025-07-22 12:09:03.023 | ERROR    | models.manager:_download_single_gguf_file:1756 | ❌ Single GGUF file download failed: Cannot connect to host huggingface.co:443 ssl:default [Connect call failed ('*************', 443)]
2025-07-22 12:10:08.631 | INFO     | models.manager:_download_single_gguf_file:1710 | 🌐 Starting direct GGUF file download
2025-07-22 12:10:08.631 | INFO     | models.manager:_download_single_gguf_file:1711 | 📂 Source: https://hf-mirror.com/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf
2025-07-22 12:10:08.631 | INFO     | models.manager:_download_single_gguf_file:1712 | 📍 Target: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf
2025-07-22 20:22:38.879 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-22 20:22:38.882 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-22 20:22:38.900 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-22 20:22:38.990 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-22 20:22:38.992 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-22 20:22:38.992 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-22 20:22:38.992 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-22 20:22:38.992 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-22 20:22:38.993 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-22 20:22:38.993 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-22 20:22:38.994 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-22 20:22:38.994 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-22 20:22:38.994 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-22 20:22:38.994 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-22 20:22:38.995 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-22 20:22:38.995 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-22 20:22:38.995 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-22 20:22:39.061 | INFO     | models.manager:_detect_device:146 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-22 20:22:39.062 | INFO     | models.manager:__init__:100 | ModelManager initialized with device: cuda
2025-07-22 20:22:39.062 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-22 20:22:39.063 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-22 20:22:39.066 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-22 20:23:08.643 | INFO     | core.logging:log_model_operation:194 | 🤖 Model download: lucy_128k-Q8_0.gguf?download=true from https://hf-mirror.com/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true
2025-07-22 20:23:08.643 | INFO     | models.manager:download_model:273 | 🤗 Attempting HuggingFace Hub download...
2025-07-22 20:23:08.644 | INFO     | models.manager:_download_with_huggingface_hub:1576 | ✅ HuggingFace Hub library available
2025-07-22 20:23:08.644 | INFO     | models.manager:_download_with_huggingface_hub:1598 | 🤗 Starting HuggingFace Hub download
2025-07-22 20:23:08.644 | INFO     | models.manager:_download_with_huggingface_hub:1599 | 📍 Repository: lucy/128k-Q8/0.gguf?download=true
2025-07-22 20:23:08.644 | INFO     | models.manager:_download_with_huggingface_hub:1600 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 20:23:08.645 | ERROR    | models.manager:_download_with_huggingface_hub:1620 | Download error: Repo id must be in the form 'repo_name' or 'namespace/repo_name': 'lucy/128k-Q8/0.gguf?download=true'. Use `repo_type` argument if needed.
2025-07-22 20:23:08.645 | INFO     | models.manager:_download_with_git:1477 | ✅ Git library available
2025-07-22 20:23:08.669 | INFO     | models.manager:_download_with_git:1489 | ✅ Git command available: git version 2.47.0.windows.1
2025-07-22 20:23:08.669 | INFO     | core.logging:log_git_operation:199 | 🔄 Git clone: Starting Git download of lucy_128k-Q8_0.gguf?download=true
2025-07-22 20:23:08.670 | INFO     | models.manager:_download_with_git:1526 | 📍 Source: https://hf-mirror.com/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true
2025-07-22 20:23:08.670 | INFO     | models.manager:_download_with_git:1527 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 20:23:08.722 | ERROR    | models.manager:_download_with_git:1552 | Git command error: Cmd('git') failed due to: exit code(128)
  cmdline: git clone -v --depth=1 --single-branch --progress -- https://hf-mirror.com/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 20:23:08.732 | INFO     | models.manager:_download_with_http:1644 | 🌐 Starting HTTP download of lucy_128k-Q8_0.gguf?download=true
2025-07-22 20:23:08.732 | INFO     | models.manager:_download_with_http:1645 | 📂 Destination: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf?download=true
2025-07-22 20:23:08.732 | ERROR    | models.manager:_download_with_http:1690 | HTTP download failed: [WinError 123] The filename, directory name, or volume label syntax is incorrect: 'H:\\RIL\\Rilance Code Studio\\Rilance Code Studio Augment\\server\\models\\llm\\lucy_128k-Q8_0.gguf?download=true'
2025-07-22 20:23:08.732 | ERROR    | models.manager:download_model:312 | Failed to download model lucy_128k-Q8_0.gguf?download=true: All download methods failed. Please check your internet connection and try again.
2025-07-22 20:23:14.518 | INFO     | models.manager:_download_single_gguf_file:1710 | 🌐 Starting direct GGUF file download
2025-07-22 20:23:14.518 | INFO     | models.manager:_download_single_gguf_file:1711 | 📂 Source: https://hf-mirror.com/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf
2025-07-22 20:23:14.520 | INFO     | models.manager:_download_single_gguf_file:1712 | 📍 Target: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf
2025-07-22 20:25:01.350 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-07-22 20:25:01.352 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-07-22 20:25:01.368 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-07-22 20:25:01.389 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-07-22 20:25:01.391 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-07-22 20:25:01.391 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-07-22 20:25:01.391 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-07-22 20:25:01.392 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-07-22 20:25:01.392 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-07-22 20:25:01.392 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-07-22 20:25:01.393 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-07-22 20:25:01.393 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-07-22 20:25:01.393 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-07-22 20:25:01.394 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-07-22 20:25:01.394 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-07-22 20:25:01.394 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-07-22 20:25:01.394 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-07-22 20:25:01.414 | INFO     | models.manager:_detect_device:146 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-07-22 20:25:01.414 | INFO     | models.manager:__init__:100 | ModelManager initialized with device: cuda
2025-07-22 20:25:01.414 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-07-22 20:25:01.416 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-07-22 20:25:01.418 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-07-22 20:25:03.566 | INFO     | models.manager:download_model:230 | GGUF file lucy_128k-Q8_0.gguf already exists at H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf
2025-07-22 20:25:16.670 | INFO     | models.manager:_download_single_gguf_file:1710 | 🌐 Starting direct GGUF file download
2025-07-22 20:25:16.670 | INFO     | models.manager:_download_single_gguf_file:1711 | 📂 Source: https://hf-mirror.com/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf
2025-07-22 20:25:16.671 | INFO     | models.manager:_download_single_gguf_file:1712 | 📍 Target: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf
2025-07-22 20:30:17.420 | ERROR    | models.manager:_download_single_gguf_file:1756 | ❌ Single GGUF file download failed: 
2025-07-22 20:33:00.798 | INFO     | models.manager:_download_single_gguf_file:1710 | 🌐 Starting direct GGUF file download
2025-07-22 20:33:00.798 | INFO     | models.manager:_download_single_gguf_file:1711 | 📂 Source: https://hf-mirror.com/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf
2025-07-22 20:33:00.798 | INFO     | models.manager:_download_single_gguf_file:1712 | 📍 Target: H:\RIL\Rilance Code Studio\Rilance Code Studio Augment\server\models\llm\lucy_128k-Q8_0.gguf
2025-08-01 21:35:16.443 | SUCCESS  | core.logging:setup_logging:171 | 🎨 Enhanced logging system initialized
2025-08-01 21:35:16.446 | INFO     | core.exceptions:setup_exception_handlers:137 | Exception handlers configured
2025-08-01 21:35:16.467 | INFO     | __main__:main:332 | 🌟 Starting server on http://127.0.0.1:8001
2025-08-01 21:35:16.614 | INFO     | core.logging:log_operation:178 | 🚀 Starting Reverie Code Studio Server
2025-08-01 21:35:16.616 | INFO     | plugins.registry:__init__:39 | Plugin registry initialized at: server\plugins\registry
2025-08-01 21:35:16.617 | INFO     | plugins.loader:__init__:42 | Plugin loader initialized with 3 directories
2025-08-01 21:35:16.617 | INFO     | plugins.manager:__init__:54 | Plugin manager initialized
2025-08-01 21:35:16.618 | INFO     | core.logging:log_operation:178 | 🔌 Initializing plugin system
2025-08-01 21:35:16.618 | INFO     | plugins.registry:initialize:45 | Plugin registry loaded with 0 plugins
2025-08-01 21:35:16.618 | INFO     | plugins.manager:discover_plugins:71 | Discovering plugins...
2025-08-01 21:35:16.619 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\builtin
2025-08-01 21:35:16.619 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\community
2025-08-01 21:35:16.620 | INFO     | plugins.loader:discover_plugins:54 | Scanning for plugins in: server\plugins\custom
2025-08-01 21:35:16.620 | INFO     | plugins.loader:discover_plugins:75 | Discovered 0 plugins
2025-08-01 21:35:16.620 | INFO     | plugins.manager:initialize:67 | Plugin manager initialization complete
2025-08-01 21:35:16.621 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system initialized successfully
2025-08-01 21:35:16.621 | INFO     | core.logging:log_operation:178 | 🤖 Initializing model manager
2025-08-01 21:35:16.714 | INFO     | models.manager:_detect_device:146 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-08-01 21:35:16.715 | INFO     | models.manager:__init__:100 | ModelManager initialized with device: cuda
2025-08-01 21:35:16.716 | INFO     | core.logging:log_operation:178 | 🤖 Model manager ready (no auto-loading)
2025-08-01 21:35:16.717 | INFO     | core.logging:log_operation:178 | 📝 Interactive console started
2025-08-01 21:35:16.721 | INFO     | core.logging:log_operation:178 | 🚀 Server startup complete
2025-08-01 21:36:21.791 | INFO     | models.manager:_detect_device:146 | CUDA available: 1 GPU(s) - NVIDIA GeForce RTX 4060
2025-08-01 21:36:21.792 | INFO     | models.manager:__init__:100 | ModelManager initialized with device: cuda
2025-08-01 21:36:23.130 | WARNING  | core.exceptions:http_exception_handler:93 | HTTP exception: 404 - Not Found
2025-08-01 21:36:58.864 | INFO     | core.logging:log_operation:178 | 🛑 Shutting down Reverie Code Studio Server
2025-08-01 21:36:58.864 | INFO     | core.logging:log_operation:178 | 📝 Stopping interactive console
2025-08-01 21:36:58.865 | INFO     | core.logging:log_operation:178 | 📝 Interactive console stopped
2025-08-01 21:36:58.865 | INFO     | core.logging:log_operation:178 | 🔌 Deactivating and unloading plugins
2025-08-01 21:36:58.866 | INFO     | core.logging:log_operation:178 | 🔌 Plugin system shutdown complete
2025-08-01 21:36:58.866 | INFO     | core.logging:log_operation:178 | 🤖 Cleaning up model manager
2025-08-01 21:36:58.866 | INFO     | models.manager:cleanup:1042 | ModelManager cleanup complete
2025-08-01 21:36:58.866 | INFO     | core.logging:log_operation:178 | 🛑 Server shutdown complete
2025-08-01 21:36:58.870 | INFO     | __main__:signal_handler:324 | Received signal 2, shutting down...
