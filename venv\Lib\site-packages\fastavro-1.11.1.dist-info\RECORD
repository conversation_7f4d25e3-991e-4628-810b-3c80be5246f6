../../Scripts/fastavro.exe,sha256=znveGK_4WY-1E4m4mQTRymLJcxBX3MG4VI8BBgq0VUw,108439
fastavro-1.11.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
fastavro-1.11.1.dist-info/LICENSE,sha256=Ih8sI-KBTXtaI4mQEUJsraHsgMlRk_gKq1_ABZF_Qv8,1089
fastavro-1.11.1.dist-info/METADATA,sha256=iKKtaXNAv8yZgBcsvfujHM_WeVn4oVaV0YA2P7TUGvU,5652
fastavro-1.11.1.dist-info/NOTICE.txt,sha256=AhX0TFVrxHNgw_QbyfyBlldaaeSKrqpEzkwq88Iwh9U,577
fastavro-1.11.1.dist-info/RECORD,,
fastavro-1.11.1.dist-info/WHEEL,sha256=NVXpD7b4Gxps0cd2ds5rr5TG8W4ApEwx_i5J99qMZ5E,102
fastavro-1.11.1.dist-info/entry_points.txt,sha256=XQaWQLv7IHj9T8kSKP4SXWU0sCRTB6PUkzpdmfvE8-c,52
fastavro-1.11.1.dist-info/top_level.txt,sha256=kcA1c2BP6SFQN2GFk9kbKob65w70HdXnETe8FlMzxd8,9
fastavro/__init__.py,sha256=-7bkvOCIUJmYSNrgGzk1zeZQnMpMtdiy445C9URN7Y4,1814
fastavro/__main__.py,sha256=rWoi4Lgj8OoBa06vUSMHQM7KXqi0r3sa791NHE4XXjs,2510
fastavro/__pycache__/__init__.cpython-310.pyc,,
fastavro/__pycache__/__main__.cpython-310.pyc,,
fastavro/__pycache__/_logical_readers_py.cpython-310.pyc,,
fastavro/__pycache__/_logical_writers_py.cpython-310.pyc,,
fastavro/__pycache__/_read_common.cpython-310.pyc,,
fastavro/__pycache__/_read_py.cpython-310.pyc,,
fastavro/__pycache__/_schema_common.cpython-310.pyc,,
fastavro/__pycache__/_schema_py.cpython-310.pyc,,
fastavro/__pycache__/_validate_common.cpython-310.pyc,,
fastavro/__pycache__/_validation_py.cpython-310.pyc,,
fastavro/__pycache__/_write_common.cpython-310.pyc,,
fastavro/__pycache__/_write_py.cpython-310.pyc,,
fastavro/__pycache__/const.cpython-310.pyc,,
fastavro/__pycache__/json_read.cpython-310.pyc,,
fastavro/__pycache__/json_write.cpython-310.pyc,,
fastavro/__pycache__/logical_readers.cpython-310.pyc,,
fastavro/__pycache__/logical_writers.cpython-310.pyc,,
fastavro/__pycache__/read.cpython-310.pyc,,
fastavro/__pycache__/schema.cpython-310.pyc,,
fastavro/__pycache__/types.cpython-310.pyc,,
fastavro/__pycache__/utils.cpython-310.pyc,,
fastavro/__pycache__/validation.cpython-310.pyc,,
fastavro/__pycache__/write.cpython-310.pyc,,
fastavro/_logical_readers.cp310-win_amd64.pyd,sha256=C52y4VozUnxIex2JYZtpGnnNofCLnqYgchT_GhPPdjo,61440
fastavro/_logical_readers_py.py,sha256=dR6Y1l4EQ-gWT29BCLTzmEVvi76mkAKYqh5OXlEF0JQ,2876
fastavro/_logical_writers.cp310-win_amd64.pyd,sha256=tUdV1pTK1T-6tW0yN2SBD7N2aQWZO6rsjw6dsUrvtc8,84992
fastavro/_logical_writers_py.py,sha256=PM1Ba4XtMNDTjZyDdI9roA6FEF0NbcST1o8z4qMkw6E,8061
fastavro/_read.cp310-win_amd64.pyd,sha256=lnT9DeW-7E_euxnhheBLwtWNLP7TPrsXbt-sXxw06YY,231424
fastavro/_read_common.py,sha256=8RSVMPqlbWOa4j7m0ROEXCZ1WzYGR7UygKH989scg2o,787
fastavro/_read_py.py,sha256=Wt_Smr3g5S3WOWyvvQVPb6KGKJIBE_jkbrTKkGnKdaI,40460
fastavro/_schema.cp310-win_amd64.pyd,sha256=tOnqgktpLRrthQHJ3KuwlB0WxNG-rLCr6F-qjlXvV8g,142848
fastavro/_schema_common.py,sha256=p9OdNe7xQYFKCeEb5z8331UW-NMnBlynikgmPmy8eRw,1490
fastavro/_schema_py.py,sha256=5w_DMIEzrVyuLAG5Sy6aImD4SOuidkDcJgeMcG1t4OY,34348
fastavro/_validate_common.py,sha256=Fznmgtr7CFqIAfJ_uqYbfLSFpLmplIJPl_P5_gF3rMI,655
fastavro/_validation.cp310-win_amd64.pyd,sha256=uD2PNyRguIxbg-4NpOBzwqPWoDVkOQq9LTPoEHt4yDY,77312
fastavro/_validation_py.py,sha256=bmf9IwnXy0PwD69KucEmz2igBZbJeyYiB_w-0UCEVJc,11130
fastavro/_write.cp310-win_amd64.pyd,sha256=JrJ1_b77Uirn9WkgSaE9rhH0GDXrS234Xr1arp6q2Sg,284672
fastavro/_write_common.py,sha256=2xiTj1uO0-4c1G05knZJ3csqhicrpScwNzZAQfYGH40,635
fastavro/_write_py.py,sha256=SSA_qolOKyZUBcWPANgaWdhsNBliedrrni45hqMgaNs,28293
fastavro/const.py,sha256=06vPAASZLd2GRKTsRonqn88fvIxFHs2K5Uw1g7ZcPNI,893
fastavro/io/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fastavro/io/__pycache__/__init__.cpython-310.pyc,,
fastavro/io/__pycache__/binary_decoder.cpython-310.pyc,,
fastavro/io/__pycache__/binary_encoder.cpython-310.pyc,,
fastavro/io/__pycache__/json_decoder.cpython-310.pyc,,
fastavro/io/__pycache__/json_encoder.cpython-310.pyc,,
fastavro/io/__pycache__/parser.cpython-310.pyc,,
fastavro/io/__pycache__/symbols.cpython-310.pyc,,
fastavro/io/binary_decoder.py,sha256=jECZe61aqKxcwRqydiJNMqiN_wQq7U1INwX6jUmJvu0,4516
fastavro/io/binary_encoder.py,sha256=Mh2YytlIT7BGuc8L6s72J5iBeFiaE3BzUjyoAHwEGEY,1946
fastavro/io/json_decoder.py,sha256=w0400yedzCT4y3EAhS-pZwPMPeAs5jJrVY2yZcwwCHs,7165
fastavro/io/json_encoder.py,sha256=4v8HBqXRh_uJlAltWSb7knbUPjpddGcqnin-mRzWjNc,5878
fastavro/io/parser.py,sha256=hwErmyBm4om4RudqvzcKeKg1PtrSuxHFILBlOvABbUc,6071
fastavro/io/symbols.py,sha256=SlWZ7Gne41YyVTec1M_9pXbICx5viSsEqw3z48FuolM,2433
fastavro/json_read.py,sha256=JZ0FnD_XUpWZQjieW4IrFD9wkc2OZpSH9ZpFh2c_hdk,1661
fastavro/json_write.py,sha256=PJUMUpouF1lWlfrxkNjGEhSaAwPe0-uWfZeG8f8EYxY,2995
fastavro/logical_readers.py,sha256=5Xn1GqFRyBRqFV5AwgIn2cXzjzdFVlUp-7ePgJ2B_C8,225
fastavro/logical_writers.py,sha256=q4h-upA8PZ9sL3orZtRtDS1-aqAzZ3itLbM3IzUjT_8,225
fastavro/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
fastavro/read.py,sha256=0IH8Ccdpjy7vEhoPZ7wl6tNnyFFKgEVUpBLPWoYrFl0,807
fastavro/repository/__init__.py,sha256=DnqK5G9MDvf_JnBJ0DdgMEc6qxCiLRL3c6jFlkXm8Gw,218
fastavro/repository/__pycache__/__init__.cpython-310.pyc,,
fastavro/repository/__pycache__/base.cpython-310.pyc,,
fastavro/repository/__pycache__/flat_dict.cpython-310.pyc,,
fastavro/repository/base.py,sha256=vMRoyUXXdl-NPucpDnuXX4SOqmX5jpBEeaEspvqZr6g,196
fastavro/repository/flat_dict.py,sha256=_KgAdiktetAGoLVURti6rdpv40JoqQRXyGnXQJOV5Ek,788
fastavro/schema.py,sha256=TThm9_0VV1wruuBJQ3uxlblknbJyqcicCLbu0UdiT-Q,1144
fastavro/types.py,sha256=WCW0wFu8KhjgHZHvMOMoXdQth6KjgwGUl9DkGisOTiA,481
fastavro/utils.py,sha256=mXANMsCFPAnRwC2zEE6LGJyaRCNYLkKNRlPWPU3-_FM,8990
fastavro/validation.py,sha256=kQKWG2T2WFr5p9fT0FrCsu0ofuKYQHVdldhfRi_fqnM,436
fastavro/write.py,sha256=EdRJW28ujFhQ3QGPSUPSwNTYg8SsQ_zlwgRVN2D8H9c,479
