# This file was auto-generated by Fern from our API Definition.

from ....core.unchecked_base_model import UncheckedBaseModel
import typing
from .finetuned_model import FinetunedModel
import pydantic
from ....core.pydantic_utilities import IS_PYDANTIC_V2


class ListFinetunedModelsResponse(UncheckedBaseModel):
    """
    Response to a request to list fine-tuned models.
    """

    finetuned_models: typing.Optional[typing.List[FinetunedModel]] = pydantic.Field(default=None)
    """
    List of fine-tuned models matching the request.
    """

    next_page_token: typing.Optional[str] = pydantic.Field(default=None)
    """
    Pagination token to retrieve the next page of results. If the value is "",
    it means no further results for the request.
    """

    total_size: typing.Optional[int] = pydantic.Field(default=None)
    """
    Total count of results.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
