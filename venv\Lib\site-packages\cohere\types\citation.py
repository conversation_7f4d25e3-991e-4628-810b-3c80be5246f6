# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from .source import Source
from .citation_type import CitationType
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class Citation(UncheckedBaseModel):
    """
    Citation information containing sources and the text cited.
    """

    start: typing.Optional[int] = pydantic.Field(default=None)
    """
    Start index of the cited snippet in the original source text.
    """

    end: typing.Optional[int] = pydantic.Field(default=None)
    """
    End index of the cited snippet in the original source text.
    """

    text: typing.Optional[str] = pydantic.Field(default=None)
    """
    Text snippet that is being cited.
    """

    sources: typing.Optional[typing.List[Source]] = None
    type: typing.Optional[CitationType] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
