"""
Plugin Loader for Reverie Code Studio
Handles dynamic loading and discovery of plugins
"""

import sys
import importlib
import importlib.util
import inspect
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Type, Any
import json
import toml

from .base import BasePlugin, PluginMetadata, PluginConfig, PluginCategory, PluginStatus
from .exceptions import (
    PluginLoadError, PluginConfigError, PluginDependencyError, 
    PluginVersionError, PluginNotFoundError
)
from core.logging import logger


class PluginLoader:
    """
    Handles discovery and loading of plugins from various sources
    
    Supports loading plugins from:
    - Python packages
    - Single Python files
    - Plugin directories with metadata
    """
    
    def __init__(self, plugin_dirs: List[Path]):
        self.plugin_dirs = plugin_dirs
        self._loaded_modules: Dict[str, Any] = {}
        
        # Ensure plugin directories exist
        for plugin_dir in self.plugin_dirs:
            plugin_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Plugin loader initialized with {len(plugin_dirs)} directories")
    
    async def discover_plugins(self) -> List[PluginMetadata]:
        """
        Discover all available plugins in the configured directories
        
        Returns:
            List of discovered plugin metadata
        """
        discovered = []
        
        for plugin_dir in self.plugin_dirs:
            logger.info(f"Scanning for plugins in: {plugin_dir}")
            
            # Scan for plugin packages (directories with __init__.py)
            for item in plugin_dir.iterdir():
                if item.is_dir() and (item / "__init__.py").exists():
                    try:
                        metadata = await self._load_plugin_metadata(item)
                        if metadata:
                            discovered.append(metadata)
                    except Exception as e:
                        logger.warning(f"Failed to load metadata for {item.name}: {e}")
                
                # Scan for single-file plugins
                elif item.is_file() and item.suffix == ".py" and not item.name.startswith("_"):
                    try:
                        metadata = await self._load_file_plugin_metadata(item)
                        if metadata:
                            discovered.append(metadata)
                    except Exception as e:
                        logger.warning(f"Failed to load metadata for {item.name}: {e}")
        
        logger.info(f"Discovered {len(discovered)} plugins")
        return discovered
    
    async def load_plugin(self, plugin_name: str, plugin_path: Path) -> BasePlugin:
        """
        Load a specific plugin by name and path
        
        Args:
            plugin_name: Name of the plugin to load
            plugin_path: Path to the plugin
            
        Returns:
            Loaded plugin instance
        """
        try:
            logger.info(f"Loading plugin: {plugin_name} from {plugin_path}")
            
            # Load plugin metadata
            metadata = await self._load_plugin_metadata(plugin_path)
            if not metadata:
                raise PluginLoadError(f"No metadata found for plugin: {plugin_name}")
            
            # Load plugin module
            plugin_module = await self._load_plugin_module(plugin_name, plugin_path)
            
            # Find plugin class
            plugin_class = self._find_plugin_class(plugin_module)
            if not plugin_class:
                raise PluginLoadError(f"No plugin class found in: {plugin_name}")
            
            # Create default config
            config = PluginConfig()
            
            # Instantiate plugin
            plugin_instance = plugin_class(metadata, config)
            
            # Validate plugin
            if not isinstance(plugin_instance, BasePlugin):
                raise PluginLoadError(f"Plugin class must inherit from BasePlugin: {plugin_name}")
            
            logger.info(f"Plugin loaded successfully: {plugin_name}")
            return plugin_instance
            
        except Exception as e:
            logger.error(f"Failed to load plugin {plugin_name}: {e}")
            raise PluginLoadError(f"Failed to load plugin {plugin_name}: {e}", plugin_name)
    
    async def unload_plugin(self, plugin_name: str):
        """
        Unload a plugin and clean up its module
        
        Args:
            plugin_name: Name of the plugin to unload
        """
        try:
            # Remove from loaded modules
            if plugin_name in self._loaded_modules:
                del self._loaded_modules[plugin_name]
            
            # Remove from sys.modules (be careful with this)
            modules_to_remove = [
                name for name in sys.modules.keys()
                if name.startswith(f"plugins.{plugin_name}")
            ]
            
            for module_name in modules_to_remove:
                del sys.modules[module_name]
            
            logger.info(f"Plugin unloaded: {plugin_name}")
            
        except Exception as e:
            logger.error(f"Failed to unload plugin {plugin_name}: {e}")
    
    async def _load_plugin_metadata(self, plugin_path: Path) -> Optional[PluginMetadata]:
        """Load plugin metadata from various sources"""
        
        # Try to load from plugin.json
        metadata_file = plugin_path / "plugin.json"
        if metadata_file.exists():
            return await self._load_json_metadata(metadata_file, plugin_path)
        
        # Try to load from pyproject.toml
        pyproject_file = plugin_path / "pyproject.toml"
        if pyproject_file.exists():
            return await self._load_toml_metadata(pyproject_file, plugin_path)
        
        # Try to load from __init__.py
        init_file = plugin_path / "__init__.py"
        if init_file.exists():
            return await self._load_python_metadata(init_file, plugin_path)
        
        return None
    
    async def _load_file_plugin_metadata(self, plugin_file: Path) -> Optional[PluginMetadata]:
        """Load metadata from a single Python file plugin"""
        return await self._load_python_metadata(plugin_file, plugin_file.parent)
    
    async def _load_json_metadata(self, metadata_file: Path, plugin_path: Path) -> PluginMetadata:
        """Load metadata from plugin.json"""
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return PluginMetadata(
                name=data['name'],
                version=data['version'],
                description=data['description'],
                author=data['author'],
                category=PluginCategory(data.get('category', 'custom')),
                homepage=data.get('homepage'),
                repository=data.get('repository'),
                license=data.get('license'),
                keywords=data.get('keywords', []),
                dependencies=data.get('dependencies', []),
                python_requires=data.get('python_requires'),
                api_version=data.get('api_version', '1.0.0'),
                config_schema=data.get('config_schema'),
                default_config=data.get('default_config', {}),
                plugin_path=plugin_path
            )
            
        except Exception as e:
            raise PluginConfigError(f"Invalid plugin.json: {e}")
    
    async def _load_toml_metadata(self, pyproject_file: Path, plugin_path: Path) -> PluginMetadata:
        """Load metadata from pyproject.toml"""
        try:
            with open(pyproject_file, 'r', encoding='utf-8') as f:
                data = toml.load(f)
            
            plugin_data = data.get('tool', {}).get('reverie-plugin', {})
            if not plugin_data:
                raise PluginConfigError("No [tool.reverie-plugin] section found")
            
            project_data = data.get('project', {})
            
            return PluginMetadata(
                name=plugin_data['name'],
                version=plugin_data.get('version', project_data.get('version', '0.1.0')),
                description=plugin_data.get('description', project_data.get('description', '')),
                author=plugin_data.get('author', ', '.join(project_data.get('authors', []))),
                category=PluginCategory(plugin_data.get('category', 'custom')),
                homepage=plugin_data.get('homepage', project_data.get('urls', {}).get('homepage')),
                repository=plugin_data.get('repository', project_data.get('urls', {}).get('repository')),
                license=plugin_data.get('license', project_data.get('license', {}).get('text')),
                keywords=plugin_data.get('keywords', project_data.get('keywords', [])),
                dependencies=plugin_data.get('dependencies', project_data.get('dependencies', [])),
                python_requires=plugin_data.get('python_requires', project_data.get('requires-python')),
                api_version=plugin_data.get('api_version', '1.0.0'),
                config_schema=plugin_data.get('config_schema'),
                default_config=plugin_data.get('default_config', {}),
                plugin_path=plugin_path
            )
            
        except Exception as e:
            raise PluginConfigError(f"Invalid pyproject.toml: {e}")
    
    async def _load_python_metadata(self, python_file: Path, plugin_path: Path) -> Optional[PluginMetadata]:
        """Load metadata from Python file docstring or variables"""
        try:
            # Read the file and look for metadata
            with open(python_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Try to extract metadata from module-level variables
            metadata_vars = {}
            lines = content.split('\n')
            
            for line in lines:
                line = line.strip()
                if line.startswith('__') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip().strip('_')
                    value = value.strip().strip('"\'')
                    metadata_vars[key] = value
            
            # Required fields
            if 'name' not in metadata_vars:
                return None
            
            return PluginMetadata(
                name=metadata_vars.get('name', python_file.stem),
                version=metadata_vars.get('version', '0.1.0'),
                description=metadata_vars.get('description', ''),
                author=metadata_vars.get('author', 'Unknown'),
                category=PluginCategory(metadata_vars.get('category', 'custom')),
                homepage=metadata_vars.get('homepage'),
                repository=metadata_vars.get('repository'),
                license=metadata_vars.get('license'),
                keywords=metadata_vars.get('keywords', '').split(',') if metadata_vars.get('keywords') else [],
                dependencies=metadata_vars.get('dependencies', '').split(',') if metadata_vars.get('dependencies') else [],
                python_requires=metadata_vars.get('python_requires'),
                api_version=metadata_vars.get('api_version', '1.0.0'),
                plugin_path=plugin_path
            )
            
        except Exception as e:
            logger.warning(f"Failed to load Python metadata from {python_file}: {e}")
            return None
    
    async def _load_plugin_module(self, plugin_name: str, plugin_path: Path) -> Any:
        """Load plugin module dynamically"""
        try:
            if plugin_path.is_file():
                # Single file plugin
                spec = importlib.util.spec_from_file_location(plugin_name, plugin_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
            else:
                # Package plugin
                spec = importlib.util.spec_from_file_location(
                    plugin_name, 
                    plugin_path / "__init__.py"
                )
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
            
            self._loaded_modules[plugin_name] = module
            return module
            
        except Exception as e:
            raise PluginLoadError(f"Failed to load module for {plugin_name}: {e}")
    
    def _find_plugin_class(self, module: Any) -> Optional[Type[BasePlugin]]:
        """Find the plugin class in a module"""
        for name, obj in inspect.getmembers(module, inspect.isclass):
            if (issubclass(obj, BasePlugin) and 
                obj is not BasePlugin and 
                obj.__module__ == module.__name__):
                return obj
        return None
