"""
Shared types and enums for the Agent system
This module contains common types used across the agent system to avoid circular imports
"""

from enum import Enum
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field
import uuid


class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class StepType(Enum):
    """Step execution type"""
    ANALYSIS = "analysis"
    PLANNING = "planning"
    TOOL_CALL = "tool_call"
    CODE_EXECUTION = "code_execution"
    FILE_OPERATION = "file_operation"
    WEB_SEARCH = "web_search"
    VALIDATION = "validation"
    COMMUNICATION = "communication"


class ToolCallStatus(Enum):
    """Tool call execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class ToolCall:
    """Tool call information"""
    tool_name: str
    parameters: Dict[str, Any]
    call_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    status: ToolCallStatus = ToolCallStatus.PENDING
    result: Optional[Any] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "call_id": self.call_id,
            "tool_name": self.tool_name,
            "parameters": self.parameters,
            "status": self.status.value,
            "result": self.result,
            "error": self.error,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None
        }


@dataclass
class AgentStep:
    """Agent execution step"""
    step_id: str
    step_type: StepType
    description: str
    task_id: str
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[Any] = None
    error: Optional[str] = None
    tool_calls: List[ToolCall] = field(default_factory=list)
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.step_id:
            self.step_id = str(uuid.uuid4())
    
    def add_tool_call(self, tool_call: ToolCall):
        """Add a tool call to this step"""
        self.tool_calls.append(tool_call)
    
    def mark_started(self):
        """Mark step as started"""
        self.status = TaskStatus.RUNNING
        self.start_time = datetime.now()
    
    def mark_completed(self, result: Any = None):
        """Mark step as completed"""
        self.status = TaskStatus.COMPLETED
        self.end_time = datetime.now()
        if result is not None:
            self.result = result
    
    def mark_failed(self, error: str):
        """Mark step as failed"""
        self.status = TaskStatus.FAILED
        self.end_time = datetime.now()
        self.error = error
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "step_id": self.step_id,
            "step_type": self.step_type.value,
            "description": self.description,
            "task_id": self.task_id,
            "status": self.status.value,
            "result": self.result,
            "error": self.error,
            "tool_calls": [tc.to_dict() for tc in self.tool_calls],
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "metadata": self.metadata
        }


@dataclass
class AgentTask:
    """Agent task information"""
    task_id: str
    description: str
    status: TaskStatus = TaskStatus.PENDING
    steps: List[AgentStep] = field(default_factory=list)
    result: Optional[Any] = None
    error: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    progress: float = 0.0
    
    def __post_init__(self):
        if not self.task_id:
            self.task_id = str(uuid.uuid4())
    
    def add_step(self, step: AgentStep):
        """Add a step to this task"""
        step.task_id = self.task_id
        self.steps.append(step)
    
    def mark_started(self):
        """Mark task as started"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now()
    
    def mark_completed(self, result: Any = None):
        """Mark task as completed"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()
        self.progress = 1.0
        if result is not None:
            self.result = result
    
    def mark_failed(self, error: str):
        """Mark task as failed"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.error = error
    
    def update_progress(self):
        """Update task progress based on completed steps"""
        if not self.steps:
            self.progress = 0.0
            return
        
        completed_steps = sum(1 for step in self.steps if step.status == TaskStatus.COMPLETED)
        self.progress = completed_steps / len(self.steps)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "task_id": self.task_id,
            "description": self.description,
            "status": self.status.value,
            "steps": [step.to_dict() for step in self.steps],
            "result": self.result,
            "error": self.error,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "progress": self.progress,
            "metadata": self.metadata
        }


class TaskComplexity(Enum):
    """Task complexity levels"""
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    VERY_COMPLEX = "very_complex"


class PlanningStrategy(Enum):
    """Planning strategy types"""
    LINEAR = "linear"
    PARALLEL = "parallel"
    ADAPTIVE = "adaptive"
    HIERARCHICAL = "hierarchical"
