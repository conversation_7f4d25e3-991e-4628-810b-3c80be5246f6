"""
Chat API endpoints for conversational AI interactions
"""

import asyncio
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from server.models.manager import ModelManager
from server.core.config import settings
from server.core.logging import logger
from server.core.exceptions import ModelException

router = APIRouter()


class ChatMessage(BaseModel):
    """Chat message model"""
    role: str  # "user", "assistant", "system"
    content: str
    timestamp: Optional[str] = None


class ChatRequest(BaseModel):
    """Chat request model"""
    messages: List[ChatMessage]
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    stream: bool = True
    context: Optional[Dict[str, Any]] = None  # Additional context like file content, cursor position


class ChatResponse(BaseModel):
    """Chat response model"""
    message: ChatMessage
    usage: Optional[Dict[str, int]] = None


def get_model_manager():
    """Dependency to get model manager from app state"""
    # This is a placeholder - in the actual implementation,
    # we'll get this from the FastAPI app state
    global _model_manager
    if '_model_manager' not in globals():
        from server.models.manager import ModelManager
        _model_manager = ModelManager()
    return _model_manager


def load_system_prompt() -> str:
    """Load the chat system prompt"""
    try:
        prompt_file = settings.project_root / settings.ai.chat_system_prompt_file
        if prompt_file.exists():
            return prompt_file.read_text(encoding='utf-8')
        else:
            logger.warning(f"System prompt file not found: {prompt_file}")
            return "You are a helpful AI coding assistant."
    except Exception as e:
        logger.error(f"Failed to load system prompt: {e}")
        return "You are a helpful AI coding assistant."


def format_chat_prompt(messages: List[ChatMessage], context: Optional[Dict[str, Any]] = None) -> str:
    """Format chat messages into a prompt"""
    
    # Load system prompt
    system_prompt = load_system_prompt()
    
    # Add context information if provided
    if context:
        context_info = []
        
        if "current_file" in context:
            context_info.append(f"Current file: {context['current_file']}")
        
        if "selected_text" in context:
            context_info.append(f"Selected text:\n```\n{context['selected_text']}\n```")
        
        if "cursor_position" in context:
            context_info.append(f"Cursor position: Line {context['cursor_position'].get('line', 0)}, Column {context['cursor_position'].get('column', 0)}")
        
        if "project_context" in context:
            context_info.append(f"Project context: {context['project_context']}")
        
        if context_info:
            system_prompt += "\n\n## Current Context:\n" + "\n".join(context_info)
    
    # Format conversation
    formatted_messages = [f"System: {system_prompt}"]
    
    for msg in messages:
        role = msg.role.capitalize()
        formatted_messages.append(f"{role}: {msg.content}")
    
    # Add assistant prompt
    formatted_messages.append("Assistant:")
    
    return "\n\n".join(formatted_messages)


@router.post("/", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Handle chat requests (non-streaming)"""
    try:
        model_manager = get_model_manager()
        
        if not model_manager.is_model_loaded():
            raise HTTPException(status_code=503, detail="No model is currently loaded")
        
        # Format the prompt
        prompt = format_chat_prompt(request.messages, request.context)
        
        # Generate response
        response_text = ""
        async for chunk in model_manager.generate_text(
            prompt=prompt,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            stream=False
        ):
            response_text += chunk
        
        # Create response message
        response_message = ChatMessage(
            role="assistant",
            content=response_text.strip()
        )
        
        return ChatResponse(message=response_message)
        
    except ModelException as e:
        logger.error(f"Model error in chat: {e}")
        raise HTTPException(status_code=503, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in chat: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/stream")
async def chat_stream(request: ChatRequest):
    """Handle streaming chat requests"""
    try:
        model_manager = get_model_manager()
        
        if not model_manager.is_model_loaded():
            raise HTTPException(status_code=503, detail="No model is currently loaded")
        
        # Format the prompt
        prompt = format_chat_prompt(request.messages, request.context)
        
        async def generate():
            try:
                async for chunk in model_manager.generate_text(
                    prompt=prompt,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    top_p=request.top_p,
                    stream=True
                ):
                    # Format as Server-Sent Events
                    yield f"data: {chunk}\n\n"
                
                # Send end marker
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                logger.error(f"Error in streaming generation: {e}")
                yield f"data: [ERROR] {str(e)}\n\n"
        
        return StreamingResponse(
            generate(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
            }
        )
        
    except ModelException as e:
        logger.error(f"Model error in streaming chat: {e}")
        raise HTTPException(status_code=503, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in streaming chat: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/completion")
async def code_completion(request: ChatRequest):
    """Handle code completion requests"""
    try:
        model_manager = get_model_manager()
        
        if not model_manager.is_model_loaded():
            raise HTTPException(status_code=503, detail="No model is currently loaded")
        
        # For code completion, we use a simpler prompt format
        if request.messages:
            last_message = request.messages[-1]
            prompt = last_message.content
        else:
            raise HTTPException(status_code=400, detail="No messages provided")
        
        # Generate completion
        completion_text = ""
        async for chunk in model_manager.generate_text(
            prompt=prompt,
            max_tokens=request.max_tokens or 100,  # Shorter for completions
            temperature=request.temperature or 0.2,  # Lower temperature for completions
            top_p=request.top_p,
            stream=False
        ):
            completion_text += chunk
        
        # Create response message
        response_message = ChatMessage(
            role="assistant",
            content=completion_text.strip()
        )
        
        return ChatResponse(message=response_message)
        
    except ModelException as e:
        logger.error(f"Model error in code completion: {e}")
        raise HTTPException(status_code=503, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in code completion: {e}")
        raise HTTPException(status_code=500, detail=str(e))
