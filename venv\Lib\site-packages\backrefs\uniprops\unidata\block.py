"""Unicode Properties from Unicode version 13.0.0 (autogen)."""
from __future__ import annotations

unicode_blocks: dict[str, str] = {
    "basiclatin": "\x00-\x7f",
    "^basiclatin": "\x80-\U0010ffff",
    "latin1supplement": "\x80-\xff",
    "^latin1supplement": "\x00-\x7f\u0100-\U0010ffff",
    "latinextendeda": "\u0100-\u017f",
    "^latinextendeda": "\x00-\xff\u0180-\U0010ffff",
    "latinextendedb": "\u0180-\u024f",
    "^latinextendedb": "\x00-\u017f\u0250-\U0010ffff",
    "ipaextensions": "\u0250-\u02af",
    "^ipaextensions": "\x00-\u024f\u02b0-\U0010ffff",
    "spacingmodifierletters": "\u02b0-\u02ff",
    "^spacingmodifierletters": "\x00-\u02af\u0300-\U0010ffff",
    "combiningdiacriticalmarks": "\u0300-\u036f",
    "^combiningdiacriticalmarks": "\x00-\u02ff\u0370-\U0010ffff",
    "greekandcoptic": "\u0370-\u03ff",
    "^greekandcoptic": "\x00-\u036f\u0400-\U0010ffff",
    "cyrillic": "\u0400-\u04ff",
    "^cyrillic": "\x00-\u03ff\u0500-\U0010ffff",
    "cyrillicsupplement": "\u0500-\u052f",
    "^cyrillicsupplement": "\x00-\u04ff\u0530-\U0010ffff",
    "armenian": "\u0530-\u058f",
    "^armenian": "\x00-\u052f\u0590-\U0010ffff",
    "hebrew": "\u0590-\u05ff",
    "^hebrew": "\x00-\u058f\u0600-\U0010ffff",
    "arabic": "\u0600-\u06ff",
    "^arabic": "\x00-\u05ff\u0700-\U0010ffff",
    "syriac": "\u0700-\u074f",
    "^syriac": "\x00-\u06ff\u0750-\U0010ffff",
    "arabicsupplement": "\u0750-\u077f",
    "^arabicsupplement": "\x00-\u074f\u0780-\U0010ffff",
    "thaana": "\u0780-\u07bf",
    "^thaana": "\x00-\u077f\u07c0-\U0010ffff",
    "nko": "\u07c0-\u07ff",
    "^nko": "\x00-\u07bf\u0800-\U0010ffff",
    "samaritan": "\u0800-\u083f",
    "^samaritan": "\x00-\u07ff\u0840-\U0010ffff",
    "mandaic": "\u0840-\u085f",
    "^mandaic": "\x00-\u083f\u0860-\U0010ffff",
    "syriacsupplement": "\u0860-\u086f",
    "^syriacsupplement": "\x00-\u085f\u0870-\U0010ffff",
    "arabicextendeda": "\u08a0-\u08ff",
    "^arabicextendeda": "\x00-\u089f\u0900-\U0010ffff",
    "devanagari": "\u0900-\u097f",
    "^devanagari": "\x00-\u08ff\u0980-\U0010ffff",
    "bengali": "\u0980-\u09ff",
    "^bengali": "\x00-\u097f\u0a00-\U0010ffff",
    "gurmukhi": "\u0a00-\u0a7f",
    "^gurmukhi": "\x00-\u09ff\u0a80-\U0010ffff",
    "gujarati": "\u0a80-\u0aff",
    "^gujarati": "\x00-\u0a7f\u0b00-\U0010ffff",
    "oriya": "\u0b00-\u0b7f",
    "^oriya": "\x00-\u0aff\u0b80-\U0010ffff",
    "tamil": "\u0b80-\u0bff",
    "^tamil": "\x00-\u0b7f\u0c00-\U0010ffff",
    "telugu": "\u0c00-\u0c7f",
    "^telugu": "\x00-\u0bff\u0c80-\U0010ffff",
    "kannada": "\u0c80-\u0cff",
    "^kannada": "\x00-\u0c7f\u0d00-\U0010ffff",
    "malayalam": "\u0d00-\u0d7f",
    "^malayalam": "\x00-\u0cff\u0d80-\U0010ffff",
    "sinhala": "\u0d80-\u0dff",
    "^sinhala": "\x00-\u0d7f\u0e00-\U0010ffff",
    "thai": "\u0e00-\u0e7f",
    "^thai": "\x00-\u0dff\u0e80-\U0010ffff",
    "lao": "\u0e80-\u0eff",
    "^lao": "\x00-\u0e7f\u0f00-\U0010ffff",
    "tibetan": "\u0f00-\u0fff",
    "^tibetan": "\x00-\u0eff\u1000-\U0010ffff",
    "myanmar": "\u1000-\u109f",
    "^myanmar": "\x00-\u0fff\u10a0-\U0010ffff",
    "georgian": "\u10a0-\u10ff",
    "^georgian": "\x00-\u109f\u1100-\U0010ffff",
    "hanguljamo": "\u1100-\u11ff",
    "^hanguljamo": "\x00-\u10ff\u1200-\U0010ffff",
    "ethiopic": "\u1200-\u137f",
    "^ethiopic": "\x00-\u11ff\u1380-\U0010ffff",
    "ethiopicsupplement": "\u1380-\u139f",
    "^ethiopicsupplement": "\x00-\u137f\u13a0-\U0010ffff",
    "cherokee": "\u13a0-\u13ff",
    "^cherokee": "\x00-\u139f\u1400-\U0010ffff",
    "unifiedcanadianaboriginalsyllabics": "\u1400-\u167f",
    "^unifiedcanadianaboriginalsyllabics": "\x00-\u13ff\u1680-\U0010ffff",
    "ogham": "\u1680-\u169f",
    "^ogham": "\x00-\u167f\u16a0-\U0010ffff",
    "runic": "\u16a0-\u16ff",
    "^runic": "\x00-\u169f\u1700-\U0010ffff",
    "tagalog": "\u1700-\u171f",
    "^tagalog": "\x00-\u16ff\u1720-\U0010ffff",
    "hanunoo": "\u1720-\u173f",
    "^hanunoo": "\x00-\u171f\u1740-\U0010ffff",
    "buhid": "\u1740-\u175f",
    "^buhid": "\x00-\u173f\u1760-\U0010ffff",
    "tagbanwa": "\u1760-\u177f",
    "^tagbanwa": "\x00-\u175f\u1780-\U0010ffff",
    "khmer": "\u1780-\u17ff",
    "^khmer": "\x00-\u177f\u1800-\U0010ffff",
    "mongolian": "\u1800-\u18af",
    "^mongolian": "\x00-\u17ff\u18b0-\U0010ffff",
    "unifiedcanadianaboriginalsyllabicsextended": "\u18b0-\u18ff",
    "^unifiedcanadianaboriginalsyllabicsextended": "\x00-\u18af\u1900-\U0010ffff",
    "limbu": "\u1900-\u194f",
    "^limbu": "\x00-\u18ff\u1950-\U0010ffff",
    "taile": "\u1950-\u197f",
    "^taile": "\x00-\u194f\u1980-\U0010ffff",
    "newtailue": "\u1980-\u19df",
    "^newtailue": "\x00-\u197f\u19e0-\U0010ffff",
    "khmersymbols": "\u19e0-\u19ff",
    "^khmersymbols": "\x00-\u19df\u1a00-\U0010ffff",
    "buginese": "\u1a00-\u1a1f",
    "^buginese": "\x00-\u19ff\u1a20-\U0010ffff",
    "taitham": "\u1a20-\u1aaf",
    "^taitham": "\x00-\u1a1f\u1ab0-\U0010ffff",
    "combiningdiacriticalmarksextended": "\u1ab0-\u1aff",
    "^combiningdiacriticalmarksextended": "\x00-\u1aaf\u1b00-\U0010ffff",
    "balinese": "\u1b00-\u1b7f",
    "^balinese": "\x00-\u1aff\u1b80-\U0010ffff",
    "sundanese": "\u1b80-\u1bbf",
    "^sundanese": "\x00-\u1b7f\u1bc0-\U0010ffff",
    "batak": "\u1bc0-\u1bff",
    "^batak": "\x00-\u1bbf\u1c00-\U0010ffff",
    "lepcha": "\u1c00-\u1c4f",
    "^lepcha": "\x00-\u1bff\u1c50-\U0010ffff",
    "olchiki": "\u1c50-\u1c7f",
    "^olchiki": "\x00-\u1c4f\u1c80-\U0010ffff",
    "cyrillicextendedc": "\u1c80-\u1c8f",
    "^cyrillicextendedc": "\x00-\u1c7f\u1c90-\U0010ffff",
    "georgianextended": "\u1c90-\u1cbf",
    "^georgianextended": "\x00-\u1c8f\u1cc0-\U0010ffff",
    "sundanesesupplement": "\u1cc0-\u1ccf",
    "^sundanesesupplement": "\x00-\u1cbf\u1cd0-\U0010ffff",
    "vedicextensions": "\u1cd0-\u1cff",
    "^vedicextensions": "\x00-\u1ccf\u1d00-\U0010ffff",
    "phoneticextensions": "\u1d00-\u1d7f",
    "^phoneticextensions": "\x00-\u1cff\u1d80-\U0010ffff",
    "phoneticextensionssupplement": "\u1d80-\u1dbf",
    "^phoneticextensionssupplement": "\x00-\u1d7f\u1dc0-\U0010ffff",
    "combiningdiacriticalmarkssupplement": "\u1dc0-\u1dff",
    "^combiningdiacriticalmarkssupplement": "\x00-\u1dbf\u1e00-\U0010ffff",
    "latinextendedadditional": "\u1e00-\u1eff",
    "^latinextendedadditional": "\x00-\u1dff\u1f00-\U0010ffff",
    "greekextended": "\u1f00-\u1fff",
    "^greekextended": "\x00-\u1eff\u2000-\U0010ffff",
    "generalpunctuation": "\u2000-\u206f",
    "^generalpunctuation": "\x00-\u1fff\u2070-\U0010ffff",
    "superscriptsandsubscripts": "\u2070-\u209f",
    "^superscriptsandsubscripts": "\x00-\u206f\u20a0-\U0010ffff",
    "currencysymbols": "\u20a0-\u20cf",
    "^currencysymbols": "\x00-\u209f\u20d0-\U0010ffff",
    "combiningdiacriticalmarksforsymbols": "\u20d0-\u20ff",
    "^combiningdiacriticalmarksforsymbols": "\x00-\u20cf\u2100-\U0010ffff",
    "letterlikesymbols": "\u2100-\u214f",
    "^letterlikesymbols": "\x00-\u20ff\u2150-\U0010ffff",
    "numberforms": "\u2150-\u218f",
    "^numberforms": "\x00-\u214f\u2190-\U0010ffff",
    "arrows": "\u2190-\u21ff",
    "^arrows": "\x00-\u218f\u2200-\U0010ffff",
    "mathematicaloperators": "\u2200-\u22ff",
    "^mathematicaloperators": "\x00-\u21ff\u2300-\U0010ffff",
    "miscellaneoustechnical": "\u2300-\u23ff",
    "^miscellaneoustechnical": "\x00-\u22ff\u2400-\U0010ffff",
    "controlpictures": "\u2400-\u243f",
    "^controlpictures": "\x00-\u23ff\u2440-\U0010ffff",
    "opticalcharacterrecognition": "\u2440-\u245f",
    "^opticalcharacterrecognition": "\x00-\u243f\u2460-\U0010ffff",
    "enclosedalphanumerics": "\u2460-\u24ff",
    "^enclosedalphanumerics": "\x00-\u245f\u2500-\U0010ffff",
    "boxdrawing": "\u2500-\u257f",
    "^boxdrawing": "\x00-\u24ff\u2580-\U0010ffff",
    "blockelements": "\u2580-\u259f",
    "^blockelements": "\x00-\u257f\u25a0-\U0010ffff",
    "geometricshapes": "\u25a0-\u25ff",
    "^geometricshapes": "\x00-\u259f\u2600-\U0010ffff",
    "miscellaneoussymbols": "\u2600-\u26ff",
    "^miscellaneoussymbols": "\x00-\u25ff\u2700-\U0010ffff",
    "dingbats": "\u2700-\u27bf",
    "^dingbats": "\x00-\u26ff\u27c0-\U0010ffff",
    "miscellaneousmathematicalsymbolsa": "\u27c0-\u27ef",
    "^miscellaneousmathematicalsymbolsa": "\x00-\u27bf\u27f0-\U0010ffff",
    "supplementalarrowsa": "\u27f0-\u27ff",
    "^supplementalarrowsa": "\x00-\u27ef\u2800-\U0010ffff",
    "braillepatterns": "\u2800-\u28ff",
    "^braillepatterns": "\x00-\u27ff\u2900-\U0010ffff",
    "supplementalarrowsb": "\u2900-\u297f",
    "^supplementalarrowsb": "\x00-\u28ff\u2980-\U0010ffff",
    "miscellaneousmathematicalsymbolsb": "\u2980-\u29ff",
    "^miscellaneousmathematicalsymbolsb": "\x00-\u297f\u2a00-\U0010ffff",
    "supplementalmathematicaloperators": "\u2a00-\u2aff",
    "^supplementalmathematicaloperators": "\x00-\u29ff\u2b00-\U0010ffff",
    "miscellaneoussymbolsandarrows": "\u2b00-\u2bff",
    "^miscellaneoussymbolsandarrows": "\x00-\u2aff\u2c00-\U0010ffff",
    "glagolitic": "\u2c00-\u2c5f",
    "^glagolitic": "\x00-\u2bff\u2c60-\U0010ffff",
    "latinextendedc": "\u2c60-\u2c7f",
    "^latinextendedc": "\x00-\u2c5f\u2c80-\U0010ffff",
    "coptic": "\u2c80-\u2cff",
    "^coptic": "\x00-\u2c7f\u2d00-\U0010ffff",
    "georgiansupplement": "\u2d00-\u2d2f",
    "^georgiansupplement": "\x00-\u2cff\u2d30-\U0010ffff",
    "tifinagh": "\u2d30-\u2d7f",
    "^tifinagh": "\x00-\u2d2f\u2d80-\U0010ffff",
    "ethiopicextended": "\u2d80-\u2ddf",
    "^ethiopicextended": "\x00-\u2d7f\u2de0-\U0010ffff",
    "cyrillicextendeda": "\u2de0-\u2dff",
    "^cyrillicextendeda": "\x00-\u2ddf\u2e00-\U0010ffff",
    "supplementalpunctuation": "\u2e00-\u2e7f",
    "^supplementalpunctuation": "\x00-\u2dff\u2e80-\U0010ffff",
    "cjkradicalssupplement": "\u2e80-\u2eff",
    "^cjkradicalssupplement": "\x00-\u2e7f\u2f00-\U0010ffff",
    "kangxiradicals": "\u2f00-\u2fdf",
    "^kangxiradicals": "\x00-\u2eff\u2fe0-\U0010ffff",
    "ideographicdescriptioncharacters": "\u2ff0-\u2fff",
    "^ideographicdescriptioncharacters": "\x00-\u2fef\u3000-\U0010ffff",
    "cjksymbolsandpunctuation": "\u3000-\u303f",
    "^cjksymbolsandpunctuation": "\x00-\u2fff\u3040-\U0010ffff",
    "hiragana": "\u3040-\u309f",
    "^hiragana": "\x00-\u303f\u30a0-\U0010ffff",
    "katakana": "\u30a0-\u30ff",
    "^katakana": "\x00-\u309f\u3100-\U0010ffff",
    "bopomofo": "\u3100-\u312f",
    "^bopomofo": "\x00-\u30ff\u3130-\U0010ffff",
    "hangulcompatibilityjamo": "\u3130-\u318f",
    "^hangulcompatibilityjamo": "\x00-\u312f\u3190-\U0010ffff",
    "kanbun": "\u3190-\u319f",
    "^kanbun": "\x00-\u318f\u31a0-\U0010ffff",
    "bopomofoextended": "\u31a0-\u31bf",
    "^bopomofoextended": "\x00-\u319f\u31c0-\U0010ffff",
    "cjkstrokes": "\u31c0-\u31ef",
    "^cjkstrokes": "\x00-\u31bf\u31f0-\U0010ffff",
    "katakanaphoneticextensions": "\u31f0-\u31ff",
    "^katakanaphoneticextensions": "\x00-\u31ef\u3200-\U0010ffff",
    "enclosedcjklettersandmonths": "\u3200-\u32ff",
    "^enclosedcjklettersandmonths": "\x00-\u31ff\u3300-\U0010ffff",
    "cjkcompatibility": "\u3300-\u33ff",
    "^cjkcompatibility": "\x00-\u32ff\u3400-\U0010ffff",
    "cjkunifiedideographsextensiona": "\u3400-\u4dbf",
    "^cjkunifiedideographsextensiona": "\x00-\u33ff\u4dc0-\U0010ffff",
    "yijinghexagramsymbols": "\u4dc0-\u4dff",
    "^yijinghexagramsymbols": "\x00-\u4dbf\u4e00-\U0010ffff",
    "cjkunifiedideographs": "\u4e00-\u9fff",
    "^cjkunifiedideographs": "\x00-\u4dff\ua000-\U0010ffff",
    "yisyllables": "\ua000-\ua48f",
    "^yisyllables": "\x00-\u9fff\ua490-\U0010ffff",
    "yiradicals": "\ua490-\ua4cf",
    "^yiradicals": "\x00-\ua48f\ua4d0-\U0010ffff",
    "lisu": "\ua4d0-\ua4ff",
    "^lisu": "\x00-\ua4cf\ua500-\U0010ffff",
    "vai": "\ua500-\ua63f",
    "^vai": "\x00-\ua4ff\ua640-\U0010ffff",
    "cyrillicextendedb": "\ua640-\ua69f",
    "^cyrillicextendedb": "\x00-\ua63f\ua6a0-\U0010ffff",
    "bamum": "\ua6a0-\ua6ff",
    "^bamum": "\x00-\ua69f\ua700-\U0010ffff",
    "modifiertoneletters": "\ua700-\ua71f",
    "^modifiertoneletters": "\x00-\ua6ff\ua720-\U0010ffff",
    "latinextendedd": "\ua720-\ua7ff",
    "^latinextendedd": "\x00-\ua71f\ua800-\U0010ffff",
    "sylotinagri": "\ua800-\ua82f",
    "^sylotinagri": "\x00-\ua7ff\ua830-\U0010ffff",
    "commonindicnumberforms": "\ua830-\ua83f",
    "^commonindicnumberforms": "\x00-\ua82f\ua840-\U0010ffff",
    "phagspa": "\ua840-\ua87f",
    "^phagspa": "\x00-\ua83f\ua880-\U0010ffff",
    "saurashtra": "\ua880-\ua8df",
    "^saurashtra": "\x00-\ua87f\ua8e0-\U0010ffff",
    "devanagariextended": "\ua8e0-\ua8ff",
    "^devanagariextended": "\x00-\ua8df\ua900-\U0010ffff",
    "kayahli": "\ua900-\ua92f",
    "^kayahli": "\x00-\ua8ff\ua930-\U0010ffff",
    "rejang": "\ua930-\ua95f",
    "^rejang": "\x00-\ua92f\ua960-\U0010ffff",
    "hanguljamoextendeda": "\ua960-\ua97f",
    "^hanguljamoextendeda": "\x00-\ua95f\ua980-\U0010ffff",
    "javanese": "\ua980-\ua9df",
    "^javanese": "\x00-\ua97f\ua9e0-\U0010ffff",
    "myanmarextendedb": "\ua9e0-\ua9ff",
    "^myanmarextendedb": "\x00-\ua9df\uaa00-\U0010ffff",
    "cham": "\uaa00-\uaa5f",
    "^cham": "\x00-\ua9ff\uaa60-\U0010ffff",
    "myanmarextendeda": "\uaa60-\uaa7f",
    "^myanmarextendeda": "\x00-\uaa5f\uaa80-\U0010ffff",
    "taiviet": "\uaa80-\uaadf",
    "^taiviet": "\x00-\uaa7f\uaae0-\U0010ffff",
    "meeteimayekextensions": "\uaae0-\uaaff",
    "^meeteimayekextensions": "\x00-\uaadf\uab00-\U0010ffff",
    "ethiopicextendeda": "\uab00-\uab2f",
    "^ethiopicextendeda": "\x00-\uaaff\uab30-\U0010ffff",
    "latinextendede": "\uab30-\uab6f",
    "^latinextendede": "\x00-\uab2f\uab70-\U0010ffff",
    "cherokeesupplement": "\uab70-\uabbf",
    "^cherokeesupplement": "\x00-\uab6f\uabc0-\U0010ffff",
    "meeteimayek": "\uabc0-\uabff",
    "^meeteimayek": "\x00-\uabbf\uac00-\U0010ffff",
    "hangulsyllables": "\uac00-\ud7af",
    "^hangulsyllables": "\x00-\uabff\ud7b0-\U0010ffff",
    "hanguljamoextendedb": "\ud7b0-\ud7ff",
    "^hanguljamoextendedb": "\x00-\ud7af\ud800-\U0010ffff",
    "highsurrogates": "\ud800-\udb7f",
    "^highsurrogates": "\x00-\ud7ff\udb80-\U0010ffff",
    "highprivateusesurrogates": "\udb80-\udbff",
    "^highprivateusesurrogates": "\x00-\udb7f\udc00-\U0010ffff",
    "lowsurrogates": "\udc00-\udfff",
    "^lowsurrogates": "\x00-\udbff\ue000-\U0010ffff",
    "privateusearea": "\ue000-\uf8ff",
    "^privateusearea": "\x00-\udfff\uf900-\U0010ffff",
    "cjkcompatibilityideographs": "\uf900-\ufaff",
    "^cjkcompatibilityideographs": "\x00-\uf8ff\ufb00-\U0010ffff",
    "alphabeticpresentationforms": "\ufb00-\ufb4f",
    "^alphabeticpresentationforms": "\x00-\ufaff\ufb50-\U0010ffff",
    "arabicpresentationformsa": "\ufb50-\ufdff",
    "^arabicpresentationformsa": "\x00-\ufb4f\ufe00-\U0010ffff",
    "variationselectors": "\ufe00-\ufe0f",
    "^variationselectors": "\x00-\ufdff\ufe10-\U0010ffff",
    "verticalforms": "\ufe10-\ufe1f",
    "^verticalforms": "\x00-\ufe0f\ufe20-\U0010ffff",
    "combininghalfmarks": "\ufe20-\ufe2f",
    "^combininghalfmarks": "\x00-\ufe1f\ufe30-\U0010ffff",
    "cjkcompatibilityforms": "\ufe30-\ufe4f",
    "^cjkcompatibilityforms": "\x00-\ufe2f\ufe50-\U0010ffff",
    "smallformvariants": "\ufe50-\ufe6f",
    "^smallformvariants": "\x00-\ufe4f\ufe70-\U0010ffff",
    "arabicpresentationformsb": "\ufe70-\ufeff",
    "^arabicpresentationformsb": "\x00-\ufe6f\uff00-\U0010ffff",
    "halfwidthandfullwidthforms": "\uff00-\uffef",
    "^halfwidthandfullwidthforms": "\x00-\ufeff\ufff0-\U0010ffff",
    "specials": "\ufff0-\uffff",
    "^specials": "\x00-\uffef\U00010000-\U0010ffff",
    "linearbsyllabary": "\U00010000-\U0001007f",
    "^linearbsyllabary": "\x00-\uffff\U00010080-\U0010ffff",
    "linearbideograms": "\U00010080-\U000100ff",
    "^linearbideograms": "\x00-\U0001007f\U00010100-\U0010ffff",
    "aegeannumbers": "\U00010100-\U0001013f",
    "^aegeannumbers": "\x00-\U000100ff\U00010140-\U0010ffff",
    "ancientgreeknumbers": "\U00010140-\U0001018f",
    "^ancientgreeknumbers": "\x00-\U0001013f\U00010190-\U0010ffff",
    "ancientsymbols": "\U00010190-\U000101cf",
    "^ancientsymbols": "\x00-\U0001018f\U000101d0-\U0010ffff",
    "phaistosdisc": "\U000101d0-\U000101ff",
    "^phaistosdisc": "\x00-\U000101cf\U00010200-\U0010ffff",
    "lycian": "\U00010280-\U0001029f",
    "^lycian": "\x00-\U0001027f\U000102a0-\U0010ffff",
    "carian": "\U000102a0-\U000102df",
    "^carian": "\x00-\U0001029f\U000102e0-\U0010ffff",
    "copticepactnumbers": "\U000102e0-\U000102ff",
    "^copticepactnumbers": "\x00-\U000102df\U00010300-\U0010ffff",
    "olditalic": "\U00010300-\U0001032f",
    "^olditalic": "\x00-\U000102ff\U00010330-\U0010ffff",
    "gothic": "\U00010330-\U0001034f",
    "^gothic": "\x00-\U0001032f\U00010350-\U0010ffff",
    "oldpermic": "\U00010350-\U0001037f",
    "^oldpermic": "\x00-\U0001034f\U00010380-\U0010ffff",
    "ugaritic": "\U00010380-\U0001039f",
    "^ugaritic": "\x00-\U0001037f\U000103a0-\U0010ffff",
    "oldpersian": "\U000103a0-\U000103df",
    "^oldpersian": "\x00-\U0001039f\U000103e0-\U0010ffff",
    "deseret": "\U00010400-\U0001044f",
    "^deseret": "\x00-\U000103ff\U00010450-\U0010ffff",
    "shavian": "\U00010450-\U0001047f",
    "^shavian": "\x00-\U0001044f\U00010480-\U0010ffff",
    "osmanya": "\U00010480-\U000104af",
    "^osmanya": "\x00-\U0001047f\U000104b0-\U0010ffff",
    "osage": "\U000104b0-\U000104ff",
    "^osage": "\x00-\U000104af\U00010500-\U0010ffff",
    "elbasan": "\U00010500-\U0001052f",
    "^elbasan": "\x00-\U000104ff\U00010530-\U0010ffff",
    "caucasianalbanian": "\U00010530-\U0001056f",
    "^caucasianalbanian": "\x00-\U0001052f\U00010570-\U0010ffff",
    "lineara": "\U00010600-\U0001077f",
    "^lineara": "\x00-\U000105ff\U00010780-\U0010ffff",
    "cypriotsyllabary": "\U00010800-\U0001083f",
    "^cypriotsyllabary": "\x00-\U000107ff\U00010840-\U0010ffff",
    "imperialaramaic": "\U00010840-\U0001085f",
    "^imperialaramaic": "\x00-\U0001083f\U00010860-\U0010ffff",
    "palmyrene": "\U00010860-\U0001087f",
    "^palmyrene": "\x00-\U0001085f\U00010880-\U0010ffff",
    "nabataean": "\U00010880-\U000108af",
    "^nabataean": "\x00-\U0001087f\U000108b0-\U0010ffff",
    "hatran": "\U000108e0-\U000108ff",
    "^hatran": "\x00-\U000108df\U00010900-\U0010ffff",
    "phoenician": "\U00010900-\U0001091f",
    "^phoenician": "\x00-\U000108ff\U00010920-\U0010ffff",
    "lydian": "\U00010920-\U0001093f",
    "^lydian": "\x00-\U0001091f\U00010940-\U0010ffff",
    "meroitichieroglyphs": "\U00010980-\U0001099f",
    "^meroitichieroglyphs": "\x00-\U0001097f\U000109a0-\U0010ffff",
    "meroiticcursive": "\U000109a0-\U000109ff",
    "^meroiticcursive": "\x00-\U0001099f\U00010a00-\U0010ffff",
    "kharoshthi": "\U00010a00-\U00010a5f",
    "^kharoshthi": "\x00-\U000109ff\U00010a60-\U0010ffff",
    "oldsoutharabian": "\U00010a60-\U00010a7f",
    "^oldsoutharabian": "\x00-\U00010a5f\U00010a80-\U0010ffff",
    "oldnortharabian": "\U00010a80-\U00010a9f",
    "^oldnortharabian": "\x00-\U00010a7f\U00010aa0-\U0010ffff",
    "manichaean": "\U00010ac0-\U00010aff",
    "^manichaean": "\x00-\U00010abf\U00010b00-\U0010ffff",
    "avestan": "\U00010b00-\U00010b3f",
    "^avestan": "\x00-\U00010aff\U00010b40-\U0010ffff",
    "inscriptionalparthian": "\U00010b40-\U00010b5f",
    "^inscriptionalparthian": "\x00-\U00010b3f\U00010b60-\U0010ffff",
    "inscriptionalpahlavi": "\U00010b60-\U00010b7f",
    "^inscriptionalpahlavi": "\x00-\U00010b5f\U00010b80-\U0010ffff",
    "psalterpahlavi": "\U00010b80-\U00010baf",
    "^psalterpahlavi": "\x00-\U00010b7f\U00010bb0-\U0010ffff",
    "oldturkic": "\U00010c00-\U00010c4f",
    "^oldturkic": "\x00-\U00010bff\U00010c50-\U0010ffff",
    "oldhungarian": "\U00010c80-\U00010cff",
    "^oldhungarian": "\x00-\U00010c7f\U00010d00-\U0010ffff",
    "hanifirohingya": "\U00010d00-\U00010d3f",
    "^hanifirohingya": "\x00-\U00010cff\U00010d40-\U0010ffff",
    "ruminumeralsymbols": "\U00010e60-\U00010e7f",
    "^ruminumeralsymbols": "\x00-\U00010e5f\U00010e80-\U0010ffff",
    "yezidi": "\U00010e80-\U00010ebf",
    "^yezidi": "\x00-\U00010e7f\U00010ec0-\U0010ffff",
    "oldsogdian": "\U00010f00-\U00010f2f",
    "^oldsogdian": "\x00-\U00010eff\U00010f30-\U0010ffff",
    "sogdian": "\U00010f30-\U00010f6f",
    "^sogdian": "\x00-\U00010f2f\U00010f70-\U0010ffff",
    "chorasmian": "\U00010fb0-\U00010fdf",
    "^chorasmian": "\x00-\U00010faf\U00010fe0-\U0010ffff",
    "elymaic": "\U00010fe0-\U00010fff",
    "^elymaic": "\x00-\U00010fdf\U00011000-\U0010ffff",
    "brahmi": "\U00011000-\U0001107f",
    "^brahmi": "\x00-\U00010fff\U00011080-\U0010ffff",
    "kaithi": "\U00011080-\U000110cf",
    "^kaithi": "\x00-\U0001107f\U000110d0-\U0010ffff",
    "sorasompeng": "\U000110d0-\U000110ff",
    "^sorasompeng": "\x00-\U000110cf\U00011100-\U0010ffff",
    "chakma": "\U00011100-\U0001114f",
    "^chakma": "\x00-\U000110ff\U00011150-\U0010ffff",
    "mahajani": "\U00011150-\U0001117f",
    "^mahajani": "\x00-\U0001114f\U00011180-\U0010ffff",
    "sharada": "\U00011180-\U000111df",
    "^sharada": "\x00-\U0001117f\U000111e0-\U0010ffff",
    "sinhalaarchaicnumbers": "\U000111e0-\U000111ff",
    "^sinhalaarchaicnumbers": "\x00-\U000111df\U00011200-\U0010ffff",
    "khojki": "\U00011200-\U0001124f",
    "^khojki": "\x00-\U000111ff\U00011250-\U0010ffff",
    "multani": "\U00011280-\U000112af",
    "^multani": "\x00-\U0001127f\U000112b0-\U0010ffff",
    "khudawadi": "\U000112b0-\U000112ff",
    "^khudawadi": "\x00-\U000112af\U00011300-\U0010ffff",
    "grantha": "\U00011300-\U0001137f",
    "^grantha": "\x00-\U000112ff\U00011380-\U0010ffff",
    "newa": "\U00011400-\U0001147f",
    "^newa": "\x00-\U000113ff\U00011480-\U0010ffff",
    "tirhuta": "\U00011480-\U000114df",
    "^tirhuta": "\x00-\U0001147f\U000114e0-\U0010ffff",
    "siddham": "\U00011580-\U000115ff",
    "^siddham": "\x00-\U0001157f\U00011600-\U0010ffff",
    "modi": "\U00011600-\U0001165f",
    "^modi": "\x00-\U000115ff\U00011660-\U0010ffff",
    "mongoliansupplement": "\U00011660-\U0001167f",
    "^mongoliansupplement": "\x00-\U0001165f\U00011680-\U0010ffff",
    "takri": "\U00011680-\U000116cf",
    "^takri": "\x00-\U0001167f\U000116d0-\U0010ffff",
    "ahom": "\U00011700-\U0001173f",
    "^ahom": "\x00-\U000116ff\U00011740-\U0010ffff",
    "dogra": "\U00011800-\U0001184f",
    "^dogra": "\x00-\U000117ff\U00011850-\U0010ffff",
    "warangciti": "\U000118a0-\U000118ff",
    "^warangciti": "\x00-\U0001189f\U00011900-\U0010ffff",
    "divesakuru": "\U00011900-\U0001195f",
    "^divesakuru": "\x00-\U000118ff\U00011960-\U0010ffff",
    "nandinagari": "\U000119a0-\U000119ff",
    "^nandinagari": "\x00-\U0001199f\U00011a00-\U0010ffff",
    "zanabazarsquare": "\U00011a00-\U00011a4f",
    "^zanabazarsquare": "\x00-\U000119ff\U00011a50-\U0010ffff",
    "soyombo": "\U00011a50-\U00011aaf",
    "^soyombo": "\x00-\U00011a4f\U00011ab0-\U0010ffff",
    "paucinhau": "\U00011ac0-\U00011aff",
    "^paucinhau": "\x00-\U00011abf\U00011b00-\U0010ffff",
    "bhaiksuki": "\U00011c00-\U00011c6f",
    "^bhaiksuki": "\x00-\U00011bff\U00011c70-\U0010ffff",
    "marchen": "\U00011c70-\U00011cbf",
    "^marchen": "\x00-\U00011c6f\U00011cc0-\U0010ffff",
    "masaramgondi": "\U00011d00-\U00011d5f",
    "^masaramgondi": "\x00-\U00011cff\U00011d60-\U0010ffff",
    "gunjalagondi": "\U00011d60-\U00011daf",
    "^gunjalagondi": "\x00-\U00011d5f\U00011db0-\U0010ffff",
    "makasar": "\U00011ee0-\U00011eff",
    "^makasar": "\x00-\U00011edf\U00011f00-\U0010ffff",
    "lisusupplement": "\U00011fb0-\U00011fbf",
    "^lisusupplement": "\x00-\U00011faf\U00011fc0-\U0010ffff",
    "tamilsupplement": "\U00011fc0-\U00011fff",
    "^tamilsupplement": "\x00-\U00011fbf\U00012000-\U0010ffff",
    "cuneiform": "\U00012000-\U000123ff",
    "^cuneiform": "\x00-\U00011fff\U00012400-\U0010ffff",
    "cuneiformnumbersandpunctuation": "\U00012400-\U0001247f",
    "^cuneiformnumbersandpunctuation": "\x00-\U000123ff\U00012480-\U0010ffff",
    "earlydynasticcuneiform": "\U00012480-\U0001254f",
    "^earlydynasticcuneiform": "\x00-\U0001247f\U00012550-\U0010ffff",
    "egyptianhieroglyphs": "\U00013000-\U0001342f",
    "^egyptianhieroglyphs": "\x00-\U00012fff\U00013430-\U0010ffff",
    "egyptianhieroglyphformatcontrols": "\U00013430-\U0001343f",
    "^egyptianhieroglyphformatcontrols": "\x00-\U0001342f\U00013440-\U0010ffff",
    "anatolianhieroglyphs": "\U00014400-\U0001467f",
    "^anatolianhieroglyphs": "\x00-\U000143ff\U00014680-\U0010ffff",
    "bamumsupplement": "\U00016800-\U00016a3f",
    "^bamumsupplement": "\x00-\U000167ff\U00016a40-\U0010ffff",
    "mro": "\U00016a40-\U00016a6f",
    "^mro": "\x00-\U00016a3f\U00016a70-\U0010ffff",
    "bassavah": "\U00016ad0-\U00016aff",
    "^bassavah": "\x00-\U00016acf\U00016b00-\U0010ffff",
    "pahawhhmong": "\U00016b00-\U00016b8f",
    "^pahawhhmong": "\x00-\U00016aff\U00016b90-\U0010ffff",
    "medefaidrin": "\U00016e40-\U00016e9f",
    "^medefaidrin": "\x00-\U00016e3f\U00016ea0-\U0010ffff",
    "miao": "\U00016f00-\U00016f9f",
    "^miao": "\x00-\U00016eff\U00016fa0-\U0010ffff",
    "ideographicsymbolsandpunctuation": "\U00016fe0-\U00016fff",
    "^ideographicsymbolsandpunctuation": "\x00-\U00016fdf\U00017000-\U0010ffff",
    "tangut": "\U00017000-\U000187ff",
    "^tangut": "\x00-\U00016fff\U00018800-\U0010ffff",
    "tangutcomponents": "\U00018800-\U00018aff",
    "^tangutcomponents": "\x00-\U000187ff\U00018b00-\U0010ffff",
    "khitansmallscript": "\U00018b00-\U00018cff",
    "^khitansmallscript": "\x00-\U00018aff\U00018d00-\U0010ffff",
    "tangutsupplement": "\U00018d00-\U00018d8f",
    "^tangutsupplement": "\x00-\U00018cff\U00018d90-\U0010ffff",
    "kanasupplement": "\U0001b000-\U0001b0ff",
    "^kanasupplement": "\x00-\U0001afff\U0001b100-\U0010ffff",
    "kanaextendeda": "\U0001b100-\U0001b12f",
    "^kanaextendeda": "\x00-\U0001b0ff\U0001b130-\U0010ffff",
    "smallkanaextension": "\U0001b130-\U0001b16f",
    "^smallkanaextension": "\x00-\U0001b12f\U0001b170-\U0010ffff",
    "nushu": "\U0001b170-\U0001b2ff",
    "^nushu": "\x00-\U0001b16f\U0001b300-\U0010ffff",
    "duployan": "\U0001bc00-\U0001bc9f",
    "^duployan": "\x00-\U0001bbff\U0001bca0-\U0010ffff",
    "shorthandformatcontrols": "\U0001bca0-\U0001bcaf",
    "^shorthandformatcontrols": "\x00-\U0001bc9f\U0001bcb0-\U0010ffff",
    "byzantinemusicalsymbols": "\U0001d000-\U0001d0ff",
    "^byzantinemusicalsymbols": "\x00-\U0001cfff\U0001d100-\U0010ffff",
    "musicalsymbols": "\U0001d100-\U0001d1ff",
    "^musicalsymbols": "\x00-\U0001d0ff\U0001d200-\U0010ffff",
    "ancientgreekmusicalnotation": "\U0001d200-\U0001d24f",
    "^ancientgreekmusicalnotation": "\x00-\U0001d1ff\U0001d250-\U0010ffff",
    "mayannumerals": "\U0001d2e0-\U0001d2ff",
    "^mayannumerals": "\x00-\U0001d2df\U0001d300-\U0010ffff",
    "taixuanjingsymbols": "\U0001d300-\U0001d35f",
    "^taixuanjingsymbols": "\x00-\U0001d2ff\U0001d360-\U0010ffff",
    "countingrodnumerals": "\U0001d360-\U0001d37f",
    "^countingrodnumerals": "\x00-\U0001d35f\U0001d380-\U0010ffff",
    "mathematicalalphanumericsymbols": "\U0001d400-\U0001d7ff",
    "^mathematicalalphanumericsymbols": "\x00-\U0001d3ff\U0001d800-\U0010ffff",
    "suttonsignwriting": "\U0001d800-\U0001daaf",
    "^suttonsignwriting": "\x00-\U0001d7ff\U0001dab0-\U0010ffff",
    "glagoliticsupplement": "\U0001e000-\U0001e02f",
    "^glagoliticsupplement": "\x00-\U0001dfff\U0001e030-\U0010ffff",
    "nyiakengpuachuehmong": "\U0001e100-\U0001e14f",
    "^nyiakengpuachuehmong": "\x00-\U0001e0ff\U0001e150-\U0010ffff",
    "wancho": "\U0001e2c0-\U0001e2ff",
    "^wancho": "\x00-\U0001e2bf\U0001e300-\U0010ffff",
    "mendekikakui": "\U0001e800-\U0001e8df",
    "^mendekikakui": "\x00-\U0001e7ff\U0001e8e0-\U0010ffff",
    "adlam": "\U0001e900-\U0001e95f",
    "^adlam": "\x00-\U0001e8ff\U0001e960-\U0010ffff",
    "indicsiyaqnumbers": "\U0001ec70-\U0001ecbf",
    "^indicsiyaqnumbers": "\x00-\U0001ec6f\U0001ecc0-\U0010ffff",
    "ottomansiyaqnumbers": "\U0001ed00-\U0001ed4f",
    "^ottomansiyaqnumbers": "\x00-\U0001ecff\U0001ed50-\U0010ffff",
    "arabicmathematicalalphabeticsymbols": "\U0001ee00-\U0001eeff",
    "^arabicmathematicalalphabeticsymbols": "\x00-\U0001edff\U0001ef00-\U0010ffff",
    "mahjongtiles": "\U0001f000-\U0001f02f",
    "^mahjongtiles": "\x00-\U0001efff\U0001f030-\U0010ffff",
    "dominotiles": "\U0001f030-\U0001f09f",
    "^dominotiles": "\x00-\U0001f02f\U0001f0a0-\U0010ffff",
    "playingcards": "\U0001f0a0-\U0001f0ff",
    "^playingcards": "\x00-\U0001f09f\U0001f100-\U0010ffff",
    "enclosedalphanumericsupplement": "\U0001f100-\U0001f1ff",
    "^enclosedalphanumericsupplement": "\x00-\U0001f0ff\U0001f200-\U0010ffff",
    "enclosedideographicsupplement": "\U0001f200-\U0001f2ff",
    "^enclosedideographicsupplement": "\x00-\U0001f1ff\U0001f300-\U0010ffff",
    "miscellaneoussymbolsandpictographs": "\U0001f300-\U0001f5ff",
    "^miscellaneoussymbolsandpictographs": "\x00-\U0001f2ff\U0001f600-\U0010ffff",
    "emoticons": "\U0001f600-\U0001f64f",
    "^emoticons": "\x00-\U0001f5ff\U0001f650-\U0010ffff",
    "ornamentaldingbats": "\U0001f650-\U0001f67f",
    "^ornamentaldingbats": "\x00-\U0001f64f\U0001f680-\U0010ffff",
    "transportandmapsymbols": "\U0001f680-\U0001f6ff",
    "^transportandmapsymbols": "\x00-\U0001f67f\U0001f700-\U0010ffff",
    "alchemicalsymbols": "\U0001f700-\U0001f77f",
    "^alchemicalsymbols": "\x00-\U0001f6ff\U0001f780-\U0010ffff",
    "geometricshapesextended": "\U0001f780-\U0001f7ff",
    "^geometricshapesextended": "\x00-\U0001f77f\U0001f800-\U0010ffff",
    "supplementalarrowsc": "\U0001f800-\U0001f8ff",
    "^supplementalarrowsc": "\x00-\U0001f7ff\U0001f900-\U0010ffff",
    "supplementalsymbolsandpictographs": "\U0001f900-\U0001f9ff",
    "^supplementalsymbolsandpictographs": "\x00-\U0001f8ff\U0001fa00-\U0010ffff",
    "chesssymbols": "\U0001fa00-\U0001fa6f",
    "^chesssymbols": "\x00-\U0001f9ff\U0001fa70-\U0010ffff",
    "symbolsandpictographsextendeda": "\U0001fa70-\U0001faff",
    "^symbolsandpictographsextendeda": "\x00-\U0001fa6f\U0001fb00-\U0010ffff",
    "symbolsforlegacycomputing": "\U0001fb00-\U0001fbff",
    "^symbolsforlegacycomputing": "\x00-\U0001faff\U0001fc00-\U0010ffff",
    "cjkunifiedideographsextensionb": "\U00020000-\U0002a6df",
    "^cjkunifiedideographsextensionb": "\x00-\U0001ffff\U0002a6e0-\U0010ffff",
    "cjkunifiedideographsextensionc": "\U0002a700-\U0002b73f",
    "^cjkunifiedideographsextensionc": "\x00-\U0002a6ff\U0002b740-\U0010ffff",
    "cjkunifiedideographsextensiond": "\U0002b740-\U0002b81f",
    "^cjkunifiedideographsextensiond": "\x00-\U0002b73f\U0002b820-\U0010ffff",
    "cjkunifiedideographsextensione": "\U0002b820-\U0002ceaf",
    "^cjkunifiedideographsextensione": "\x00-\U0002b81f\U0002ceb0-\U0010ffff",
    "cjkunifiedideographsextensionf": "\U0002ceb0-\U0002ebef",
    "^cjkunifiedideographsextensionf": "\x00-\U0002ceaf\U0002ebf0-\U0010ffff",
    "cjkcompatibilityideographssupplement": "\U0002f800-\U0002fa1f",
    "^cjkcompatibilityideographssupplement": "\x00-\U0002f7ff\U0002fa20-\U0010ffff",
    "cjkunifiedideographsextensiong": "\U00030000-\U0003134f",
    "^cjkunifiedideographsextensiong": "\x00-\U0002ffff\U00031350-\U0010ffff",
    "tags": "\U000e0000-\U000e007f",
    "^tags": "\x00-\U000dffff\U000e0080-\U0010ffff",
    "variationselectorssupplement": "\U000e0100-\U000e01ef",
    "^variationselectorssupplement": "\x00-\U000e00ff\U000e01f0-\U0010ffff",
    "supplementaryprivateuseareaa": "\U000f0000-\U000fffff",
    "^supplementaryprivateuseareaa": "\x00-\U000effff\U00100000-\U0010ffff",
    "supplementaryprivateuseareab": "\U00100000-\U0010ffff",
    "^supplementaryprivateuseareab": "\x00-\U000fffff",
    "noblock": "\u0870-\u089f\u2fe0-\u2fef\U00010200-\U0001027f\U000103e0-\U000103ff\U00010570-\U000105ff\U00010780-\U000107ff\U000108b0-\U000108df\U00010940-\U0001097f\U00010aa0-\U00010abf\U00010bb0-\U00010bff\U00010c50-\U00010c7f\U00010d40-\U00010e5f\U00010ec0-\U00010eff\U00010f70-\U00010faf\U00011250-\U0001127f\U00011380-\U000113ff\U000114e0-\U0001157f\U000116d0-\U000116ff\U00011740-\U000117ff\U00011850-\U0001189f\U00011960-\U0001199f\U00011ab0-\U00011abf\U00011b00-\U00011bff\U00011cc0-\U00011cff\U00011db0-\U00011edf\U00011f00-\U00011faf\U00012550-\U00012fff\U00013440-\U000143ff\U00014680-\U000167ff\U00016a70-\U00016acf\U00016b90-\U00016e3f\U00016ea0-\U00016eff\U00016fa0-\U00016fdf\U00018d90-\U0001afff\U0001b300-\U0001bbff\U0001bcb0-\U0001cfff\U0001d250-\U0001d2df\U0001d380-\U0001d3ff\U0001dab0-\U0001dfff\U0001e030-\U0001e0ff\U0001e150-\U0001e2bf\U0001e300-\U0001e7ff\U0001e8e0-\U0001e8ff\U0001e960-\U0001ec6f\U0001ecc0-\U0001ecff\U0001ed50-\U0001edff\U0001ef00-\U0001efff\U0001fc00-\U0001ffff\U0002a6e0-\U0002a6ff\U0002ebf0-\U0002f7ff\U0002fa20-\U0002ffff\U00031350-\U000dffff\U000e0080-\U000e00ff\U000e01f0-\U000effff",
    "^noblock": "\x00-\u086f\u08a0-\u2fdf\u2ff0-\U000101ff\U00010280-\U000103df\U00010400-\U0001056f\U00010600-\U0001077f\U00010800-\U000108af\U000108e0-\U0001093f\U00010980-\U00010a9f\U00010ac0-\U00010baf\U00010c00-\U00010c4f\U00010c80-\U00010d3f\U00010e60-\U00010ebf\U00010f00-\U00010f6f\U00010fb0-\U0001124f\U00011280-\U0001137f\U00011400-\U000114df\U00011580-\U000116cf\U00011700-\U0001173f\U00011800-\U0001184f\U000118a0-\U0001195f\U000119a0-\U00011aaf\U00011ac0-\U00011aff\U00011c00-\U00011cbf\U00011d00-\U00011daf\U00011ee0-\U00011eff\U00011fb0-\U0001254f\U00013000-\U0001343f\U00014400-\U0001467f\U00016800-\U00016a6f\U00016ad0-\U00016b8f\U00016e40-\U00016e9f\U00016f00-\U00016f9f\U00016fe0-\U00018d8f\U0001b000-\U0001b2ff\U0001bc00-\U0001bcaf\U0001d000-\U0001d24f\U0001d2e0-\U0001d37f\U0001d400-\U0001daaf\U0001e000-\U0001e02f\U0001e100-\U0001e14f\U0001e2c0-\U0001e2ff\U0001e800-\U0001e8df\U0001e900-\U0001e95f\U0001ec70-\U0001ecbf\U0001ed00-\U0001ed4f\U0001ee00-\U0001eeff\U0001f000-\U0001fbff\U00020000-\U0002a6df\U0002a700-\U0002ebef\U0002f800-\U0002fa1f\U00030000-\U0003134f\U000e0000-\U000e007f\U000e0100-\U000e01ef",
}
ascii_blocks: dict[str, str] = {
    "basiclatin": "\x00-\x7f",
    "^basiclatin": "\x80-\U0010ffff",
    "latin1supplement": "",
    "^latin1supplement": "\x00-\U0010ffff",
    "latinextendeda": "",
    "^latinextendeda": "\x00-\U0010ffff",
    "latinextendedb": "",
    "^latinextendedb": "\x00-\U0010ffff",
    "ipaextensions": "",
    "^ipaextensions": "\x00-\U0010ffff",
    "spacingmodifierletters": "",
    "^spacingmodifierletters": "\x00-\U0010ffff",
    "combiningdiacriticalmarks": "",
    "^combiningdiacriticalmarks": "\x00-\U0010ffff",
    "greekandcoptic": "",
    "^greekandcoptic": "\x00-\U0010ffff",
    "cyrillic": "",
    "^cyrillic": "\x00-\U0010ffff",
    "cyrillicsupplement": "",
    "^cyrillicsupplement": "\x00-\U0010ffff",
    "armenian": "",
    "^armenian": "\x00-\U0010ffff",
    "hebrew": "",
    "^hebrew": "\x00-\U0010ffff",
    "arabic": "",
    "^arabic": "\x00-\U0010ffff",
    "syriac": "",
    "^syriac": "\x00-\U0010ffff",
    "arabicsupplement": "",
    "^arabicsupplement": "\x00-\U0010ffff",
    "thaana": "",
    "^thaana": "\x00-\U0010ffff",
    "nko": "",
    "^nko": "\x00-\U0010ffff",
    "samaritan": "",
    "^samaritan": "\x00-\U0010ffff",
    "mandaic": "",
    "^mandaic": "\x00-\U0010ffff",
    "syriacsupplement": "",
    "^syriacsupplement": "\x00-\U0010ffff",
    "arabicextendeda": "",
    "^arabicextendeda": "\x00-\U0010ffff",
    "devanagari": "",
    "^devanagari": "\x00-\U0010ffff",
    "bengali": "",
    "^bengali": "\x00-\U0010ffff",
    "gurmukhi": "",
    "^gurmukhi": "\x00-\U0010ffff",
    "gujarati": "",
    "^gujarati": "\x00-\U0010ffff",
    "oriya": "",
    "^oriya": "\x00-\U0010ffff",
    "tamil": "",
    "^tamil": "\x00-\U0010ffff",
    "telugu": "",
    "^telugu": "\x00-\U0010ffff",
    "kannada": "",
    "^kannada": "\x00-\U0010ffff",
    "malayalam": "",
    "^malayalam": "\x00-\U0010ffff",
    "sinhala": "",
    "^sinhala": "\x00-\U0010ffff",
    "thai": "",
    "^thai": "\x00-\U0010ffff",
    "lao": "",
    "^lao": "\x00-\U0010ffff",
    "tibetan": "",
    "^tibetan": "\x00-\U0010ffff",
    "myanmar": "",
    "^myanmar": "\x00-\U0010ffff",
    "georgian": "",
    "^georgian": "\x00-\U0010ffff",
    "hanguljamo": "",
    "^hanguljamo": "\x00-\U0010ffff",
    "ethiopic": "",
    "^ethiopic": "\x00-\U0010ffff",
    "ethiopicsupplement": "",
    "^ethiopicsupplement": "\x00-\U0010ffff",
    "cherokee": "",
    "^cherokee": "\x00-\U0010ffff",
    "unifiedcanadianaboriginalsyllabics": "",
    "^unifiedcanadianaboriginalsyllabics": "\x00-\U0010ffff",
    "ogham": "",
    "^ogham": "\x00-\U0010ffff",
    "runic": "",
    "^runic": "\x00-\U0010ffff",
    "tagalog": "",
    "^tagalog": "\x00-\U0010ffff",
    "hanunoo": "",
    "^hanunoo": "\x00-\U0010ffff",
    "buhid": "",
    "^buhid": "\x00-\U0010ffff",
    "tagbanwa": "",
    "^tagbanwa": "\x00-\U0010ffff",
    "khmer": "",
    "^khmer": "\x00-\U0010ffff",
    "mongolian": "",
    "^mongolian": "\x00-\U0010ffff",
    "unifiedcanadianaboriginalsyllabicsextended": "",
    "^unifiedcanadianaboriginalsyllabicsextended": "\x00-\U0010ffff",
    "limbu": "",
    "^limbu": "\x00-\U0010ffff",
    "taile": "",
    "^taile": "\x00-\U0010ffff",
    "newtailue": "",
    "^newtailue": "\x00-\U0010ffff",
    "khmersymbols": "",
    "^khmersymbols": "\x00-\U0010ffff",
    "buginese": "",
    "^buginese": "\x00-\U0010ffff",
    "taitham": "",
    "^taitham": "\x00-\U0010ffff",
    "combiningdiacriticalmarksextended": "",
    "^combiningdiacriticalmarksextended": "\x00-\U0010ffff",
    "balinese": "",
    "^balinese": "\x00-\U0010ffff",
    "sundanese": "",
    "^sundanese": "\x00-\U0010ffff",
    "batak": "",
    "^batak": "\x00-\U0010ffff",
    "lepcha": "",
    "^lepcha": "\x00-\U0010ffff",
    "olchiki": "",
    "^olchiki": "\x00-\U0010ffff",
    "cyrillicextendedc": "",
    "^cyrillicextendedc": "\x00-\U0010ffff",
    "georgianextended": "",
    "^georgianextended": "\x00-\U0010ffff",
    "sundanesesupplement": "",
    "^sundanesesupplement": "\x00-\U0010ffff",
    "vedicextensions": "",
    "^vedicextensions": "\x00-\U0010ffff",
    "phoneticextensions": "",
    "^phoneticextensions": "\x00-\U0010ffff",
    "phoneticextensionssupplement": "",
    "^phoneticextensionssupplement": "\x00-\U0010ffff",
    "combiningdiacriticalmarkssupplement": "",
    "^combiningdiacriticalmarkssupplement": "\x00-\U0010ffff",
    "latinextendedadditional": "",
    "^latinextendedadditional": "\x00-\U0010ffff",
    "greekextended": "",
    "^greekextended": "\x00-\U0010ffff",
    "generalpunctuation": "",
    "^generalpunctuation": "\x00-\U0010ffff",
    "superscriptsandsubscripts": "",
    "^superscriptsandsubscripts": "\x00-\U0010ffff",
    "currencysymbols": "",
    "^currencysymbols": "\x00-\U0010ffff",
    "combiningdiacriticalmarksforsymbols": "",
    "^combiningdiacriticalmarksforsymbols": "\x00-\U0010ffff",
    "letterlikesymbols": "",
    "^letterlikesymbols": "\x00-\U0010ffff",
    "numberforms": "",
    "^numberforms": "\x00-\U0010ffff",
    "arrows": "",
    "^arrows": "\x00-\U0010ffff",
    "mathematicaloperators": "",
    "^mathematicaloperators": "\x00-\U0010ffff",
    "miscellaneoustechnical": "",
    "^miscellaneoustechnical": "\x00-\U0010ffff",
    "controlpictures": "",
    "^controlpictures": "\x00-\U0010ffff",
    "opticalcharacterrecognition": "",
    "^opticalcharacterrecognition": "\x00-\U0010ffff",
    "enclosedalphanumerics": "",
    "^enclosedalphanumerics": "\x00-\U0010ffff",
    "boxdrawing": "",
    "^boxdrawing": "\x00-\U0010ffff",
    "blockelements": "",
    "^blockelements": "\x00-\U0010ffff",
    "geometricshapes": "",
    "^geometricshapes": "\x00-\U0010ffff",
    "miscellaneoussymbols": "",
    "^miscellaneoussymbols": "\x00-\U0010ffff",
    "dingbats": "",
    "^dingbats": "\x00-\U0010ffff",
    "miscellaneousmathematicalsymbolsa": "",
    "^miscellaneousmathematicalsymbolsa": "\x00-\U0010ffff",
    "supplementalarrowsa": "",
    "^supplementalarrowsa": "\x00-\U0010ffff",
    "braillepatterns": "",
    "^braillepatterns": "\x00-\U0010ffff",
    "supplementalarrowsb": "",
    "^supplementalarrowsb": "\x00-\U0010ffff",
    "miscellaneousmathematicalsymbolsb": "",
    "^miscellaneousmathematicalsymbolsb": "\x00-\U0010ffff",
    "supplementalmathematicaloperators": "",
    "^supplementalmathematicaloperators": "\x00-\U0010ffff",
    "miscellaneoussymbolsandarrows": "",
    "^miscellaneoussymbolsandarrows": "\x00-\U0010ffff",
    "glagolitic": "",
    "^glagolitic": "\x00-\U0010ffff",
    "latinextendedc": "",
    "^latinextendedc": "\x00-\U0010ffff",
    "coptic": "",
    "^coptic": "\x00-\U0010ffff",
    "georgiansupplement": "",
    "^georgiansupplement": "\x00-\U0010ffff",
    "tifinagh": "",
    "^tifinagh": "\x00-\U0010ffff",
    "ethiopicextended": "",
    "^ethiopicextended": "\x00-\U0010ffff",
    "cyrillicextendeda": "",
    "^cyrillicextendeda": "\x00-\U0010ffff",
    "supplementalpunctuation": "",
    "^supplementalpunctuation": "\x00-\U0010ffff",
    "cjkradicalssupplement": "",
    "^cjkradicalssupplement": "\x00-\U0010ffff",
    "kangxiradicals": "",
    "^kangxiradicals": "\x00-\U0010ffff",
    "ideographicdescriptioncharacters": "",
    "^ideographicdescriptioncharacters": "\x00-\U0010ffff",
    "cjksymbolsandpunctuation": "",
    "^cjksymbolsandpunctuation": "\x00-\U0010ffff",
    "hiragana": "",
    "^hiragana": "\x00-\U0010ffff",
    "katakana": "",
    "^katakana": "\x00-\U0010ffff",
    "bopomofo": "",
    "^bopomofo": "\x00-\U0010ffff",
    "hangulcompatibilityjamo": "",
    "^hangulcompatibilityjamo": "\x00-\U0010ffff",
    "kanbun": "",
    "^kanbun": "\x00-\U0010ffff",
    "bopomofoextended": "",
    "^bopomofoextended": "\x00-\U0010ffff",
    "cjkstrokes": "",
    "^cjkstrokes": "\x00-\U0010ffff",
    "katakanaphoneticextensions": "",
    "^katakanaphoneticextensions": "\x00-\U0010ffff",
    "enclosedcjklettersandmonths": "",
    "^enclosedcjklettersandmonths": "\x00-\U0010ffff",
    "cjkcompatibility": "",
    "^cjkcompatibility": "\x00-\U0010ffff",
    "cjkunifiedideographsextensiona": "",
    "^cjkunifiedideographsextensiona": "\x00-\U0010ffff",
    "yijinghexagramsymbols": "",
    "^yijinghexagramsymbols": "\x00-\U0010ffff",
    "cjkunifiedideographs": "",
    "^cjkunifiedideographs": "\x00-\U0010ffff",
    "yisyllables": "",
    "^yisyllables": "\x00-\U0010ffff",
    "yiradicals": "",
    "^yiradicals": "\x00-\U0010ffff",
    "lisu": "",
    "^lisu": "\x00-\U0010ffff",
    "vai": "",
    "^vai": "\x00-\U0010ffff",
    "cyrillicextendedb": "",
    "^cyrillicextendedb": "\x00-\U0010ffff",
    "bamum": "",
    "^bamum": "\x00-\U0010ffff",
    "modifiertoneletters": "",
    "^modifiertoneletters": "\x00-\U0010ffff",
    "latinextendedd": "",
    "^latinextendedd": "\x00-\U0010ffff",
    "sylotinagri": "",
    "^sylotinagri": "\x00-\U0010ffff",
    "commonindicnumberforms": "",
    "^commonindicnumberforms": "\x00-\U0010ffff",
    "phagspa": "",
    "^phagspa": "\x00-\U0010ffff",
    "saurashtra": "",
    "^saurashtra": "\x00-\U0010ffff",
    "devanagariextended": "",
    "^devanagariextended": "\x00-\U0010ffff",
    "kayahli": "",
    "^kayahli": "\x00-\U0010ffff",
    "rejang": "",
    "^rejang": "\x00-\U0010ffff",
    "hanguljamoextendeda": "",
    "^hanguljamoextendeda": "\x00-\U0010ffff",
    "javanese": "",
    "^javanese": "\x00-\U0010ffff",
    "myanmarextendedb": "",
    "^myanmarextendedb": "\x00-\U0010ffff",
    "cham": "",
    "^cham": "\x00-\U0010ffff",
    "myanmarextendeda": "",
    "^myanmarextendeda": "\x00-\U0010ffff",
    "taiviet": "",
    "^taiviet": "\x00-\U0010ffff",
    "meeteimayekextensions": "",
    "^meeteimayekextensions": "\x00-\U0010ffff",
    "ethiopicextendeda": "",
    "^ethiopicextendeda": "\x00-\U0010ffff",
    "latinextendede": "",
    "^latinextendede": "\x00-\U0010ffff",
    "cherokeesupplement": "",
    "^cherokeesupplement": "\x00-\U0010ffff",
    "meeteimayek": "",
    "^meeteimayek": "\x00-\U0010ffff",
    "hangulsyllables": "",
    "^hangulsyllables": "\x00-\U0010ffff",
    "hanguljamoextendedb": "",
    "^hanguljamoextendedb": "\x00-\U0010ffff",
    "highsurrogates": "",
    "^highsurrogates": "\x00-\U0010ffff",
    "highprivateusesurrogates": "",
    "^highprivateusesurrogates": "\x00-\U0010ffff",
    "lowsurrogates": "",
    "^lowsurrogates": "\x00-\U0010ffff",
    "privateusearea": "",
    "^privateusearea": "\x00-\U0010ffff",
    "cjkcompatibilityideographs": "",
    "^cjkcompatibilityideographs": "\x00-\U0010ffff",
    "alphabeticpresentationforms": "",
    "^alphabeticpresentationforms": "\x00-\U0010ffff",
    "arabicpresentationformsa": "",
    "^arabicpresentationformsa": "\x00-\U0010ffff",
    "variationselectors": "",
    "^variationselectors": "\x00-\U0010ffff",
    "verticalforms": "",
    "^verticalforms": "\x00-\U0010ffff",
    "combininghalfmarks": "",
    "^combininghalfmarks": "\x00-\U0010ffff",
    "cjkcompatibilityforms": "",
    "^cjkcompatibilityforms": "\x00-\U0010ffff",
    "smallformvariants": "",
    "^smallformvariants": "\x00-\U0010ffff",
    "arabicpresentationformsb": "",
    "^arabicpresentationformsb": "\x00-\U0010ffff",
    "halfwidthandfullwidthforms": "",
    "^halfwidthandfullwidthforms": "\x00-\U0010ffff",
    "specials": "",
    "^specials": "\x00-\U0010ffff",
    "linearbsyllabary": "",
    "^linearbsyllabary": "\x00-\U0010ffff",
    "linearbideograms": "",
    "^linearbideograms": "\x00-\U0010ffff",
    "aegeannumbers": "",
    "^aegeannumbers": "\x00-\U0010ffff",
    "ancientgreeknumbers": "",
    "^ancientgreeknumbers": "\x00-\U0010ffff",
    "ancientsymbols": "",
    "^ancientsymbols": "\x00-\U0010ffff",
    "phaistosdisc": "",
    "^phaistosdisc": "\x00-\U0010ffff",
    "lycian": "",
    "^lycian": "\x00-\U0010ffff",
    "carian": "",
    "^carian": "\x00-\U0010ffff",
    "copticepactnumbers": "",
    "^copticepactnumbers": "\x00-\U0010ffff",
    "olditalic": "",
    "^olditalic": "\x00-\U0010ffff",
    "gothic": "",
    "^gothic": "\x00-\U0010ffff",
    "oldpermic": "",
    "^oldpermic": "\x00-\U0010ffff",
    "ugaritic": "",
    "^ugaritic": "\x00-\U0010ffff",
    "oldpersian": "",
    "^oldpersian": "\x00-\U0010ffff",
    "deseret": "",
    "^deseret": "\x00-\U0010ffff",
    "shavian": "",
    "^shavian": "\x00-\U0010ffff",
    "osmanya": "",
    "^osmanya": "\x00-\U0010ffff",
    "osage": "",
    "^osage": "\x00-\U0010ffff",
    "elbasan": "",
    "^elbasan": "\x00-\U0010ffff",
    "caucasianalbanian": "",
    "^caucasianalbanian": "\x00-\U0010ffff",
    "lineara": "",
    "^lineara": "\x00-\U0010ffff",
    "cypriotsyllabary": "",
    "^cypriotsyllabary": "\x00-\U0010ffff",
    "imperialaramaic": "",
    "^imperialaramaic": "\x00-\U0010ffff",
    "palmyrene": "",
    "^palmyrene": "\x00-\U0010ffff",
    "nabataean": "",
    "^nabataean": "\x00-\U0010ffff",
    "hatran": "",
    "^hatran": "\x00-\U0010ffff",
    "phoenician": "",
    "^phoenician": "\x00-\U0010ffff",
    "lydian": "",
    "^lydian": "\x00-\U0010ffff",
    "meroitichieroglyphs": "",
    "^meroitichieroglyphs": "\x00-\U0010ffff",
    "meroiticcursive": "",
    "^meroiticcursive": "\x00-\U0010ffff",
    "kharoshthi": "",
    "^kharoshthi": "\x00-\U0010ffff",
    "oldsoutharabian": "",
    "^oldsoutharabian": "\x00-\U0010ffff",
    "oldnortharabian": "",
    "^oldnortharabian": "\x00-\U0010ffff",
    "manichaean": "",
    "^manichaean": "\x00-\U0010ffff",
    "avestan": "",
    "^avestan": "\x00-\U0010ffff",
    "inscriptionalparthian": "",
    "^inscriptionalparthian": "\x00-\U0010ffff",
    "inscriptionalpahlavi": "",
    "^inscriptionalpahlavi": "\x00-\U0010ffff",
    "psalterpahlavi": "",
    "^psalterpahlavi": "\x00-\U0010ffff",
    "oldturkic": "",
    "^oldturkic": "\x00-\U0010ffff",
    "oldhungarian": "",
    "^oldhungarian": "\x00-\U0010ffff",
    "hanifirohingya": "",
    "^hanifirohingya": "\x00-\U0010ffff",
    "ruminumeralsymbols": "",
    "^ruminumeralsymbols": "\x00-\U0010ffff",
    "yezidi": "",
    "^yezidi": "\x00-\U0010ffff",
    "oldsogdian": "",
    "^oldsogdian": "\x00-\U0010ffff",
    "sogdian": "",
    "^sogdian": "\x00-\U0010ffff",
    "chorasmian": "",
    "^chorasmian": "\x00-\U0010ffff",
    "elymaic": "",
    "^elymaic": "\x00-\U0010ffff",
    "brahmi": "",
    "^brahmi": "\x00-\U0010ffff",
    "kaithi": "",
    "^kaithi": "\x00-\U0010ffff",
    "sorasompeng": "",
    "^sorasompeng": "\x00-\U0010ffff",
    "chakma": "",
    "^chakma": "\x00-\U0010ffff",
    "mahajani": "",
    "^mahajani": "\x00-\U0010ffff",
    "sharada": "",
    "^sharada": "\x00-\U0010ffff",
    "sinhalaarchaicnumbers": "",
    "^sinhalaarchaicnumbers": "\x00-\U0010ffff",
    "khojki": "",
    "^khojki": "\x00-\U0010ffff",
    "multani": "",
    "^multani": "\x00-\U0010ffff",
    "khudawadi": "",
    "^khudawadi": "\x00-\U0010ffff",
    "grantha": "",
    "^grantha": "\x00-\U0010ffff",
    "newa": "",
    "^newa": "\x00-\U0010ffff",
    "tirhuta": "",
    "^tirhuta": "\x00-\U0010ffff",
    "siddham": "",
    "^siddham": "\x00-\U0010ffff",
    "modi": "",
    "^modi": "\x00-\U0010ffff",
    "mongoliansupplement": "",
    "^mongoliansupplement": "\x00-\U0010ffff",
    "takri": "",
    "^takri": "\x00-\U0010ffff",
    "ahom": "",
    "^ahom": "\x00-\U0010ffff",
    "dogra": "",
    "^dogra": "\x00-\U0010ffff",
    "warangciti": "",
    "^warangciti": "\x00-\U0010ffff",
    "divesakuru": "",
    "^divesakuru": "\x00-\U0010ffff",
    "nandinagari": "",
    "^nandinagari": "\x00-\U0010ffff",
    "zanabazarsquare": "",
    "^zanabazarsquare": "\x00-\U0010ffff",
    "soyombo": "",
    "^soyombo": "\x00-\U0010ffff",
    "paucinhau": "",
    "^paucinhau": "\x00-\U0010ffff",
    "bhaiksuki": "",
    "^bhaiksuki": "\x00-\U0010ffff",
    "marchen": "",
    "^marchen": "\x00-\U0010ffff",
    "masaramgondi": "",
    "^masaramgondi": "\x00-\U0010ffff",
    "gunjalagondi": "",
    "^gunjalagondi": "\x00-\U0010ffff",
    "makasar": "",
    "^makasar": "\x00-\U0010ffff",
    "lisusupplement": "",
    "^lisusupplement": "\x00-\U0010ffff",
    "tamilsupplement": "",
    "^tamilsupplement": "\x00-\U0010ffff",
    "cuneiform": "",
    "^cuneiform": "\x00-\U0010ffff",
    "cuneiformnumbersandpunctuation": "",
    "^cuneiformnumbersandpunctuation": "\x00-\U0010ffff",
    "earlydynasticcuneiform": "",
    "^earlydynasticcuneiform": "\x00-\U0010ffff",
    "egyptianhieroglyphs": "",
    "^egyptianhieroglyphs": "\x00-\U0010ffff",
    "egyptianhieroglyphformatcontrols": "",
    "^egyptianhieroglyphformatcontrols": "\x00-\U0010ffff",
    "anatolianhieroglyphs": "",
    "^anatolianhieroglyphs": "\x00-\U0010ffff",
    "bamumsupplement": "",
    "^bamumsupplement": "\x00-\U0010ffff",
    "mro": "",
    "^mro": "\x00-\U0010ffff",
    "bassavah": "",
    "^bassavah": "\x00-\U0010ffff",
    "pahawhhmong": "",
    "^pahawhhmong": "\x00-\U0010ffff",
    "medefaidrin": "",
    "^medefaidrin": "\x00-\U0010ffff",
    "miao": "",
    "^miao": "\x00-\U0010ffff",
    "ideographicsymbolsandpunctuation": "",
    "^ideographicsymbolsandpunctuation": "\x00-\U0010ffff",
    "tangut": "",
    "^tangut": "\x00-\U0010ffff",
    "tangutcomponents": "",
    "^tangutcomponents": "\x00-\U0010ffff",
    "khitansmallscript": "",
    "^khitansmallscript": "\x00-\U0010ffff",
    "tangutsupplement": "",
    "^tangutsupplement": "\x00-\U0010ffff",
    "kanasupplement": "",
    "^kanasupplement": "\x00-\U0010ffff",
    "kanaextendeda": "",
    "^kanaextendeda": "\x00-\U0010ffff",
    "smallkanaextension": "",
    "^smallkanaextension": "\x00-\U0010ffff",
    "nushu": "",
    "^nushu": "\x00-\U0010ffff",
    "duployan": "",
    "^duployan": "\x00-\U0010ffff",
    "shorthandformatcontrols": "",
    "^shorthandformatcontrols": "\x00-\U0010ffff",
    "byzantinemusicalsymbols": "",
    "^byzantinemusicalsymbols": "\x00-\U0010ffff",
    "musicalsymbols": "",
    "^musicalsymbols": "\x00-\U0010ffff",
    "ancientgreekmusicalnotation": "",
    "^ancientgreekmusicalnotation": "\x00-\U0010ffff",
    "mayannumerals": "",
    "^mayannumerals": "\x00-\U0010ffff",
    "taixuanjingsymbols": "",
    "^taixuanjingsymbols": "\x00-\U0010ffff",
    "countingrodnumerals": "",
    "^countingrodnumerals": "\x00-\U0010ffff",
    "mathematicalalphanumericsymbols": "",
    "^mathematicalalphanumericsymbols": "\x00-\U0010ffff",
    "suttonsignwriting": "",
    "^suttonsignwriting": "\x00-\U0010ffff",
    "glagoliticsupplement": "",
    "^glagoliticsupplement": "\x00-\U0010ffff",
    "nyiakengpuachuehmong": "",
    "^nyiakengpuachuehmong": "\x00-\U0010ffff",
    "wancho": "",
    "^wancho": "\x00-\U0010ffff",
    "mendekikakui": "",
    "^mendekikakui": "\x00-\U0010ffff",
    "adlam": "",
    "^adlam": "\x00-\U0010ffff",
    "indicsiyaqnumbers": "",
    "^indicsiyaqnumbers": "\x00-\U0010ffff",
    "ottomansiyaqnumbers": "",
    "^ottomansiyaqnumbers": "\x00-\U0010ffff",
    "arabicmathematicalalphabeticsymbols": "",
    "^arabicmathematicalalphabeticsymbols": "\x00-\U0010ffff",
    "mahjongtiles": "",
    "^mahjongtiles": "\x00-\U0010ffff",
    "dominotiles": "",
    "^dominotiles": "\x00-\U0010ffff",
    "playingcards": "",
    "^playingcards": "\x00-\U0010ffff",
    "enclosedalphanumericsupplement": "",
    "^enclosedalphanumericsupplement": "\x00-\U0010ffff",
    "enclosedideographicsupplement": "",
    "^enclosedideographicsupplement": "\x00-\U0010ffff",
    "miscellaneoussymbolsandpictographs": "",
    "^miscellaneoussymbolsandpictographs": "\x00-\U0010ffff",
    "emoticons": "",
    "^emoticons": "\x00-\U0010ffff",
    "ornamentaldingbats": "",
    "^ornamentaldingbats": "\x00-\U0010ffff",
    "transportandmapsymbols": "",
    "^transportandmapsymbols": "\x00-\U0010ffff",
    "alchemicalsymbols": "",
    "^alchemicalsymbols": "\x00-\U0010ffff",
    "geometricshapesextended": "",
    "^geometricshapesextended": "\x00-\U0010ffff",
    "supplementalarrowsc": "",
    "^supplementalarrowsc": "\x00-\U0010ffff",
    "supplementalsymbolsandpictographs": "",
    "^supplementalsymbolsandpictographs": "\x00-\U0010ffff",
    "chesssymbols": "",
    "^chesssymbols": "\x00-\U0010ffff",
    "symbolsandpictographsextendeda": "",
    "^symbolsandpictographsextendeda": "\x00-\U0010ffff",
    "symbolsforlegacycomputing": "",
    "^symbolsforlegacycomputing": "\x00-\U0010ffff",
    "cjkunifiedideographsextensionb": "",
    "^cjkunifiedideographsextensionb": "\x00-\U0010ffff",
    "cjkunifiedideographsextensionc": "",
    "^cjkunifiedideographsextensionc": "\x00-\U0010ffff",
    "cjkunifiedideographsextensiond": "",
    "^cjkunifiedideographsextensiond": "\x00-\U0010ffff",
    "cjkunifiedideographsextensione": "",
    "^cjkunifiedideographsextensione": "\x00-\U0010ffff",
    "cjkunifiedideographsextensionf": "",
    "^cjkunifiedideographsextensionf": "\x00-\U0010ffff",
    "cjkcompatibilityideographssupplement": "",
    "^cjkcompatibilityideographssupplement": "\x00-\U0010ffff",
    "cjkunifiedideographsextensiong": "",
    "^cjkunifiedideographsextensiong": "\x00-\U0010ffff",
    "tags": "",
    "^tags": "\x00-\U0010ffff",
    "variationselectorssupplement": "",
    "^variationselectorssupplement": "\x00-\U0010ffff",
    "supplementaryprivateuseareaa": "",
    "^supplementaryprivateuseareaa": "\x00-\U0010ffff",
    "supplementaryprivateuseareab": "",
    "^supplementaryprivateuseareab": "\x00-\U0010ffff",
    "noblock": "",
    "^noblock": "\x00-\U0010ffff",
}
