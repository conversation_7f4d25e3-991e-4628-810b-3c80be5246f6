"""
AI Model Manager for Rilance Code Studio
Handles loading, switching, and managing AI models with support for both Transformers and vLLM backends
"""

import asyncio
import os
import subprocess
import shutil
import sys
import platform
from pathlib import Path
from typing import Optional, Dict, Any, List, AsyncGenerator
from enum import Enum
import torch
from transformers import (
    AutoTokenizer, AutoModelForCausalLM,
    TextIteratorStreamer, BitsAndBytesConfig
)
from threading import Thread
import git

# TensorFlow imports (optional)
try:
    import tensorflow as tf
    TENSORFLOW_AVAILABLE = True
except ImportError:
    tf = None
    TENSORFLOW_AVAILABLE = False

# GGUF/Llama.cpp imports (optional)
try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    Llama = None
    LLAMA_CPP_AVAILABLE = False

# HuggingFace Hub for GGUF downloads
try:
    from huggingface_hub import hf_hub_download
    HUGGINGFACE_HUB_AVAILABLE = True
except ImportError:
    hf_hub_download = None
    HUGGINGFACE_HUB_AVAILABLE = False

from core.config import settings
from core.logging import logger, log_model_operation, log_performance, log_git_operation
from core.exceptions import ModelException


class ModelBackend(Enum):
    """Supported model backends"""
    TRANSFORMERS = "transformers"
    VLLM = "vllm"
    TENSORFLOW = "tensorflow"
    GGUF = "gguf"


class ModelInfo:
    """Information about a loaded model"""
    
    def __init__(
        self,
        name: str,
        backend: ModelBackend,
        model_path: str,
        context_length: int = 4096,
        loaded: bool = False
    ):
        self.name = name
        self.backend = backend
        self.model_path = model_path
        self.context_length = context_length
        self.loaded = loaded
        self.load_time: Optional[float] = None


class ModelManager:
    """Manages AI models for the Rilance Code Studio server"""
    
    def __init__(self):
        self.current_model: Optional[ModelInfo] = None
        self.tokenizer: Optional[Any] = None
        self.model: Optional[Any] = None
        self.vllm_engine: Optional[Any] = None
        self.tf_model: Optional[Any] = None
        self.tf_tokenizer: Optional[Any] = None
        self.gguf_model: Optional[Any] = None
        self.device = self._detect_device()
        self.models_cache_dir = settings.models_cache_path
        self.llm_models_dir = self.models_cache_dir / "llm"
        self.t2i_models_dir = self.models_cache_dir / "t2i"

        # Create directory structure
        self.models_cache_dir.mkdir(exist_ok=True)
        self.llm_models_dir.mkdir(exist_ok=True)
        self.t2i_models_dir.mkdir(exist_ok=True)
        
        logger.info(f"ModelManager initialized with device: {self.device}")

        # Model-specific optimizations
        self.model_optimizations = {
            "Menlo/Jan-nano-128k": {
                "recommended_backend": ModelBackend.VLLM,
                "max_context": 131072,
                "rope_scaling": {
                    "rope_type": "yarn",
                    "factor": 3.2,
                    "original_max_position_embeddings": 40960
                },
                "trust_remote_code": True,
                "enable_auto_tool_choice": True,
                "tool_call_parser": "hermes"
            },
            "microsoft/DialoGPT-medium": {
                "recommended_backend": ModelBackend.TRANSFORMERS,
                "load_in_4bit": False,
                "max_context": 1024
            },
            "gpt2": {
                "recommended_backend": ModelBackend.TRANSFORMERS,
                "load_in_4bit": False,
                "max_context": 1024
            },
            "Menlo/Lucy-128k-gguf": {
                "recommended_backend": ModelBackend.GGUF,
                "max_context": 131072,
                "n_ctx": 131072,
                "n_gpu_layers": -1,  # Use all GPU layers if available
                "chat_format": "chatml",
                "trust_remote_code": True,
                "enable_auto_tool_choice": True,
                "tool_call_parser": "hermes",
                "gguf_file": "lucy_128k-Q8_0.gguf",
                "download_url": "https://huggingface.co/Menlo/Lucy-128k-gguf/resolve/main/lucy_128k-Q8_0.gguf?download=true"
            }
        }
    
    def _detect_device(self) -> str:
        """Detect the best available device"""
        if torch.cuda.is_available():
            device = "cuda"
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            logger.info(f"CUDA available: {gpu_count} GPU(s) - {gpu_name}")
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device = "mps"
            logger.info("MPS (Apple Silicon) available")
        else:
            device = "cpu"
            logger.info("Using CPU")
        
        return device

    def _resolve_target_device(self, device: str = None, backend: ModelBackend = None) -> str:
        """Resolve target device based on user preference, backend, and system capabilities"""

        # If no device specified, use auto-detected device
        if device is None or device.lower() == "auto":
            return self.device

        # Normalize device input
        device = device.lower()

        # Handle device aliases
        if device in ["gpu", "cuda"]:
            device = "cuda"
        elif device == "cpu":
            device = "cpu"
        elif device in ["mps", "apple", "metal"]:
            device = "mps"

        # Validate device availability
        if device == "cuda":
            if not torch.cuda.is_available():
                logger.warning("⚠️ CUDA requested but not available, falling back to CPU")
                return "cpu"
            logger.info(f"✅ Using CUDA device: {torch.cuda.get_device_name(0)}")
            return "cuda"

        elif device == "mps":
            if not (hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()):
                logger.warning("⚠️ MPS requested but not available, falling back to CPU")
                return "cpu"
            logger.info("✅ Using MPS (Apple Silicon) device")
            return "mps"

        elif device == "cpu":
            # CPU is always available
            if backend == ModelBackend.VLLM:
                logger.warning("⚠️ vLLM backend typically requires GPU, performance may be poor on CPU")
            logger.info("✅ Using CPU device")
            return "cpu"

        else:
            logger.warning(f"⚠️ Unknown device '{device}', falling back to auto-detected: {self.device}")
            return self.device

    async def download_model(self, model_name_or_url: str, force: bool = False, model_type: str = "llm") -> str:
        """Download a model from HuggingFace using git or direct file download"""

        # Determine if it's a URL or model name
        if model_name_or_url.startswith(('http://', 'https://')):
            model_url = model_name_or_url
            # Check if it's a direct file download (e.g., .gguf file)
            if '/resolve/' in model_url and model_url.endswith('.gguf'):
                # Direct GGUF file download
                import urllib.parse
                from pathlib import Path
                
                # Parse filename from URL
                parsed_url = urllib.parse.urlparse(model_url)
                filename = Path(parsed_url.path).name
                
                # Clean filename - remove query parameters
                if '?' in filename:
                    filename = filename.split('?')[0]
                
                # Determine target directory
                if model_type.lower() == "t2i":
                    target_dir = self.t2i_models_dir
                else:
                    target_dir = self.llm_models_dir
                
                target_file = target_dir / filename
                
                # Check if file already exists
                if target_file.exists() and not force:
                    logger.info(f"GGUF file {filename} already exists at {target_file}")
                    return str(target_file)
                
                # Remove existing file if force is True
                if target_file.exists() and force:
                    logger.info(f"Removing existing file: {filename}")
                    target_file.unlink()
                
                # Download single GGUF file
                return await self._download_single_gguf_file(model_url, target_file, filename)
            else:
                # Repository download
                model_name = model_url.split('/')[-1]
        else:
            model_name = model_name_or_url.replace('/', '_')
            model_url = f"https://huggingface.co/{model_name_or_url}"

        # Determine target directory based on model type
        if model_type.lower() == "t2i":
            target_dir = self.t2i_models_dir
        else:
            target_dir = self.llm_models_dir

        model_path = target_dir / model_name
        
        # Check if model already exists
        if model_path.exists() and not force:
            logger.info(f"Model {model_name} already exists at {model_path}")
            return str(model_path)
        
        # Remove existing model if force is True
        if model_path.exists() and force:
            logger.info(f"Removing existing model: {model_name}")
            shutil.rmtree(model_path)
        
        log_model_operation("download", model_name, f"from {model_url}")
        
        try:
            # Try multiple download strategies in order of reliability
            success = False

            # Strategy 1: Try HuggingFace Hub first (most reliable)
            try:
                logger.info("🤗 Attempting HuggingFace Hub download...")
                success = await self._download_with_huggingface_hub(model_url, model_path, model_name)
            except Exception as hf_error:
                logger.warning(f"HuggingFace Hub download failed: {hf_error}")
                logger.info("Trying Git clone method...")

            # Strategy 2: Try Git clone if HuggingFace Hub fails
            if not success:
                try:
                    success = await self._download_with_git(model_url, model_path, model_name)
                except Exception as git_error:
                    logger.warning(f"Git download failed: {git_error}")
                    logger.info("Trying direct HTTP download...")

            # Strategy 3: Try direct HTTP download as last resort
            if not success:
                try:
                    success = await self._download_with_http(model_url, model_path, model_name)
                except Exception as http_error:
                    logger.warning(f"HTTP download failed: {http_error}")

            if not success:
                raise ModelException("All download methods failed. Please check your internet connection and try again.")

            # Verify download success
            if model_path.exists() and any(model_path.iterdir()):
                # Calculate download statistics
                total_size = sum(f.stat().st_size for f in model_path.rglob('*') if f.is_file())
                file_count = len(list(model_path.rglob('*')))

                log_model_operation("download_complete", model_name,
                                  f"({file_count} files, {total_size / (1024*1024):.1f} MB)")
                logger.info(f"📂 Location: {model_path}")

                return str(model_path)
            else:
                raise ModelException("Download completed but model directory is empty")
            
        except Exception as e:
            logger.error(f"Failed to download model {model_name}: {e}")
            # Clean up partial download
            if model_path.exists():
                shutil.rmtree(model_path)
            raise ModelException(f"Failed to download model: {e}")
    
    def is_model_loaded(self) -> bool:
        """Check if a model is currently loaded"""
        return self.current_model is not None and self.current_model.loaded
    
    def get_model_info(self) -> Optional[ModelInfo]:
        """Get information about the currently loaded model"""
        return self.current_model
    
    def list_cached_models(self) -> Dict[str, List[str]]:
        """List all cached models by type (LLM and T2I)"""
        models = {
            "llm": [],
            "t2i": []
        }

        # Scan LLM models directory
        if self.llm_models_dir.exists():
            for item in self.llm_models_dir.iterdir():
                if item.is_dir() and self._is_valid_model_directory(item):
                    models["llm"].append(item.name)

        # Scan T2I models directory
        if self.t2i_models_dir.exists():
            for item in self.t2i_models_dir.iterdir():
                if item.is_dir() and self._is_valid_model_directory(item):
                    models["t2i"].append(item.name)

        # Sort both lists
        models["llm"] = sorted(models["llm"])
        models["t2i"] = sorted(models["t2i"])

        return models

    def _is_valid_model_directory(self, model_path: Path) -> bool:
        """Check if a directory contains valid model files"""
        if not model_path.exists():
            return False
        
        # Handle single GGUF file
        if model_path.is_file() and model_path.suffix == '.gguf':
            return True
        
        # Handle directory-based models
        if not model_path.is_dir():
            return False

        # Check for GGUF files in directory
        gguf_files = list(model_path.glob('*.gguf'))
        if gguf_files:
            return True

        # Check for essential model files (traditional format)
        required_files = [
            "config.json",
            "tokenizer.json",
            "tokenizer_config.json"
        ]

        # At least config.json should exist for traditional models
        if not (model_path / "config.json").exists():
            return False

        # Check for model weights (any of these formats)
        weight_files = [
            "pytorch_model.bin",
            "model.safetensors",
            "pytorch_model-00001-of-*.bin",
            "model-00001-of-*.safetensors"
        ]

        has_weights = any(
            list(model_path.glob(pattern)) for pattern in weight_files
        )

        return has_weights

    def get_model_info_from_disk(self, model_name: str, model_type: str = "llm") -> Optional[Dict[str, Any]]:
        """Get model information from disk"""
        target_dir = self.llm_models_dir if model_type == "llm" else self.t2i_models_dir
        model_path = target_dir / model_name

        if not model_path.exists() or not self._is_valid_model_directory(model_path):
            return None

        try:
            # Read config.json for model info
            config_path = model_path / "config.json"
            if config_path.exists():
                import json
                with open(config_path, 'r') as f:
                    config = json.load(f)

                # Calculate directory size
                total_size = sum(f.stat().st_size for f in model_path.rglob('*') if f.is_file())

                return {
                    "name": model_name,
                    "path": str(model_path),
                    "size_bytes": total_size,
                    "size_gb": total_size / (1024**3),
                    "config": config,
                    "model_type": config.get("model_type", "unknown"),
                    "architecture": config.get("architectures", ["unknown"])[0] if config.get("architectures") else "unknown",
                    "vocab_size": config.get("vocab_size", 0),
                    "hidden_size": config.get("hidden_size", 0),
                    "num_layers": config.get("num_hidden_layers", 0),
                    "max_position_embeddings": config.get("max_position_embeddings", 0)
                }
        except Exception as e:
            logger.warning(f"Failed to read model info for {model_name}: {e}")

        return None

    def delete_model(self, model_name: str, model_type: str = "llm") -> bool:
        """Delete a model from disk"""
        target_dir = self.llm_models_dir if model_type == "llm" else self.t2i_models_dir
        model_path = target_dir / model_name

        if not model_path.exists():
            logger.warning(f"Model {model_name} not found in {model_type} directory")
            return False

        try:
            import shutil
            shutil.rmtree(model_path)
            logger.info(f"Successfully deleted model {model_name} from {model_type} directory")
            return True
        except Exception as e:
            logger.error(f"Failed to delete model {model_name}: {e}")
            return False

    def get_model_status(self, model_name: str) -> Dict[str, Any]:
        """Get comprehensive status of a model"""
        # Check in both directories
        llm_info = self.get_model_info_from_disk(model_name, "llm")
        t2i_info = self.get_model_info_from_disk(model_name, "t2i")

        status = {
            "name": model_name,
            "downloaded": False,
            "type": None,
            "path": None,
            "size_gb": 0,
            "backend": "unknown"
        }

        if llm_info:
            status.update({
                "downloaded": True,
                "type": "llm",
                "path": llm_info["path"],
                "size_gb": llm_info["size_gb"],
                "backend": self.model_optimizations.get(model_name, {}).get("recommended_backend", ModelBackend.TRANSFORMERS).value,
                "architecture": llm_info["architecture"],
                "max_context": llm_info["max_position_embeddings"]
            })
        elif t2i_info:
            status.update({
                "downloaded": True,
                "type": "t2i",
                "path": t2i_info["path"],
                "size_gb": t2i_info["size_gb"],
                "backend": "diffusers",
                "architecture": t2i_info["architecture"]
            })

        return status

    def _validate_backend_compatibility(self, backend: ModelBackend) -> bool:
        """Validate if backend is compatible with current system"""
        if backend == ModelBackend.VLLM:
            # Check Windows compatibility
            if platform.system() == "Windows":
                logger.warning("⚠️ vLLM加载方式暂时不适用于Windows端")
                logger.warning("⚠️ vLLM backend is currently not compatible with Windows")
                return False

            # Check if CUDA is available for vLLM
            if not torch.cuda.is_available():
                logger.warning("⚠️ vLLM backend requires CUDA but GPU not available")
                return False

            # Check if vLLM is installed
            try:
                import vllm
                return True
            except ImportError:
                logger.warning("⚠️ vLLM not installed")
                return False

        elif backend == ModelBackend.TRANSFORMERS:
            # Transformers should always be available
            try:
                import transformers
                return True
            except ImportError:
                logger.error("❌ Transformers library not available")
                return False

        elif backend == ModelBackend.TENSORFLOW:
            # Check if TensorFlow is available
            if not TENSORFLOW_AVAILABLE:
                logger.warning("⚠️ TensorFlow not installed")
                return False

            try:
                # Check TensorFlow installation
                tf.config.list_physical_devices()
                return True
            except Exception as e:
                logger.warning(f"⚠️ TensorFlow validation failed: {e}")
                return False

        elif backend == ModelBackend.GGUF:
            # Check if llama-cpp-python is available
            if not LLAMA_CPP_AVAILABLE:
                logger.warning("⚠️ llama-cpp-python not installed")
                return False

            try:
                # Test llama-cpp-python functionality
                return True
            except Exception as e:
                logger.warning(f"⚠️ llama-cpp-python validation failed: {e}")
                return False

        return True

    async def _resolve_model_path(self, model_name_or_path: str, force_download: bool) -> tuple[str, str]:
        """Resolve model path and name with proper validation"""

        # Check if it's a local path
        if os.path.exists(model_name_or_path):
            model_path = model_name_or_path
            model_name = Path(model_path).name
            logger.info(f"📂 Using local model: {model_path}")
            return model_path, model_name

        # Try to find in cache first
        cached_name = model_name_or_path.replace('/', '_')
        cached_path = self.models_cache_dir / cached_name

        if cached_path.exists() and not force_download:
            model_path = str(cached_path)
            model_name = cached_name
            logger.info(f"💾 Using cached model: {model_path}")
            return model_path, model_name

        # Model not found locally, need to download
        if force_download or not cached_path.exists():
            logger.info(f"📥 Model not found locally, downloading: {model_name_or_path}")
            model_path = await self.download_model(model_name_or_path, force_download)
            model_name = cached_name
            return model_path, model_name

        raise ModelException(f"Model not found: {model_name_or_path}")

    def _validate_model_files(self, model_path: str) -> bool:
        """Validate that model directory contains required files"""
        model_dir = Path(model_path)

        if not model_dir.exists():
            return False

        # Check for essential model files
        required_files = ["config.json"]
        optional_files = ["pytorch_model.bin", "model.safetensors", "tokenizer.json", "tokenizer_config.json"]

        # At least config.json should exist
        if not (model_dir / "config.json").exists():
            logger.error(f"❌ Missing config.json in {model_path}")
            return False

        # Check for at least one model weight file
        has_weights = any((model_dir / f).exists() for f in optional_files)
        if not has_weights:
            logger.warning(f"⚠️ No model weight files found in {model_path}")
            # Don't fail here as some models might have different file structures

        logger.info(f"✅ Model files validated: {model_path}")
        return True
    
    async def load_model(
        self,
        model_name_or_path: str,
        backend: ModelBackend = None,
        force_download: bool = False,
        device: str = None
    ) -> ModelInfo:
        """Load a model with enhanced error handling, validation, and device selection"""

        # Auto-detect backend if not specified
        if backend is None:
            backend = self._detect_optimal_backend(model_name_or_path)

        # Handle device selection
        target_device = self._resolve_target_device(device, backend)

        log_model_operation("load_start", model_name_or_path, f"backend: {backend.value}, device: {target_device}")

        # Validate backend compatibility
        if not self._validate_backend_compatibility(backend):
            raise ModelException(f"Backend {backend.value} is not available on this system")

        # Unload current model first
        if self.current_model:
            log_model_operation("unload", self.current_model.name, "preparing for new model")
            await self.unload_model()

        # Determine and validate model path
        model_path, model_name = await self._resolve_model_path(
            model_name_or_path, force_download
        )

        # Validate model files
        if not self._validate_model_files(model_path):
            raise ModelException(f"Invalid model files in {model_path}")

        # Load model based on backend
        start_time = asyncio.get_event_loop().time()

        try:
            if backend == ModelBackend.TRANSFORMERS:
                await self._load_transformers_model(model_path, target_device)
            elif backend == ModelBackend.VLLM:
                await self._load_vllm_model(model_path, target_device)
            elif backend == ModelBackend.TENSORFLOW:
                await self._load_tensorflow_model(model_path, target_device)
            elif backend == ModelBackend.GGUF:
                await self._load_gguf_model(model_path, target_device)
            else:
                raise ModelException(f"Unsupported backend: {backend}")

            load_time = asyncio.get_event_loop().time() - start_time

            # Create model info
            context_length = getattr(self.tokenizer, 'model_max_length', 4096) if self.tokenizer else 4096
            self.current_model = ModelInfo(
                name=model_name,
                backend=backend,
                model_path=model_path,
                context_length=context_length,
                loaded=True
            )
            self.current_model.load_time = load_time

            # Log successful loading with details
            log_model_operation("load_complete", model_name,
                              f"backend: {backend.value}, context: {context_length}")
            log_performance("model_loading", load_time, f"model: {model_name}")

            return self.current_model

        except Exception as e:
            load_time = asyncio.get_event_loop().time() - start_time
            log_model_operation("load_failed", model_name_or_path, f"after {load_time:.2f}s: {str(e)}")

            # Clean up any partial loading
            await self.unload_model()

            # Try fallback models if this is the default model
            if model_name_or_path == settings.models.default_model and hasattr(settings.models, 'fallback_models'):
                logger.info("🔄 Attempting fallback models...")
                for fallback_model in settings.models.fallback_models:
                    try:
                        log_model_operation("fallback_attempt", fallback_model, "trying alternative")
                        return await self.load_model(fallback_model, backend, force_download)
                    except Exception as fallback_error:
                        logger.warning(f"⚠️ Fallback model {fallback_model} failed: {fallback_error}")
                        continue

            raise ModelException(f"Failed to load model {model_name_or_path}: {e}")

    def _detect_optimal_backend(self, model_name: str) -> ModelBackend:
        """Detect the optimal backend for a given model"""

        # Check model-specific optimizations
        if model_name in self.model_optimizations:
            return self.model_optimizations[model_name]["recommended_backend"]

        # Use default backend from settings
        default_backend = settings.models.default_backend.lower()
        if default_backend == "vllm":
            return ModelBackend.VLLM
        elif default_backend == "tensorflow":
            return ModelBackend.TENSORFLOW
        else:
            return ModelBackend.TRANSFORMERS

    def _get_model_optimizations(self, model_name: str) -> Dict[str, Any]:
        """Get model-specific optimizations"""
        return self.model_optimizations.get(model_name, {})

    async def _load_transformers_model(self, model_path: str, target_device: str = None):
        """Load model using Transformers backend with device selection"""

        logger.info(f"Loading model with Transformers backend on device: {target_device or 'auto'}")

        # Get model name for optimizations
        model_name = Path(model_path).name if "/" not in model_path else model_path
        optimizations = self._get_model_optimizations(model_name)

        # Use target device if specified, otherwise fall back to settings/auto-detection
        if target_device:
            device = target_device
        else:
            device = settings.models.transformers_device
            if device == "auto":
                device = self.device

        torch_dtype = settings.models.transformers_torch_dtype
        if torch_dtype == "auto":
            torch_dtype = torch.float16 if device != "cpu" else torch.float32
        else:
            torch_dtype = getattr(torch, torch_dtype)

        # Configure quantization with model-specific optimizations
        quantization_config = None
        use_4bit = optimizations.get("load_in_4bit", settings.models.transformers_load_in_4bit)
        use_8bit = settings.models.transformers_load_in_8bit

        if use_4bit:
            from transformers import BitsAndBytesConfig
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch_dtype,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
            logger.info("Using 4-bit quantization (optimized for Jan-nano-128k)")
        elif use_8bit:
            from transformers import BitsAndBytesConfig
            quantization_config = BitsAndBytesConfig(load_in_8bit=True)
            logger.info("Using 8-bit quantization")

        # Load tokenizer with model-specific settings
        logger.info("Loading tokenizer...")
        trust_remote_code = optimizations.get("trust_remote_code", settings.models.transformers_trust_remote_code)
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=trust_remote_code,
            padding_side="left"
        )

        # Add pad token if missing
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token

        # Load model with optimizations
        logger.info("Loading model...")
        low_cpu_mem_usage = settings.models.transformers_low_cpu_mem_usage

        # Model-specific optimizations for Jan-nano-128k
        model_kwargs = {
            "torch_dtype": torch_dtype,
            "device_map": "auto" if device != "cpu" else None,
            "quantization_config": quantization_config,
            "trust_remote_code": trust_remote_code,
            "low_cpu_mem_usage": low_cpu_mem_usage
        }

        # Add flash attention if supported and enabled
        if optimizations.get("use_flash_attention", settings.models.enable_flash_attention):
            try:
                model_kwargs["attn_implementation"] = "flash_attention_2"
                logger.info("Using Flash Attention 2 for improved performance")
            except Exception as e:
                logger.warning(f"Flash Attention not available: {e}")

        self.model = AutoModelForCausalLM.from_pretrained(
            model_path,
            **model_kwargs
        )

        # Move to device if not using device_map
        if device == "cpu":
            self.model = self.model.to(device)

        logger.info(f"Model loaded on device: {device}")

    async def _load_vllm_model(self, model_path: str, target_device: str = None):
        """Load model using vLLM backend with device selection"""

        try:
            from vllm import LLM, SamplingParams
            from vllm.engine.arg_utils import AsyncEngineArgs
            from vllm.engine.async_llm_engine import AsyncLLMEngine
        except ImportError:
            raise ModelException("vLLM is not installed. Please install it with: pip install vllm")

        # vLLM device validation
        if target_device and target_device == "cpu":
            logger.warning("⚠️ vLLM on CPU is not officially supported and may have poor performance")
        elif target_device and target_device not in ["cuda", "auto", None]:
            logger.warning(f"⚠️ vLLM may not support device '{target_device}', using default GPU configuration")

        logger.info(f"Loading model with vLLM backend on device: {target_device or 'auto (GPU preferred)'}")

        # Get model name for optimizations
        model_name = Path(model_path).name if "/" not in model_path else model_path
        optimizations = self._get_model_optimizations(model_name)

        # Configure vLLM arguments with model-specific optimizations
        max_model_len = optimizations.get("max_context", settings.models.vllm_max_model_len)
        trust_remote_code = optimizations.get("trust_remote_code", True)

        engine_args = AsyncEngineArgs(
            model=model_path,
            tensor_parallel_size=settings.models.vllm_tensor_parallel_size,
            gpu_memory_utilization=settings.models.vllm_gpu_memory_utilization,
            max_model_len=max_model_len,
            trust_remote_code=trust_remote_code,
            dtype="auto",
            enforce_eager=settings.models.vllm_enforce_eager,
            disable_custom_all_reduce=settings.models.vllm_disable_custom_all_reduce
        )

        # Add model-specific optimizations for Jan-nano-128k
        if model_name == "Menlo/Jan-nano-128k":
            logger.info("Applying Jan-nano-128k specific optimizations for vLLM")
            engine_args.max_model_len = min(max_model_len, 128000)  # Respect model's context limit

        # Create async engine
        self.vllm_engine = AsyncLLMEngine.from_engine_args(engine_args)

        # Load tokenizer separately for compatibility
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_path,
            trust_remote_code=trust_remote_code
        )

        logger.info("vLLM model loaded successfully")

    async def _load_tensorflow_model(self, model_path: str, target_device: str = None):
        """Load model using TensorFlow backend with GPU/CPU fallback"""

        if not TENSORFLOW_AVAILABLE:
            raise ModelException("TensorFlow is not installed")

        logger.info(f"Loading model with TensorFlow backend")

        # Configure TensorFlow device strategy
        try:
            # List available devices
            physical_devices = tf.config.list_physical_devices()
            gpu_devices = tf.config.list_physical_devices('GPU')

            logger.info(f"Available TensorFlow devices: {[device.name for device in physical_devices]}")

            # Configure GPU if available and requested
            if target_device in ["gpu", "cuda"] or (target_device is None and gpu_devices):
                if gpu_devices:
                    try:
                        # Configure GPU memory growth to avoid allocation issues
                        for gpu in gpu_devices:
                            tf.config.experimental.set_memory_growth(gpu, True)

                        # Set GPU as preferred device
                        device_name = "/GPU:0"
                        logger.info(f"🎯 Using TensorFlow GPU device: {gpu_devices[0].name}")
                    except Exception as e:
                        logger.warning(f"⚠️ GPU configuration failed, falling back to CPU: {e}")
                        device_name = "/CPU:0"
                else:
                    logger.warning("⚠️ GPU requested but not available, using CPU")
                    device_name = "/CPU:0"
            else:
                device_name = "/CPU:0"
                logger.info("🎯 Using TensorFlow CPU device")

            # Load model with TensorFlow
            with tf.device(device_name):
                try:
                    # Try to load as SavedModel format first
                    if (Path(model_path) / "saved_model.pb").exists():
                        self.tf_model = tf.saved_model.load(model_path)
                        logger.info("✅ Loaded model in SavedModel format")
                    else:
                        # Try to load using transformers with TensorFlow backend
                        from transformers import TFAutoModelForCausalLM, AutoTokenizer

                        self.tf_tokenizer = AutoTokenizer.from_pretrained(
                            model_path,
                            trust_remote_code=True,
                            use_fast=True
                        )

                        self.tf_model = TFAutoModelForCausalLM.from_pretrained(
                            model_path,
                            trust_remote_code=True,
                            from_tf=True
                        )

                        # Also set the regular tokenizer for compatibility
                        self.tokenizer = self.tf_tokenizer

                        logger.info("✅ Loaded model using TensorFlow transformers")

                except Exception as e:
                    if "GPU" in device_name:
                        logger.warning(f"⚠️ GPU loading failed, trying CPU: {e}")
                        # Fallback to CPU
                        with tf.device("/CPU:0"):
                            if (Path(model_path) / "saved_model.pb").exists():
                                self.tf_model = tf.saved_model.load(model_path)
                            else:
                                from transformers import TFAutoModelForCausalLM, AutoTokenizer

                                self.tf_tokenizer = AutoTokenizer.from_pretrained(
                                    model_path,
                                    trust_remote_code=True,
                                    use_fast=True
                                )

                                self.tf_model = TFAutoModelForCausalLM.from_pretrained(
                                    model_path,
                                    trust_remote_code=True,
                                    from_tf=True
                                )

                                self.tokenizer = self.tf_tokenizer

                        logger.info("✅ Model loaded on CPU after GPU fallback")
                    else:
                        raise e

            logger.info("TensorFlow model loaded successfully")

        except Exception as e:
            logger.error(f"❌ TensorFlow model loading failed: {e}")
            raise ModelException(f"Failed to load TensorFlow model: {e}")

    async def _load_gguf_model(self, model_path: str, target_device: str = None):
        """Load model using GGUF/llama-cpp-python backend"""

        if not LLAMA_CPP_AVAILABLE:
            raise ModelException("llama-cpp-python is not installed")

        logger.info(f"Loading GGUF model with llama-cpp-python backend")

        try:
            # Get model optimizations
            model_name = Path(model_path).name
            optimizations = self._get_model_optimizations(model_name)
            
            # Handle GGUF file path resolution
            gguf_file_path = None
            
            # Check if model_path is already a .gguf file
            if model_path.endswith('.gguf'):
                gguf_file_path = model_path
            else:
                # Look for GGUF file in model directory
                model_dir = Path(model_path)
                
                # Check for specific GGUF file from optimizations
                if 'gguf_file' in optimizations:
                    specific_gguf = model_dir / optimizations['gguf_file']
                    if specific_gguf.exists():
                        gguf_file_path = str(specific_gguf)
                
                # If not found, look for any .gguf file in directory
                if not gguf_file_path:
                    gguf_files = list(model_dir.glob('*.gguf'))
                    if gguf_files:
                        gguf_file_path = str(gguf_files[0])
                        logger.info(f"Found GGUF file: {gguf_file_path}")
                    
            if not gguf_file_path or not Path(gguf_file_path).exists():
                raise ModelException(f"No GGUF file found in {model_path}")

            # Configure GGUF model parameters
            model_kwargs = {
                'model_path': gguf_file_path,
                'n_ctx': optimizations.get('n_ctx', 4096),
                'verbose': False,
            }

            # GPU configuration
            if target_device in ["gpu", "cuda"] and torch.cuda.is_available():
                model_kwargs['n_gpu_layers'] = optimizations.get('n_gpu_layers', -1)
                logger.info(f"🎯 Using GPU with {model_kwargs['n_gpu_layers']} layers")
            else:
                model_kwargs['n_gpu_layers'] = 0
                logger.info("🎯 Using CPU only")

            # Chat format configuration
            if 'chat_format' in optimizations:
                model_kwargs['chat_format'] = optimizations['chat_format']

            # Additional parameters
            if 'seed' in optimizations:
                model_kwargs['seed'] = optimizations['seed']

            logger.info(f"Loading GGUF model with parameters: {model_kwargs}")

            # Load the GGUF model
            self.gguf_model = Llama(**model_kwargs)

            # Set a dummy tokenizer for compatibility
            class GGUFTokenizer:
                def __init__(self, model):
                    self.model = model
                    self.model_max_length = model_kwargs.get('n_ctx', 4096)
                    self.pad_token_id = 0
                    self.eos_token_id = 2
                
                def decode(self, tokens, **kwargs):
                    return self.model.detokenize(tokens).decode('utf-8', errors='ignore')
                
                def encode(self, text, **kwargs):
                    return self.model.tokenize(text.encode('utf-8'))

            self.tokenizer = GGUFTokenizer(self.gguf_model)

            logger.info("✅ GGUF model loaded successfully")

        except Exception as e:
            logger.error(f"❌ GGUF model loading failed: {e}")
            raise ModelException(f"Failed to load GGUF model: {e}")

    async def cleanup(self):
        """Cleanup resources"""
        await self.unload_model()
        logger.info("ModelManager cleanup complete")

    async def unload_model(self):
        """Unload the current model with proper cleanup"""
        if not self.current_model:
            return

        model_name = self.current_model.name
        backend = self.current_model.backend

        log_model_operation("unload_start", model_name, f"backend: {backend.value}")

        try:
            # Backend-specific cleanup
            if backend == ModelBackend.VLLM and self.vllm_engine:
                # vLLM specific cleanup
                try:
                    # vLLM engines may have specific cleanup methods
                    if hasattr(self.vllm_engine, 'cleanup'):
                        await self.vllm_engine.cleanup()
                except Exception as e:
                    logger.warning(f"⚠️ vLLM cleanup warning: {e}")

            elif backend == ModelBackend.TRANSFORMERS and self.model:
                # Transformers specific cleanup
                try:
                    # Move model to CPU before deletion if on GPU
                    if hasattr(self.model, 'cpu'):
                        self.model.cpu()
                except Exception as e:
                    logger.warning(f"⚠️ Model CPU transfer warning: {e}")

            elif backend == ModelBackend.TENSORFLOW and self.tf_model:
                # TensorFlow specific cleanup
                try:
                    # Clear TensorFlow session and memory
                    if TENSORFLOW_AVAILABLE:
                        tf.keras.backend.clear_session()
                except Exception as e:
                    logger.warning(f"⚠️ TensorFlow cleanup warning: {e}")

            elif backend == ModelBackend.GGUF and self.gguf_model:
                # GGUF specific cleanup
                try:
                    # llama-cpp-python models can be cleaned up by deleting the reference
                    self.gguf_model = None
                    logger.info("✅ GGUF model cleaned up")
                except Exception as e:
                    logger.warning(f"⚠️ GGUF cleanup warning: {e}")

            # Clear all model references
            self.model = None
            self.tokenizer = None
            self.vllm_engine = None
            self.tf_model = None
            self.tf_tokenizer = None
            self.gguf_model = None

            # Clear model info
            self.current_model = None

            # Force garbage collection and GPU memory cleanup
            import gc
            gc.collect()

            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

                # Log GPU memory status
                if torch.cuda.is_available():
                    memory_allocated = torch.cuda.memory_allocated() / (1024**3)
                    memory_cached = torch.cuda.memory_reserved() / (1024**3)
                    logger.info(f"🔧 GPU Memory: {memory_allocated:.1f}GB allocated, {memory_cached:.1f}GB cached")

            log_model_operation("unload_complete", model_name, "cleanup successful")

        except Exception as e:
            logger.error(f"❌ Error during model unload: {e}")
            # Force clear references even if cleanup failed
            self.model = None
            self.tokenizer = None
            self.vllm_engine = None
            self.current_model = None
            raise ModelException(f"Failed to unload model: {e}")

    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = None,
        temperature: float = None,
        top_p: float = None,
        stream: bool = False
    ) -> AsyncGenerator[str, None]:
        """Generate text using the loaded model"""

        if not self.is_model_loaded():
            raise ModelException("No model is currently loaded")

        # Use default values from settings if not provided
        max_tokens = max_tokens or settings.models.max_tokens
        temperature = temperature or settings.models.temperature
        top_p = top_p or settings.models.top_p

        if self.current_model.backend == ModelBackend.TRANSFORMERS:
            async for chunk in self._generate_transformers(prompt, max_tokens, temperature, top_p, stream):
                yield chunk
        elif self.current_model.backend == ModelBackend.VLLM:
            async for chunk in self._generate_vllm(prompt, max_tokens, temperature, top_p, stream):
                yield chunk
        elif self.current_model.backend == ModelBackend.TENSORFLOW:
            async for chunk in self._generate_tensorflow(prompt, max_tokens, temperature, top_p, stream):
                yield chunk
        elif self.current_model.backend == ModelBackend.GGUF:
            async for chunk in self._generate_gguf(prompt, max_tokens, temperature, top_p, stream):
                yield chunk

    async def _generate_transformers(
        self,
        prompt: str,
        max_tokens: int,
        temperature: float,
        top_p: float,
        stream: bool
    ) -> AsyncGenerator[str, None]:
        """Generate text using Transformers backend"""

        # Tokenize input
        inputs = self.tokenizer(prompt, return_tensors="pt", padding=True)
        inputs = {k: v.to(self.model.device) for k, v in inputs.items()}

        # Generation parameters
        generation_kwargs = {
            "max_new_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "do_sample": True,
            "pad_token_id": self.tokenizer.pad_token_id,
            "eos_token_id": self.tokenizer.eos_token_id,
        }

        if stream:
            # Streaming generation
            streamer = TextIteratorStreamer(
                self.tokenizer,
                skip_prompt=True,
                skip_special_tokens=True
            )
            generation_kwargs["streamer"] = streamer

            # Start generation in a separate thread
            def generate():
                with torch.no_grad():
                    self.model.generate(**inputs, **generation_kwargs)

            thread = Thread(target=generate)
            thread.start()

            # Yield tokens as they are generated
            for token in streamer:
                yield token

            thread.join()
        else:
            # Non-streaming generation
            with torch.no_grad():
                outputs = self.model.generate(**inputs, **generation_kwargs)

            # Decode the generated text
            generated_text = self.tokenizer.decode(
                outputs[0][inputs["input_ids"].shape[1]:],
                skip_special_tokens=True
            )
            yield generated_text

    async def _generate_vllm(
        self,
        prompt: str,
        max_tokens: int,
        temperature: float,
        top_p: float,
        stream: bool
    ) -> AsyncGenerator[str, None]:
        """Generate text using vLLM backend"""

        from vllm import SamplingParams

        # Configure sampling parameters
        sampling_params = SamplingParams(
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=top_p,
        )

        if stream:
            # Streaming generation
            request_id = f"request_{asyncio.get_event_loop().time()}"

            # Start generation
            results_generator = self.vllm_engine.generate(
                prompt,
                sampling_params,
                request_id
            )

            # Yield tokens as they are generated
            async for request_output in results_generator:
                if request_output.outputs:
                    output = request_output.outputs[0]
                    yield output.text
        else:
            # Non-streaming generation
            request_id = f"request_{asyncio.get_event_loop().time()}"

            results_generator = self.vllm_engine.generate(
                prompt,
                sampling_params,
                request_id
            )

            # Get the final result
            final_output = None
            async for request_output in results_generator:
                final_output = request_output

            if final_output and final_output.outputs:
                yield final_output.outputs[0].text

    async def _generate_tensorflow(
        self,
        prompt: str,
        max_tokens: int,
        temperature: float,
        top_p: float,
        stream: bool
    ) -> AsyncGenerator[str, None]:
        """Generate text using TensorFlow backend"""

        if not TENSORFLOW_AVAILABLE or not self.tf_model:
            raise ModelException("TensorFlow model not loaded")

        try:
            # Use the tokenizer (either tf_tokenizer or regular tokenizer)
            tokenizer = self.tf_tokenizer or self.tokenizer
            if not tokenizer:
                raise ModelException("No tokenizer available for TensorFlow model")

            # Tokenize input
            inputs = tokenizer(prompt, return_tensors="tf", padding=True, truncation=True)
            input_ids = inputs["input_ids"]

            if stream:
                # Streaming generation for TensorFlow
                current_length = input_ids.shape[1]
                max_length = min(current_length + max_tokens, tokenizer.model_max_length or 2048)

                for _ in range(max_length - current_length):
                    # Generate next token
                    with tf.device(self.tf_model.device if hasattr(self.tf_model, 'device') else '/CPU:0'):
                        outputs = self.tf_model(input_ids)

                        # Get logits for the last token
                        logits = outputs.logits[:, -1, :] / temperature

                        # Apply top-p sampling
                        if top_p < 1.0:
                            sorted_logits = tf.sort(logits, direction='DESCENDING')
                            sorted_indices = tf.argsort(logits, direction='DESCENDING')
                            cumulative_probs = tf.cumsum(tf.nn.softmax(sorted_logits), axis=-1)

                            # Remove tokens with cumulative probability above the threshold
                            sorted_indices_to_remove = cumulative_probs > top_p
                            # Keep at least one token
                            sorted_indices_to_remove = tf.concat([
                                tf.zeros_like(sorted_indices_to_remove[:, :1]),
                                sorted_indices_to_remove[:, :-1]
                            ], axis=-1)

                            # Set logits to -inf for removed tokens
                            logits = tf.where(
                                tf.gather(sorted_indices_to_remove, tf.argsort(sorted_indices), batch_dims=1),
                                tf.fill(tf.shape(logits), float('-inf')),
                                logits
                            )

                        # Sample next token
                        next_token = tf.random.categorical(logits, 1)

                        # Decode token
                        next_token_text = tokenizer.decode(next_token[0], skip_special_tokens=True)

                        # Check for end of sequence
                        if tokenizer.eos_token_id and next_token[0, 0] == tokenizer.eos_token_id:
                            break

                        # Append to input_ids for next iteration
                        input_ids = tf.concat([input_ids, next_token], axis=-1)

                        # Yield the new token
                        yield next_token_text
            else:
                # Non-streaming generation
                with tf.device(self.tf_model.device if hasattr(self.tf_model, 'device') else '/CPU:0'):
                    # For non-streaming, generate all tokens at once if possible
                    max_length = min(input_ids.shape[1] + max_tokens, tokenizer.model_max_length or 2048)

                    # Simple greedy generation (can be enhanced with more sophisticated methods)
                    generated_ids = input_ids

                    for _ in range(max_tokens):
                        outputs = self.tf_model(generated_ids)
                        logits = outputs.logits[:, -1, :] / temperature

                        # Apply top-p sampling
                        if top_p < 1.0:
                            sorted_logits = tf.sort(logits, direction='DESCENDING')
                            sorted_indices = tf.argsort(logits, direction='DESCENDING')
                            cumulative_probs = tf.cumsum(tf.nn.softmax(sorted_logits), axis=-1)

                            sorted_indices_to_remove = cumulative_probs > top_p
                            sorted_indices_to_remove = tf.concat([
                                tf.zeros_like(sorted_indices_to_remove[:, :1]),
                                sorted_indices_to_remove[:, :-1]
                            ], axis=-1)

                            logits = tf.where(
                                tf.gather(sorted_indices_to_remove, tf.argsort(sorted_indices), batch_dims=1),
                                tf.fill(tf.shape(logits), float('-inf')),
                                logits
                            )

                        next_token = tf.random.categorical(logits, 1)

                        if tokenizer.eos_token_id and next_token[0, 0] == tokenizer.eos_token_id:
                            break

                        generated_ids = tf.concat([generated_ids, next_token], axis=-1)

                    # Decode the generated text
                    generated_text = tokenizer.decode(
                        generated_ids[0][input_ids.shape[1]:],
                        skip_special_tokens=True
                    )
                    yield generated_text

        except Exception as e:
            logger.error(f"TensorFlow generation error: {e}")
            yield f"Error: {str(e)}"

    async def _generate_gguf(
        self,
        prompt: str,
        max_tokens: int,
        temperature: float,
        top_p: float,
        stream: bool
    ) -> AsyncGenerator[str, None]:
        """Generate text using GGUF/llama-cpp-python backend"""
        
        if not self.gguf_model:
            raise ModelException("GGUF model not loaded")
        
        try:
            # Prepare generation parameters
            generation_kwargs = {
                'max_tokens': max_tokens,
                'temperature': temperature,
                'top_p': top_p,
                'stream': stream,
                'stop': ['</s>', '<|im_end|>', '<|endoftext|>'],  # Common stop sequences
                'echo': False,  # Don't repeat the prompt
            }
            
            logger.info(f"Generating GGUF text with params: {generation_kwargs}")
            
            if stream:
                # Streaming generation
                try:
                    response = self.gguf_model(
                        prompt,
                        **generation_kwargs
                    )
                    
                    # Process streaming response
                    for chunk in response:
                        if 'choices' in chunk and len(chunk['choices']) > 0:
                            choice = chunk['choices'][0]
                            if 'text' in choice:
                                text = choice['text']
                                if text:  # Only yield non-empty text
                                    yield text
                            elif 'delta' in choice and 'content' in choice['delta']:
                                # Handle chat completion format
                                text = choice['delta']['content']
                                if text:
                                    yield text
                                    
                            # Check for finish reason
                            if choice.get('finish_reason') in ['stop', 'length']:
                                break
                                
                except Exception as stream_error:
                    logger.error(f"GGUF streaming error: {stream_error}")
                    # Fallback to non-streaming
                    generation_kwargs['stream'] = False
                    response = self.gguf_model(prompt, **generation_kwargs)
                    
                    if 'choices' in response and len(response['choices']) > 0:
                        text = response['choices'][0].get('text', '')
                        if text:
                            yield text
                    else:
                        yield "Error in GGUF text generation"
                        
            else:
                # Non-streaming generation
                response = self.gguf_model(prompt, **generation_kwargs)
                
                if 'choices' in response and len(response['choices']) > 0:
                    text = response['choices'][0].get('text', '')
                    if text:
                        yield text
                else:
                    yield "Error in GGUF text generation"
                    
        except Exception as e:
            logger.error(f"GGUF generation error: {e}")
            yield f"Error: {str(e)}"

    async def _download_with_git(self, model_url: str, model_path: Path, model_name: str) -> bool:
        """Download model using Git clone with enhanced error handling"""
        try:
            # Check if Git is available
            try:
                import git
                logger.info("✅ Git library available")
            except ImportError:
                logger.warning("❌ GitPython not available")
                return False

            # Check if git command is available
            try:
                import subprocess
                result = subprocess.run(['git', '--version'], capture_output=True, text=True, timeout=5)
                if result.returncode != 0:
                    logger.warning("❌ Git command not available")
                    return False
                logger.info(f"✅ Git command available: {result.stdout.strip()}")
            except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
                logger.warning("❌ Git command not found in PATH")
                return False

            # Enhanced Git clone with beautiful progress tracking
            last_percentage = -1
            operation_type = "Initializing"

            def clone_progress(op_code, cur_count, max_count=None, message=''):
                nonlocal last_percentage, operation_type

                if max_count and max_count > 0:
                    percentage = int((cur_count / max_count) * 100)

                    # Determine operation type
                    if op_code & git.RemoteProgress.COUNTING:
                        operation_type = "📊 Counting objects"
                    elif op_code & git.RemoteProgress.COMPRESSING:
                        operation_type = "🗜️ Compressing objects"
                    elif op_code & git.RemoteProgress.RECEIVING:
                        operation_type = "📥 Receiving objects"
                    elif op_code & git.RemoteProgress.RESOLVING:
                        operation_type = "🔗 Resolving deltas"
                    else:
                        operation_type = "🔄 Processing"

                    # Only log significant progress changes (every 20%)
                    if percentage != last_percentage and percentage % 20 == 0:
                        last_percentage = percentage
                        logger.info(f"   {operation_type}: {percentage}% ({cur_count:,}/{max_count:,})")
                elif cur_count > 0:
                    # For operations without max_count
                    if cur_count % 1000 == 0:  # Log every 1000 items
                        logger.info(f"   {operation_type}: {cur_count:,} items processed")

            log_git_operation("clone", f"Starting Git download of {model_name}")
            logger.info(f"📍 Source: {model_url}")
            logger.info(f"📂 Destination: {model_path}")

            # Configure Git environment for better compatibility
            env = os.environ.copy()
            env['GIT_LFS_SKIP_SMUDGE'] = '1'  # Skip LFS files initially
            env['GIT_TERMINAL_PROMPT'] = '0'  # Disable interactive prompts

            # Create parent directory
            model_path.parent.mkdir(parents=True, exist_ok=True)

            # Use git to clone the model repository
            try:
                repo = git.Repo.clone_from(
                    model_url,
                    str(model_path),
                    progress=clone_progress,
                    depth=1,  # Shallow clone for faster download
                    single_branch=True,  # Only clone the default branch
                    env=env
                )

                logger.info("✅ Git clone completed successfully")
                return True

            except git.exc.GitCommandError as git_error:
                logger.error(f"Git command error: {git_error}")
                if "Authentication failed" in str(git_error):
                    logger.warning("⚠️ Authentication required for this repository")
                elif "not found" in str(git_error):
                    logger.warning("⚠️ Repository not found or access denied")
                return False

        except Exception as e:
            logger.error(f"Git download failed: {e}")
            # Clean up partial download
            if model_path.exists():
                import shutil
                try:
                    shutil.rmtree(model_path)
                except:
                    pass  # Ignore cleanup errors
            return False

    async def _download_with_huggingface_hub(self, model_url: str, model_path: Path, model_name: str) -> bool:
        """Download model using HuggingFace Hub library"""
        try:
            # Try to import huggingface_hub
            try:
                from huggingface_hub import snapshot_download
                logger.info("✅ HuggingFace Hub library available")
            except ImportError:
                logger.info("📦 Installing HuggingFace Hub library...")
                import subprocess
                import sys
                try:
                    subprocess.check_call([
                        sys.executable, "-m", "pip", "install",
                        "huggingface_hub", "--quiet", "--no-warn-script-location"
                    ])
                    from huggingface_hub import snapshot_download
                    logger.info("✅ HuggingFace Hub library installed successfully")
                except Exception as install_error:
                    logger.error(f"Failed to install HuggingFace Hub: {install_error}")
                    return False

            # Extract model name from URL
            if model_url.startswith('https://huggingface.co/'):
                repo_id = model_url.replace('https://huggingface.co/', '')
            else:
                repo_id = model_name.replace('_', '/')  # Convert back from safe filename

            logger.info(f"🤗 Starting HuggingFace Hub download")
            logger.info(f"📍 Repository: {repo_id}")
            logger.info(f"📂 Destination: {model_path}")

            # Create parent directory if it doesn't exist
            model_path.parent.mkdir(parents=True, exist_ok=True)

            # Download using HuggingFace Hub with progress
            try:
                downloaded_path = snapshot_download(
                    repo_id=repo_id,
                    local_dir=str(model_path),
                    local_dir_use_symlinks=False,
                    resume_download=True,
                    ignore_patterns=["*.git*", "README.md", "*.md"]  # Skip unnecessary files
                )

                logger.info("✅ HuggingFace Hub download completed successfully")
                logger.info(f"📂 Downloaded to: {downloaded_path}")
                return True

            except Exception as download_error:
                logger.error(f"Download error: {download_error}")
                # Try alternative approach for private or gated models
                if "401" in str(download_error) or "403" in str(download_error):
                    logger.warning("⚠️ Model may require authentication or be private")
                    logger.info("💡 Try: huggingface-cli login")
                return False

        except Exception as e:
            logger.error(f"HuggingFace Hub download failed: {e}")
            # Clean up partial download
            if model_path.exists():
                import shutil
                try:
                    shutil.rmtree(model_path)
                except:
                    pass  # Ignore cleanup errors
            return False

    async def _download_with_http(self, model_url: str, model_path: Path, model_name: str) -> bool:
        """Download model using direct HTTP requests (fallback method)"""
        try:
            import aiohttp
            import aiofiles

            logger.info(f"🌐 Starting HTTP download of {model_name}")
            logger.info(f"📂 Destination: {model_path}")

            # Create model directory
            model_path.mkdir(parents=True, exist_ok=True)

            # For HTTP download, we'll try to get the model files directly
            # This is a simplified implementation - in practice, you'd need to
            # know the specific file structure of the model

            async with aiohttp.ClientSession() as session:
                # Try to download common model files
                common_files = [
                    'config.json',
                    'tokenizer.json',
                    'tokenizer_config.json',
                    'pytorch_model.bin',
                    'model.safetensors'
                ]

                downloaded_any = False

                for filename in common_files:
                    file_url = f"{model_url}/resolve/main/{filename}"
                    try:
                        async with session.get(file_url) as response:
                            if response.status == 200:
                                file_path = model_path / filename
                                async with aiofiles.open(file_path, 'wb') as f:
                                    async for chunk in response.content.iter_chunked(8192):
                                        await f.write(chunk)
                                logger.info(f"   ✅ Downloaded {filename}")
                                downloaded_any = True
                            else:
                                logger.debug(f"   ⏭️ Skipped {filename} (not found)")
                    except Exception as e:
                        logger.debug(f"   ❌ Failed to download {filename}: {e}")

                if downloaded_any:
                    logger.info("✅ HTTP download completed successfully")
                    return True
                else:
                    logger.error("❌ No files could be downloaded via HTTP")
                    return False

        except Exception as e:
            logger.error(f"HTTP download failed: {e}")
            # Clean up partial download
            if model_path.exists():
                import shutil
                shutil.rmtree(model_path)
            return False

    async def _download_single_gguf_file(self, model_url: str, target_file: Path, filename: str) -> str:
        """Download single GGUF file with progress tracking"""
        try:
            import aiohttp
            import aiofiles
            from rich.console import Console
            from rich.progress import Progress, TextColumn, BarColumn, DownloadColumn, TimeRemainingColumn, SpinnerColumn
            
            console = Console()
            
            # Create target directory if it doesn't exist
            target_file.parent.mkdir(parents=True, exist_ok=True)
            
            logger.info(f"🌐 Starting direct GGUF file download")
            logger.info(f"📂 Source: {model_url}")
            logger.info(f"📍 Target: {target_file}")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(model_url) as response:
                    if response.status == 200:
                        total_size = int(response.headers.get('content-length', 0))
                        
                        # Create progress bar
                        with Progress(
                            SpinnerColumn(),
                            TextColumn("[bold blue]Downloading GGUF...", justify="right"),
                            BarColumn(bar_width=40),
                            "[progress.percentage]{task.percentage:>3.1f}%",
                            "•",
                            DownloadColumn(),
                            "•",
                            TimeRemainingColumn(),
                            console=console,
                            transient=True
                        ) as progress:
                            
                            task = progress.add_task("download", total=total_size)
                            
                            async with aiofiles.open(target_file, 'wb') as f:
                                downloaded = 0
                                async for chunk in response.content.iter_chunked(8192):
                                    await f.write(chunk)
                                    downloaded += len(chunk)
                                    progress.update(task, advance=len(chunk))
                        
                        # Display completion message
                        file_size = target_file.stat().st_size
                        logger.info(f"✅ GGUF file downloaded successfully")
                        logger.info(f"📏 File size: {file_size / (1024**2):.1f}MB")
                        logger.info(f"💡 Use 'load {filename} gguf' to load the model")
                        
                        return str(target_file)
                        
                    else:
                        logger.error(f"❌ Download failed: HTTP {response.status}")
                        logger.error(f"Response: {response.reason}")
                        raise Exception(f"HTTP {response.status}: {response.reason}")
                        
        except Exception as e:
            logger.error(f"❌ Single GGUF file download failed: {e}")
            # Clean up partial download
            if target_file.exists():
                try:
                    target_file.unlink()
                except:
                    pass
            raise Exception(f"Failed to download GGUF file: {e}")
