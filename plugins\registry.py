"""
Plugin Registry for Reverie Code Studio
Manages plugin metadata, configuration, and state persistence
"""

import json
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Set
from datetime import datetime

from .base import PluginMetadata, PluginConfig, PluginStatus, PluginCategory
from .exceptions import PluginException, PluginNotFoundError, PluginConfigError
from core.logging import logger


class PluginRegistry:
    """
    Central registry for managing plugin metadata and configuration
    
    The registry maintains information about all available plugins,
    their configurations, and current status.
    """
    
    def __init__(self, registry_path: Path):
        self.registry_path = registry_path
        self.registry_file = registry_path / "plugins.json"
        self.config_dir = registry_path / "configs"
        
        # In-memory storage
        self._plugins: Dict[str, PluginMetadata] = {}
        self._configs: Dict[str, PluginConfig] = {}
        self._status: Dict[str, PluginStatus] = {}
        
        # Ensure directories exist
        self.registry_path.mkdir(parents=True, exist_ok=True)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Plugin registry initialized at: {registry_path}")
    
    async def initialize(self):
        """Initialize the registry by loading existing data"""
        await self._load_registry()
        await self._load_configs()
        logger.info(f"Plugin registry loaded with {len(self._plugins)} plugins")
    
    async def register_plugin(self, metadata: PluginMetadata, config: Optional[PluginConfig] = None):
        """
        Register a new plugin in the registry
        
        Args:
            metadata: Plugin metadata
            config: Plugin configuration (optional)
        """
        plugin_name = metadata.name
        
        # Store metadata
        self._plugins[plugin_name] = metadata
        self._status[plugin_name] = metadata.status
        
        # Store or create default config
        if config:
            self._configs[plugin_name] = config
        elif plugin_name not in self._configs:
            self._configs[plugin_name] = PluginConfig()
        
        # Persist changes
        await self._save_registry()
        await self._save_config(plugin_name)
        
        logger.info(f"Plugin registered: {plugin_name} v{metadata.version}")
    
    async def unregister_plugin(self, plugin_name: str):
        """
        Unregister a plugin from the registry
        
        Args:
            plugin_name: Name of the plugin to unregister
        """
        if plugin_name not in self._plugins:
            raise PluginNotFoundError(f"Plugin not found: {plugin_name}")
        
        # Remove from memory
        del self._plugins[plugin_name]
        del self._status[plugin_name]
        if plugin_name in self._configs:
            del self._configs[plugin_name]
        
        # Remove config file
        config_file = self.config_dir / f"{plugin_name}.json"
        if config_file.exists():
            config_file.unlink()
        
        # Persist changes
        await self._save_registry()
        
        logger.info(f"Plugin unregistered: {plugin_name}")
    
    def get_plugin_metadata(self, plugin_name: str) -> Optional[PluginMetadata]:
        """Get plugin metadata by name"""
        return self._plugins.get(plugin_name)
    
    def get_plugin_config(self, plugin_name: str) -> Optional[PluginConfig]:
        """Get plugin configuration by name"""
        return self._configs.get(plugin_name)
    
    def get_plugin_status(self, plugin_name: str) -> Optional[PluginStatus]:
        """Get plugin status by name"""
        return self._status.get(plugin_name)
    
    async def update_plugin_status(self, plugin_name: str, status: PluginStatus):
        """Update plugin status"""
        if plugin_name not in self._plugins:
            raise PluginNotFoundError(f"Plugin not found: {plugin_name}")
        
        self._status[plugin_name] = status
        self._plugins[plugin_name].status = status
        
        # Update loaded_at timestamp if becoming active
        if status == PluginStatus.ACTIVE:
            self._plugins[plugin_name].loaded_at = datetime.now()
        
        await self._save_registry()
    
    async def update_plugin_config(self, plugin_name: str, config: PluginConfig):
        """Update plugin configuration"""
        if plugin_name not in self._plugins:
            raise PluginNotFoundError(f"Plugin not found: {plugin_name}")
        
        self._configs[plugin_name] = config
        await self._save_config(plugin_name)
        
        logger.info(f"Plugin config updated: {plugin_name}")
    
    def list_plugins(self, 
                    category: Optional[PluginCategory] = None,
                    status: Optional[PluginStatus] = None,
                    enabled_only: bool = False) -> List[PluginMetadata]:
        """
        List plugins with optional filtering
        
        Args:
            category: Filter by plugin category
            status: Filter by plugin status
            enabled_only: Only return enabled plugins
            
        Returns:
            List of plugin metadata matching the criteria
        """
        plugins = []
        
        for name, metadata in self._plugins.items():
            # Apply filters
            if category and metadata.category != category:
                continue
            
            if status and self._status.get(name) != status:
                continue
            
            if enabled_only:
                config = self._configs.get(name)
                if not config or not config.enabled:
                    continue
            
            plugins.append(metadata)
        
        # Sort by priority (lower number = higher priority)
        plugins.sort(key=lambda p: self._configs.get(p.name, PluginConfig()).priority)
        
        return plugins
    
    def get_enabled_plugins(self) -> List[str]:
        """Get list of enabled plugin names"""
        enabled = []
        for name, config in self._configs.items():
            if config.enabled and name in self._plugins:
                enabled.append(name)
        return enabled
    
    def get_plugins_by_category(self, category: PluginCategory) -> List[str]:
        """Get plugin names by category"""
        return [
            name for name, metadata in self._plugins.items()
            if metadata.category == category
        ]
    
    def plugin_exists(self, plugin_name: str) -> bool:
        """Check if a plugin exists in the registry"""
        return plugin_name in self._plugins
    
    def get_plugin_dependencies(self, plugin_name: str) -> List[str]:
        """Get plugin dependencies"""
        metadata = self._plugins.get(plugin_name)
        return metadata.dependencies if metadata else []
    
    def get_dependent_plugins(self, plugin_name: str) -> List[str]:
        """Get plugins that depend on the given plugin"""
        dependents = []
        for name, metadata in self._plugins.items():
            if plugin_name in metadata.dependencies:
                dependents.append(name)
        return dependents
    
    async def _load_registry(self):
        """Load plugin registry from disk"""
        if not self.registry_file.exists():
            return
        
        try:
            with open(self.registry_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for plugin_data in data.get('plugins', []):
                metadata = PluginMetadata(
                    name=plugin_data['name'],
                    version=plugin_data['version'],
                    description=plugin_data['description'],
                    author=plugin_data['author'],
                    category=PluginCategory(plugin_data['category']),
                    homepage=plugin_data.get('homepage'),
                    repository=plugin_data.get('repository'),
                    license=plugin_data.get('license'),
                    keywords=plugin_data.get('keywords', []),
                    dependencies=plugin_data.get('dependencies', []),
                    python_requires=plugin_data.get('python_requires'),
                    api_version=plugin_data.get('api_version', '1.0.0'),
                    config_schema=plugin_data.get('config_schema'),
                    default_config=plugin_data.get('default_config', {}),
                    plugin_path=Path(plugin_data['plugin_path']) if plugin_data.get('plugin_path') else None,
                    loaded_at=datetime.fromisoformat(plugin_data['loaded_at']) if plugin_data.get('loaded_at') else None,
                    status=PluginStatus(plugin_data.get('status', 'unloaded'))
                )
                
                self._plugins[metadata.name] = metadata
                self._status[metadata.name] = metadata.status
                
        except Exception as e:
            logger.error(f"Failed to load plugin registry: {e}")
    
    async def _save_registry(self):
        """Save plugin registry to disk"""
        try:
            plugins_data = []
            for metadata in self._plugins.values():
                plugin_data = {
                    'name': metadata.name,
                    'version': metadata.version,
                    'description': metadata.description,
                    'author': metadata.author,
                    'category': metadata.category.value,
                    'homepage': metadata.homepage,
                    'repository': metadata.repository,
                    'license': metadata.license,
                    'keywords': metadata.keywords,
                    'dependencies': metadata.dependencies,
                    'python_requires': metadata.python_requires,
                    'api_version': metadata.api_version,
                    'config_schema': metadata.config_schema,
                    'default_config': metadata.default_config,
                    'plugin_path': str(metadata.plugin_path) if metadata.plugin_path else None,
                    'loaded_at': metadata.loaded_at.isoformat() if metadata.loaded_at else None,
                    'status': metadata.status.value
                }
                plugins_data.append(plugin_data)
            
            registry_data = {
                'version': '1.0.0',
                'updated_at': datetime.now().isoformat(),
                'plugins': plugins_data
            }
            
            with open(self.registry_file, 'w', encoding='utf-8') as f:
                json.dump(registry_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Failed to save plugin registry: {e}")
    
    async def _load_configs(self):
        """Load plugin configurations from disk"""
        for config_file in self.config_dir.glob("*.json"):
            plugin_name = config_file.stem
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                config = PluginConfig(
                    enabled=config_data.get('enabled', True),
                    config=config_data.get('config', {}),
                    priority=config_data.get('priority', 100),
                    auto_load=config_data.get('auto_load', True)
                )
                
                self._configs[plugin_name] = config
                
            except Exception as e:
                logger.error(f"Failed to load config for plugin {plugin_name}: {e}")
    
    async def _save_config(self, plugin_name: str):
        """Save plugin configuration to disk"""
        if plugin_name not in self._configs:
            return
        
        config = self._configs[plugin_name]
        config_file = self.config_dir / f"{plugin_name}.json"
        
        try:
            config_data = {
                'enabled': config.enabled,
                'config': config.config,
                'priority': config.priority,
                'auto_load': config.auto_load,
                'updated_at': datetime.now().isoformat()
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Failed to save config for plugin {plugin_name}: {e}")
