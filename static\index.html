<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rilance Code Studio - AI-Native IDE</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <h1 class="logo-text">Reverie</h1>
                <p class="logo-subtitle">AI-Native Code Studio</p>
            </div>
            <nav class="nav">
                <a href="#features" class="nav-link">Features</a>
                <a href="#api" class="nav-link">API</a>
                <a href="#docs" class="nav-link">Docs</a>
                <a href="/docs" class="nav-link primary">API Docs</a>
            </nav>
        </header>

        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <h1 class="hero-title">
                    The Future of
                    <span class="gradient-text">AI-Powered</span>
                    Development
                </h1>
                <p class="hero-description">
                    Rilance Code Studio brings together the power of modern AI with an intuitive IDE experience. 
                    Chat with your code, let AI agents handle complex tasks, and accelerate your development workflow.
                </p>
                <div class="hero-buttons">
                    <a href="#download" class="btn btn-primary">Download Client</a>
                    <a href="/docs" class="btn btn-secondary">View API Docs</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="code-preview">
                    <div class="code-header">
                        <div class="code-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="code-title">main.py</span>
                    </div>
                    <div class="code-content">
                        <pre><code><span class="keyword">def</span> <span class="function">fibonacci</span>(<span class="param">n</span>):
    <span class="comment"># AI-generated optimized version</span>
    <span class="keyword">if</span> n <= <span class="number">1</span>:
        <span class="keyword">return</span> n
    
    a, b = <span class="number">0</span>, <span class="number">1</span>
    <span class="keyword">for</span> _ <span class="keyword">in</span> <span class="function">range</span>(<span class="number">2</span>, n + <span class="number">1</span>):
        a, b = b, a + b
    <span class="keyword">return</span> b</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="features">
            <div class="section-header">
                <h2 class="section-title">Powerful AI Features</h2>
                <p class="section-description">Everything you need for AI-enhanced development</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🤖</div>
                    <h3 class="feature-title">Dual AI Modes</h3>
                    <p class="feature-description">
                        Switch between Chat mode for interactive assistance and Agent mode for autonomous task execution.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🧠</div>
                    <h3 class="feature-title">Context Awareness</h3>
                    <p class="feature-description">
                        AI understands your entire codebase, current file, and cursor position for relevant suggestions.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <h3 class="feature-title">Web Integration</h3>
                    <p class="feature-description">
                        Search the web for current information and best practices with @web commands.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3 class="feature-title">Multiple Providers</h3>
                    <p class="feature-description">
                        Support for OpenAI, Claude, Zhipu AI, and local models with easy switching.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🛠️</div>
                    <h3 class="feature-title">Tool Integration</h3>
                    <p class="feature-description">
                        File operations, command execution, Git integration, and project analysis tools.
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3 class="feature-title">Modern Interface</h3>
                    <p class="feature-description">
                        Beautiful, responsive interface inspired by Cursor with customizable themes.
                    </p>
                </div>
            </div>
        </section>

        <!-- API Section -->
        <section id="api" class="api-section">
            <div class="section-header">
                <h2 class="section-title">RESTful API</h2>
                <p class="section-description">Comprehensive API for all AI functionality</p>
            </div>
            
            <div class="api-grid">
                <div class="api-card">
                    <h3 class="api-title">Chat API</h3>
                    <p class="api-description">Interactive chat with AI models</p>
                    <div class="api-endpoint">
                        <span class="method post">POST</span>
                        <span class="url">/api/v1/chat/</span>
                    </div>
                </div>
                
                <div class="api-card">
                    <h3 class="api-title">Agent API</h3>
                    <p class="api-description">Autonomous task execution</p>
                    <div class="api-endpoint">
                        <span class="method post">POST</span>
                        <span class="url">/api/v1/agent/execute</span>
                    </div>
                </div>
                
                <div class="api-card">
                    <h3 class="api-title">Models API</h3>
                    <p class="api-description">Model management and loading</p>
                    <div class="api-endpoint">
                        <span class="method get">GET</span>
                        <span class="url">/api/v1/models/</span>
                    </div>
                </div>
                
                <div class="api-card">
                    <h3 class="api-title">Tools API</h3>
                    <p class="api-description">File operations and web search</p>
                    <div class="api-endpoint">
                        <span class="method post">POST</span>
                        <span class="url">/api/v1/tools/web_search</span>
                    </div>
                </div>
            </div>
            
            <div class="api-example">
                <h3>Example Request</h3>
                <pre><code class="language-json">{
  "messages": [
    {"role": "user", "content": "Explain this Python function"}
  ],
  "stream": true,
  "context": {
    "current_file": "main.py",
    "selected_text": "def fibonacci(n): ..."
  }
}</code></pre>
            </div>
        </section>

        <!-- Status Section -->
        <section class="status-section">
            <div class="status-card">
                <h3>Server Status</h3>
                <div class="status-item">
                    <span class="status-label">Status:</span>
                    <span class="status-value online" id="server-status">Online</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Model:</span>
                    <span class="status-value" id="current-model">Loading...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Uptime:</span>
                    <span class="status-value" id="uptime">Loading...</span>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Rilance Code Studio</h4>
                    <p>AI-Native development environment for the future of coding.</p>
                </div>
                <div class="footer-section">
                    <h4>Resources</h4>
                    <a href="/docs">API Documentation</a>
                    <a href="#download">Download Client</a>
                    <a href="/health">Health Check</a>
                </div>
                <div class="footer-section">
                    <h4>API Endpoints</h4>
                    <a href="/api/v1/models/">Models</a>
                    <a href="/api/v1/system/info">System Info</a>
                    <a href="/api/v1/system/status">Server Status</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Rilance Code Studio. Powered by Reverie AI.</p>
            </div>
        </footer>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
