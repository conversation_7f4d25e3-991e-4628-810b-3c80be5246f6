"""
Hello World Plugin Implementation
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Callable

from server.plugins.base import ToolPlugin


class HelloWorldPlugin(ToolPlugin):
    """
    A simple hello world plugin that demonstrates:
    - Basic plugin lifecycle
    - Configuration usage
    - Tool provision
    - Hook registration
    - Logging
    """
    
    async def initialize(self) -> bool:
        """Initialize the plugin"""
        self.logger.info("Initializing Hello World Plugin")
        
        # Validate configuration
        greeting = self.get_config_value("greeting", "Hello")
        if not isinstance(greeting, str):
            self.logger.error("Invalid greeting configuration - must be string")
            return False
        
        self.logger.info(f"Plugin initialized with greeting: '{greeting}'")
        return True
    
    async def activate(self) -> bool:
        """Activate the plugin"""
        self.logger.info("Activating Hello World Plugin")
        
        # Register for system events
        self.register_hook("file_saved", self.on_file_saved)
        self.register_hook("ai_request_started", self.on_ai_request)
        
        # Set up plugin context
        self.set_context("activation_time", datetime.now())
        self.set_context("greeting_count", 0)
        
        self.logger.info("Hello World Plugin activated successfully")
        return True
    
    async def deactivate(self) -> bool:
        """Deactivate the plugin"""
        self.logger.info("Deactivating Hello World Plugin")
        
        # Log statistics
        greeting_count = self.get_context("greeting_count", 0)
        activation_time = self.get_context("activation_time")
        
        if activation_time:
            duration = datetime.now() - activation_time
            self.logger.info(f"Plugin was active for {duration}, generated {greeting_count} greetings")
        
        return True
    
    async def cleanup(self) -> bool:
        """Clean up plugin resources"""
        self.logger.info("Cleaning up Hello World Plugin")
        return True
    
    # Tool Plugin Implementation
    def get_tools(self) -> Dict[str, Callable]:
        """Return tools provided by this plugin"""
        return {
            "hello_world": self.hello_world_tool,
            "greeting": self.greeting_tool,
            "plugin_info": self.plugin_info_tool
        }
    
    def get_tool_descriptions(self) -> Dict[str, str]:
        """Return tool descriptions"""
        return {
            "hello_world": "hello_world() - Generate a simple hello world message",
            "greeting": "greeting(name: str) - Generate a personalized greeting",
            "plugin_info": "plugin_info() - Get information about this plugin"
        }
    
    # Tool Implementations
    async def hello_world_tool(self) -> Dict[str, Any]:
        """Simple hello world tool"""
        greeting = self.get_config_value("greeting", "Hello")
        include_timestamp = self.get_config_value("include_timestamp", False)
        
        message = f"{greeting}, World!"
        
        if include_timestamp:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            message += f" (Generated at {timestamp})"
        
        # Update greeting count
        count = self.get_context("greeting_count", 0) + 1
        self.set_context("greeting_count", count)
        
        self.logger.info(f"Generated hello world message #{count}")
        
        return {
            "message": message,
            "greeting_count": count,
            "plugin": self.metadata.name
        }
    
    async def greeting_tool(self, name: str) -> Dict[str, Any]:
        """Personalized greeting tool"""
        if not name or not isinstance(name, str):
            return {
                "error": "Name parameter is required and must be a string"
            }
        
        greeting = self.get_config_value("greeting", "Hello")
        include_timestamp = self.get_config_value("include_timestamp", False)
        
        message = f"{greeting}, {name}!"
        
        if include_timestamp:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            message += f" (Generated at {timestamp})"
        
        # Update greeting count
        count = self.get_context("greeting_count", 0) + 1
        self.set_context("greeting_count", count)
        
        self.logger.info(f"Generated personalized greeting for '{name}' #{count}")
        
        return {
            "message": message,
            "name": name,
            "greeting_count": count,
            "plugin": self.metadata.name
        }
    
    async def plugin_info_tool(self) -> Dict[str, Any]:
        """Get plugin information"""
        activation_time = self.get_context("activation_time")
        greeting_count = self.get_context("greeting_count", 0)
        
        info = {
            "name": self.metadata.name,
            "version": self.metadata.version,
            "description": self.metadata.description,
            "author": self.metadata.author,
            "category": self.metadata.category.value,
            "status": self.status.value,
            "enabled": self.is_enabled(),
            "greeting_count": greeting_count,
            "config": {
                "greeting": self.get_config_value("greeting", "Hello"),
                "include_timestamp": self.get_config_value("include_timestamp", False)
            }
        }
        
        if activation_time:
            uptime = datetime.now() - activation_time
            info["uptime_seconds"] = uptime.total_seconds()
        
        return info
    
    # Event Handlers
    async def on_file_saved(self, file_path: str, content: str = None):
        """Handle file save events"""
        self.logger.info(f"File saved: {file_path}")
        
        # Could implement file-specific greetings or analysis here
        if file_path.endswith('.py'):
            self.logger.info("Python file saved - could analyze for greeting patterns")
    
    async def on_ai_request(self, request_type: str = None, context: Dict[str, Any] = None):
        """Handle AI request events"""
        self.logger.info(f"AI request started: {request_type}")
        
        # Could implement AI request enhancement or logging here
        if context and "greeting" in str(context).lower():
            self.logger.info("AI request involves greetings - plugin could assist")
    
    # Utility Methods
    def get_greeting_stats(self) -> Dict[str, Any]:
        """Get greeting statistics"""
        return {
            "total_greetings": self.get_context("greeting_count", 0),
            "activation_time": self.get_context("activation_time"),
            "current_greeting": self.get_config_value("greeting", "Hello")
        }
