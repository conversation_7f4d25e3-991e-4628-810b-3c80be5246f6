# Reverie Code Studio Plugin System

The Reverie Code Studio Plugin System provides a comprehensive framework for extending the IDE's functionality through custom plugins. This system allows developers to create powerful extensions that integrate seamlessly with the AI-powered coding environment.

## Table of Contents

1. [Overview](#overview)
2. [Plugin Architecture](#plugin-architecture)
3. [Getting Started](#getting-started)
4. [Plugin Types](#plugin-types)
5. [API Reference](#api-reference)
6. [Examples](#examples)
7. [Best Practices](#best-practices)
8. [Deployment](#deployment)

## Overview

The plugin system is built on a modular architecture that supports:

- **Dynamic Loading**: Plugins can be loaded and unloaded at runtime
- **Dependency Management**: Automatic resolution of plugin dependencies
- **Configuration Management**: Flexible configuration system for each plugin
- **Hook System**: Event-driven architecture for plugin communication
- **Multiple Plugin Types**: Support for various plugin categories
- **Hot Reloading**: Development-friendly plugin reloading

## Plugin Architecture

### Core Components

1. **BasePlugin**: Abstract base class that all plugins must inherit from
2. **PluginManager**: Central management system for plugin lifecycle
3. **PluginRegistry**: Metadata and configuration storage
4. **PluginLoader**: Dynamic loading and discovery mechanism

### Plugin Lifecycle

```
Discovered → Registered → Loaded → Activated → Active
                ↓           ↓         ↓
            Unregistered ← Unloaded ← Deactivated
```

### Plugin Categories

- **Code Analysis**: Static analysis, linting, formatting
- **File Operations**: File manipulation, search, organization
- **AI Integrations**: Custom AI models, providers, tools
- **Web Tools**: Web scraping, API integrations, search
- **Development Tools**: Build systems, testing, debugging
- **UI Extensions**: Custom UI components, themes
- **Data Processing**: Data transformation, analysis
- **System Integration**: OS integration, external tools

## Getting Started

### 1. Create Plugin Directory

Create a new directory in one of the plugin directories:
- `server/plugins/builtin/` - Built-in plugins
- `server/plugins/community/` - Community plugins
- `server/plugins/custom/` - Custom plugins

### 2. Plugin Structure

```
my_plugin/
├── __init__.py          # Plugin entry point
├── plugin.json          # Plugin metadata
├── main.py             # Main plugin logic
├── config.py           # Configuration handling
└── README.md           # Plugin documentation
```

### 3. Basic Plugin Template

```python
# __init__.py
from .main import MyPlugin

__all__ = ["MyPlugin"]
```

```python
# main.py
from server.plugins.base import BasePlugin, PluginMetadata, PluginConfig

class MyPlugin(BasePlugin):
    """Example plugin implementation"""
    
    async def initialize(self) -> bool:
        """Initialize the plugin"""
        self.logger.info("Initializing MyPlugin")
        return True
    
    async def activate(self) -> bool:
        """Activate the plugin"""
        self.logger.info("Activating MyPlugin")
        return True
    
    async def deactivate(self) -> bool:
        """Deactivate the plugin"""
        self.logger.info("Deactivating MyPlugin")
        return True
    
    async def cleanup(self) -> bool:
        """Clean up plugin resources"""
        self.logger.info("Cleaning up MyPlugin")
        return True
```

### 4. Plugin Metadata

```json
{
  "name": "my-plugin",
  "version": "1.0.0",
  "description": "An example plugin for Reverie Code Studio",
  "author": "Your Name",
  "category": "development_tools",
  "homepage": "https://github.com/yourname/my-plugin",
  "repository": "https://github.com/yourname/my-plugin",
  "license": "MIT",
  "keywords": ["example", "development"],
  "dependencies": [],
  "python_requires": ">=3.8",
  "api_version": "1.0.0",
  "config_schema": {
    "type": "object",
    "properties": {
      "enabled": {"type": "boolean", "default": true},
      "debug": {"type": "boolean", "default": false}
    }
  },
  "default_config": {
    "enabled": true,
    "debug": false
  }
}
```

## Plugin Types

### Tool Plugin

Extends the AI agent with new tools:

```python
from server.plugins.base import ToolPlugin

class MyToolPlugin(ToolPlugin):
    def get_tools(self) -> Dict[str, Callable]:
        return {
            "my_tool": self.my_tool_function
        }
    
    def get_tool_descriptions(self) -> Dict[str, str]:
        return {
            "my_tool": "my_tool(param: str) - Description of what the tool does"
        }
    
    async def my_tool_function(self, param: str) -> Dict[str, Any]:
        """Implementation of the tool"""
        return {"result": f"Processed: {param}"}
```

### Analysis Plugin

Provides code analysis capabilities:

```python
from server.plugins.base import AnalysisPlugin

class MyAnalysisPlugin(AnalysisPlugin):
    async def analyze_code(self, code: str, language: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze code and return insights"""
        # Implement your analysis logic
        return {
            "issues": [],
            "suggestions": [],
            "metrics": {}
        }
```

### Integration Plugin

Integrates with external services:

```python
from server.plugins.base import IntegrationPlugin

class MyIntegrationPlugin(IntegrationPlugin):
    async def connect(self) -> bool:
        """Connect to external service"""
        # Implement connection logic
        return True
    
    async def disconnect(self) -> bool:
        """Disconnect from external service"""
        # Implement disconnection logic
        return True
```

## Configuration Management

### Plugin Configuration

```python
class MyPlugin(BasePlugin):
    async def initialize(self) -> bool:
        # Access configuration values
        debug_mode = self.get_config_value("debug", False)
        api_key = self.get_config_value("api_key")
        
        if not api_key:
            self.logger.error("API key not configured")
            return False
        
        return True
```

### Dynamic Configuration Updates

```python
# Update configuration via API
PUT /api/v1/plugins/my-plugin/config
{
  "enabled": true,
  "config": {
    "debug": true,
    "api_key": "new-key"
  }
}
```

## Hook System

### Registering Hooks

```python
class MyPlugin(BasePlugin):
    async def activate(self) -> bool:
        # Register for file save events
        self.register_hook("file_saved", self.on_file_saved)
        return True
    
    async def on_file_saved(self, file_path: str, content: str):
        """Handle file save event"""
        self.logger.info(f"File saved: {file_path}")
```

### Emitting Hooks

```python
# Emit custom hooks
await self.emit_hook("my_custom_event", data={"key": "value"})
```

## Error Handling

### Plugin Exceptions

```python
from server.plugins.exceptions import PluginException, PluginConfigError

class MyPlugin(BasePlugin):
    async def initialize(self) -> bool:
        try:
            # Plugin initialization logic
            pass
        except Exception as e:
            raise PluginConfigError(f"Failed to initialize: {e}", self.metadata.name)
```

## Testing Plugins

### Unit Testing

```python
import pytest
from server.plugins.base import PluginMetadata, PluginConfig, PluginCategory
from my_plugin import MyPlugin

@pytest.fixture
def plugin():
    metadata = PluginMetadata(
        name="test-plugin",
        version="1.0.0",
        description="Test plugin",
        author="Test Author",
        category=PluginCategory.DEVELOPMENT_TOOLS
    )
    config = PluginConfig()
    return MyPlugin(metadata, config)

@pytest.mark.asyncio
async def test_plugin_initialization(plugin):
    result = await plugin.initialize()
    assert result is True
```

## Best Practices

### 1. Error Handling
- Always handle exceptions gracefully
- Use appropriate plugin exception types
- Log errors with context information

### 2. Resource Management
- Clean up resources in the cleanup method
- Use async context managers when appropriate
- Avoid blocking operations in the main thread

### 3. Configuration
- Provide sensible default configurations
- Validate configuration values
- Document configuration options

### 4. Logging
- Use the provided logger instance
- Include plugin name in log messages
- Use appropriate log levels

### 5. Dependencies
- Minimize external dependencies
- Specify version constraints
- Handle missing dependencies gracefully

## Deployment

### 1. Package Structure
```
my-plugin/
├── setup.py
├── pyproject.toml
├── README.md
├── LICENSE
└── src/
    └── my_plugin/
        ├── __init__.py
        ├── plugin.json
        └── main.py
```

### 2. Installation
```bash
# Install plugin
pip install my-plugin

# Or copy to plugin directory
cp -r my-plugin/ server/plugins/custom/
```

### 3. Registration
Plugins are automatically discovered and registered when the server starts.

## API Reference

See the [API Reference](api-reference.md) for detailed documentation of all plugin system APIs.

## Examples

See the [Examples](examples/) directory for complete plugin examples:
- [Hello World Plugin](examples/hello-world/)
- [File Watcher Plugin](examples/file-watcher/)
- [Code Formatter Plugin](examples/code-formatter/)
- [External API Integration](examples/api-integration/)

## Support

For questions and support:
- Check the [FAQ](faq.md)
- Review [Common Issues](troubleshooting.md)
- Join our [Discord Community](https://discord.gg/reverie-code-studio)
- Open an issue on [GitHub](https://github.com/reverie-code-studio/plugins)
