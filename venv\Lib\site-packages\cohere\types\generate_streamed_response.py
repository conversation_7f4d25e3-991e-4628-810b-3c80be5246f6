# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
from ..core.unchecked_base_model import UncheckedBaseModel
import typing
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic
from .finish_reason import FinishReason
from .generate_stream_end_response import GenerateStreamEndResponse
import typing_extensions
from ..core.unchecked_base_model import UnionMetadata


class TextGenerationGenerateStreamedResponse(UncheckedBaseModel):
    """
    Response in content type stream when `stream` is `true` in the request parameters. Generation tokens are streamed with the GenerationStream response. The final response is of type GenerationFinalResponse.
    """

    event_type: typing.Literal["text-generation"] = "text-generation"
    text: str
    index: typing.Optional[int] = None
    is_finished: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class StreamEndGenerateStreamedResponse(UncheckedBaseModel):
    """
    Response in content type stream when `stream` is `true` in the request parameters. Generation tokens are streamed with the GenerationStream response. The final response is of type GenerationFinalResponse.
    """

    event_type: typing.Literal["stream-end"] = "stream-end"
    is_finished: bool
    finish_reason: typing.Optional[FinishReason] = None
    response: GenerateStreamEndResponse

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


class StreamErrorGenerateStreamedResponse(UncheckedBaseModel):
    """
    Response in content type stream when `stream` is `true` in the request parameters. Generation tokens are streamed with the GenerationStream response. The final response is of type GenerationFinalResponse.
    """

    event_type: typing.Literal["stream-error"] = "stream-error"
    index: typing.Optional[int] = None
    is_finished: bool
    finish_reason: FinishReason
    err: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow


GenerateStreamedResponse = typing_extensions.Annotated[
    typing.Union[
        TextGenerationGenerateStreamedResponse, StreamEndGenerateStreamedResponse, StreamErrorGenerateStreamedResponse
    ],
    UnionMetadata(discriminant="event_type"),
]
