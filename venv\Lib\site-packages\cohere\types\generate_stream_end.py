# This file was auto-generated by Fern from our API Definition.

from .generate_stream_event import GenerateStreamEvent
import typing
from .finish_reason import FinishReason
from .generate_stream_end_response import GenerateStreamEndResponse
from ..core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class GenerateStreamEnd(GenerateStreamEvent):
    is_finished: bool
    finish_reason: typing.Optional[FinishReason] = None
    response: GenerateStreamEndResponse

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
