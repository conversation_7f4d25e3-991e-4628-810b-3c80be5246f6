{%- extends "basic/layout.html" %}

{%- block extrahead %}
  {{ super() }}
  <link rel="stylesheet" href="{{ pathto('_static/custom.css', resource=True) }}" type="text/css" />
  {% if theme_touch_icon %}
    <link rel="apple-touch-icon" href="{{ pathto('_static/' ~ theme_touch_icon, resource=True) }}" />
  {% endif %}

  {# Deprecated in favor of html_baseurl (pageurl). This is already set in the basic theme #}
  {% if theme_canonical_url and not pageurl %}
    <link rel="canonical" href="{{ theme_canonical_url }}{{ pagename }}.html" />
  {% endif %}
{% endblock %}

{# top+bottom related navs; we also have our own in sidebar #}
{%- macro rellink_markup() %}
  <nav id="rellinks">
    <ul>
      {%- if prev %}
        <li>
          &larr;
          <a href="{{ prev.link|e }}" title="Previous document">{{ prev.title }}</a>
        </li>
      {%- endif %}
      {%- if next %}
        <li>
          <a href="{{ next.link|e }}" title="Next document">{{ next.title }}</a>
          &rarr;
        </li>
      {%- endif %}
    </ul>
  </nav>
{%- endmacro %}

{%- set theme_show_relbar_top = theme_show_relbar_top or theme_show_relbars %}
{%- set theme_show_relbar_bottom = theme_show_relbar_bottom or theme_show_relbars %}

{# removed existing top+bottom related nav, and embed in main content #}
{%- block relbar1 %}{% endblock %}
{%- block relbar2 %}{% endblock %}

{# Nav should appear before content, not after #}
{%- block content %}
{%- if theme_fixed_sidebar|lower == 'true' %}
  <div class="document">
    {{ sidebar() }}
    {%- block document %}
      <div class="documentwrapper">
      {%- if render_sidebar %}
        <div class="bodywrapper">
      {%- endif %}

          {%- block relbar_top %}
            {%- if theme_show_relbar_top|tobool %}
              <div class="related top">
                &nbsp;
                {{- rellink_markup () }}
              </div>
            {%- endif %}
          {% endblock %}

          <div class="body" role="main">
            {% block body %} {% endblock %}
          </div>

          {%- block relbar_bottom %}
            {%- if theme_show_relbar_bottom|tobool %}
              <div class="related bottom">
                &nbsp;
                {{- rellink_markup () }}
              </div>
            {%- endif %}
          {% endblock %}

      {%- if render_sidebar %}
        </div>
      {%- endif %}
      </div>
    {%- endblock %}
    <div class="clearer"></div>
  </div>
{%- else %}
{{ super() }}
{%- endif %}
{%- endblock %}

{%- block footer %}
    <div class="footer">
      {% if show_copyright %}&#169;{{ copyright }}.{% endif %}
      {% if show_sphinx %}
      {% if show_copyright %}|{% endif %}
      Powered by <a href="https://www.sphinx-doc.org/">Sphinx {{ sphinx_version }}</a>
      &amp; <a href="https://alabaster.readthedocs.io">Alabaster {{ alabaster_version }}</a>
      {% endif %}
      {%- if show_source and has_source and sourcename %}
      {% if show_copyright or show_sphinx %}|{% endif %}
      <a href="{{ pathto('_sources/' + sourcename, resource=True)|e }}"
          rel="nofollow">{{ _('Page source') }}</a>
      {%- endif %}
    </div>

    {% if theme_github_banner|lower != 'false' %}
    <a href="https://github.com/{{ theme_github_user }}/{{ theme_github_repo }}" class="github">
        {%- if theme_github_banner|lower == 'true' %}
        <img src="{{ pathto('_static/github-banner.svg', resource=True) }}" alt="Fork me on GitHub" class="github"/>
        {%- else %}
        <img src="{{ pathto('_static/' ~ theme_github_banner, resource=True) }}" alt="Fork me on GitHub" class="github"/>
        {%- endif %}
    </a>
    {% endif %}

    {% if theme_analytics_id %}
    <script>

      var _gaq = _gaq || [];
      _gaq.push(['_setAccount', '{{ theme_analytics_id }}']);
      _gaq.push(['_setDomainName', 'none']);
      _gaq.push(['_setAllowLinker', true]);
      _gaq.push(['_trackPageview']);

      (function() {
        var ga = document.createElement('script'); ga.async = true;
        ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'https://www') + '.google-analytics.com/ga.js';
        var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
      })();

    </script>
    {% endif %}
{%- endblock %}
