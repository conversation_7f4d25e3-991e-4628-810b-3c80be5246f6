"""
Tools API endpoints
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from tools.manager import ToolManager
from core.logging import logger
from core.exceptions import ToolException

router = APIRouter()


class ToolExecuteRequest(BaseModel):
    """Tool execution request"""
    tool_name: str
    parameters: Dict[str, Any] = {}


class ToolResponse(BaseModel):
    """Tool execution response"""
    success: bool
    result: Optional[Any] = None
    error: Optional[str] = None


class WebSearchRequest(BaseModel):
    """Web search request"""
    query: str
    max_results: int = 5


class CommandExecuteRequest(BaseModel):
    """Command execution request"""
    command: str
    timeout: Optional[int] = None
    working_dir: Optional[str] = None


class BatchExecuteRequest(BaseModel):
    """Batch command execution request"""
    commands: List[str]
    stop_on_error: bool = True


class TerminalExecuteRequest(BaseModel):
    """Terminal execution request"""
    command: str
    interactive: bool = False
    query: str
    max_results: Optional[int] = 5


class FileReadRequest(BaseModel):
    """File read request"""
    path: str


class FileWriteRequest(BaseModel):
    """File write request"""
    path: str
    content: str
    backup: Optional[bool] = None


class FileListRequest(BaseModel):
    """File list request"""
    path: str = "."


class CodeAnalyzeRequest(BaseModel):
    """Code analysis request"""
    code: str
    language: str = "python"


# Global tool manager instance
tool_manager = None

def get_tool_manager():
    """Get or create tool manager instance"""
    global tool_manager
    if tool_manager is None:
        tool_manager = ToolManager()
    return tool_manager


@router.get("/")
async def list_tools():
    """List all available tools"""
    manager = get_tool_manager()
    return {
        "tools": list(manager.tools.keys()),
        "descriptions": manager.get_tools_description()
    }


@router.post("/execute", response_model=ToolResponse)
async def execute_tool(request: ToolExecuteRequest):
    """Execute a tool with given parameters"""
    try:
        manager = get_tool_manager()
        result = await manager.execute_tool(
            request.tool_name,
            **request.parameters
        )
        return ToolResponse(**result)
        
    except ToolException as e:
        logger.error(f"Tool execution failed: {e}")
        return ToolResponse(success=False, error=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in tool execution: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/web_search")
async def web_search(request: WebSearchRequest):
    """Search the web for information"""
    try:
        manager = get_tool_manager()
        result = await manager.web_search(
            query=request.query,
            max_results=request.max_results
        )
        return {"success": True, "results": result}
        
    except ToolException as e:
        logger.error(f"Web search failed: {e}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error in web search: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/file/read")
async def file_read(request: FileReadRequest):
    """Read contents of a file"""
    try:
        manager = get_tool_manager()
        content = await manager.file_read(request.path)
        return {"success": True, "content": content}
        
    except ToolException as e:
        logger.error(f"File read failed: {e}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error in file read: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/file/write")
async def file_write(request: FileWriteRequest):
    """Write content to a file"""
    try:
        manager = get_tool_manager()
        result = await manager.file_write(
            path=request.path,
            content=request.content,
            backup=request.backup
        )
        return {"success": True, "message": result}
        
    except ToolException as e:
        logger.error(f"File write failed: {e}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error in file write: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/file/list")
async def file_list(request: FileListRequest):
    """List files and directories"""
    try:
        manager = get_tool_manager()
        items = await manager.file_list(request.path)
        return {"success": True, "items": items}
        
    except ToolException as e:
        logger.error(f"File list failed: {e}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error in file list: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/command/execute")
async def command_execute(request: CommandExecuteRequest):
    """Execute a shell command"""
    try:
        manager = get_tool_manager()
        result = await manager.command_execute(
            command=request.command,
            timeout=request.timeout
        )
        return {"success": True, "result": result}
        
    except ToolException as e:
        logger.error(f"Command execution failed: {e}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error in command execution: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/code/analyze")
async def code_analyze(request: CodeAnalyzeRequest):
    """Analyze code structure"""
    try:
        manager = get_tool_manager()
        analysis = await manager.code_analyze(
            code=request.code,
            language=request.language
        )
        return {"success": True, "analysis": analysis}
        
    except ToolException as e:
        logger.error(f"Code analysis failed: {e}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error in code analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/command/terminal")
async def terminal_execute(request: TerminalExecuteRequest):
    """Execute command in terminal with enhanced support"""
    try:
        manager = get_tool_manager()
        result = await manager.terminal_execute(
            command=request.command,
            interactive=request.interactive
        )
        return {"success": True, "result": result}

    except ToolException as e:
        logger.error(f"Terminal execution failed: {e}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error in terminal execution: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/command/batch")
async def batch_execute(request: BatchExecuteRequest):
    """Execute multiple commands in sequence"""
    try:
        manager = get_tool_manager()
        result = await manager.batch_execute(
            commands=request.commands,
            stop_on_error=request.stop_on_error
        )
        return {"success": True, "result": result}

    except ToolException as e:
        logger.error(f"Batch execution failed: {e}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error in batch execution: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/environment")
async def get_environment_info():
    """Get current environment information"""
    try:
        manager = get_tool_manager()
        info = await manager.environment_info()
        return {"success": True, "environment": info}

    except ToolException as e:
        logger.error(f"Environment info failed: {e}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error getting environment info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/command/history")
async def get_command_history():
    """Get command execution history"""
    try:
        manager = get_tool_manager()
        history = manager.command_history[-20:]  # Last 20 commands
        return {"success": True, "history": history}

    except Exception as e:
        logger.error(f"Error getting command history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tools/list")
async def list_available_tools():
    """List all available tools with descriptions"""
    try:
        manager = get_tool_manager()
        tools_desc = manager.get_tools_description()
        tools_list = list(manager.tools.keys())

        return {
            "success": True,
            "tools": tools_list,
            "descriptions": tools_desc,
            "total_tools": len(tools_list)
        }

    except Exception as e:
        logger.error(f"Error listing tools: {e}")
        raise HTTPException(status_code=500, detail=str(e))
