// Rilance Code Studio - Web Interface JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the page
    initializeStatusUpdates();
    initializeSmoothScrolling();
    initializeAnimations();
});

// Status Updates
function initializeStatusUpdates() {
    updateServerStatus();
    updateCurrentModel();
    updateUptime();
    
    // Update status every 30 seconds
    setInterval(() => {
        updateServerStatus();
        updateCurrentModel();
        updateUptime();
    }, 30000);
}

async function updateServerStatus() {
    try {
        const response = await fetch('/health');
        const data = await response.json();
        
        const statusElement = document.getElementById('server-status');
        if (statusElement) {
            statusElement.textContent = data.status === 'healthy' ? 'Online' : 'Offline';
            statusElement.className = `status-value ${data.status === 'healthy' ? 'online' : 'offline'}`;
        }
    } catch (error) {
        const statusElement = document.getElementById('server-status');
        if (statusElement) {
            statusElement.textContent = 'Offline';
            statusElement.className = 'status-value offline';
        }
    }
}

async function updateCurrentModel() {
    try {
        const response = await fetch('/api/v1/models/current');
        const data = await response.json();
        
        const modelElement = document.getElementById('current-model');
        if (modelElement) {
            if (data && data.name) {
                modelElement.textContent = data.name;
            } else {
                modelElement.textContent = 'No model loaded';
            }
        }
    } catch (error) {
        const modelElement = document.getElementById('current-model');
        if (modelElement) {
            modelElement.textContent = 'Unknown';
        }
    }
}

async function updateUptime() {
    try {
        const response = await fetch('/api/v1/system/status');
        const data = await response.json();
        
        const uptimeElement = document.getElementById('uptime');
        if (uptimeElement && data.uptime) {
            const uptime = formatUptime(data.uptime);
            uptimeElement.textContent = uptime;
        }
    } catch (error) {
        const uptimeElement = document.getElementById('uptime');
        if (uptimeElement) {
            uptimeElement.textContent = 'Unknown';
        }
    }
}

function formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
        return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
        return `${hours}h ${minutes}m`;
    } else {
        return `${minutes}m`;
    }
}

// Smooth Scrolling
function initializeSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Animations
function initializeAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.feature-card, .api-card, .status-card');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
    
    // Typing animation for hero title
    initializeTypingAnimation();
}

function initializeTypingAnimation() {
    const heroTitle = document.querySelector('.hero-title');
    if (!heroTitle) return;
    
    const originalText = heroTitle.innerHTML;
    const words = originalText.split(' ');
    let currentWordIndex = 0;
    
    // Don't run animation on mobile
    if (window.innerWidth < 768) return;
    
    heroTitle.innerHTML = '';
    
    function typeWord() {
        if (currentWordIndex < words.length) {
            heroTitle.innerHTML += words[currentWordIndex] + ' ';
            currentWordIndex++;
            setTimeout(typeWord, 200);
        }
    }
    
    // Start typing animation after a short delay
    setTimeout(typeWord, 500);
}

// API Testing Functions
async function testAPI(endpoint, method = 'GET', data = null) {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        if (data) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(endpoint, options);
        const result = await response.json();
        
        console.log(`API Test - ${method} ${endpoint}:`, result);
        return result;
    } catch (error) {
        console.error(`API Test Failed - ${method} ${endpoint}:`, error);
        return null;
    }
}

// Utility Functions
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Copied to clipboard!');
    }).catch(err => {
        console.error('Failed to copy: ', err);
    });
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--bg-secondary);
        color: var(--text-primary);
        padding: 12px 20px;
        border-radius: 8px;
        border: 1px solid var(--border-color);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Keyboard Shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K for quick API docs
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        window.open('/docs', '_blank');
    }
    
    // Ctrl/Cmd + Shift + S for server status
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'S') {
        e.preventDefault();
        updateServerStatus();
        updateCurrentModel();
        updateUptime();
        showNotification('Status updated!');
    }
});

// Export functions for console access
window.RilanceAPI = {
    testAPI,
    updateServerStatus,
    updateCurrentModel,
    updateUptime,
    copyToClipboard,
    showNotification
};

// Console welcome message
console.log(`
%c🌟 Rilance Code Studio Web Interface 🌟
%cWelcome to the Reverie AI-powered development environment!

Available functions:
- RilanceAPI.testAPI(endpoint, method, data)
- RilanceAPI.updateServerStatus()
- RilanceAPI.showNotification(message)

Keyboard shortcuts:
- Ctrl/Cmd + K: Open API docs
- Ctrl/Cmd + Shift + S: Update status

Visit /docs for complete API documentation.
`, 
'color: #9d4edd; font-size: 16px; font-weight: bold;',
'color: #c77dff; font-size: 12px;'
);
