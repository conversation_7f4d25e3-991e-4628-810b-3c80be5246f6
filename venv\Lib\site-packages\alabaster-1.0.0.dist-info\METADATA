Metadata-Version: 2.1
Name: alabaster
Version: 1.0.0
Summary: A light, configurable Sphinx theme
Maintainer: <PERSON>
Maintainer-email: <PERSON> <<EMAIL>>
Requires-Python: >=3.10
Description-Content-Type: text/x-rst
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: Sphinx
Classifier: Framework :: Sphinx :: Theme
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Documentation
Classifier: Topic :: Documentation :: Sphinx
Classifier: Topic :: Software Development :: Documentation
Project-URL: Changelog, https://alabaster.readthedocs.io/en/latest/changelog.html
Project-URL: Documentation, https://alabaster.readthedocs.io/
Project-URL: Download, https://pypi.org/project/alabaster/
Project-URL: Homepage, https://alabaster.readthedocs.io/
Project-URL: Issue tracker, https://github.com/sphinx-doc/alabaster/issues
Project-URL: Source, https://github.com/sphinx-doc/alabaster

.. image:: https://img.shields.io/pypi/v/alabaster.svg
   :target: https://pypi.org/project/alabaster/
   :alt: Package on PyPI

.. image:: https://github.com/sphinx-doc/alabaster/actions/workflows/test.yml/badge.svg
    :target: https://github.com/sphinx-doc/alabaster/actions/workflows/test.yml
    :alt: CI Status

.. image:: https://readthedocs.org/projects/alabaster/badge/
   :target: https://alabaster.readthedocs.io/
   :alt: Documentation Status

.. image:: https://img.shields.io/badge/License-BSD%203--Clause-blue.svg
   :target: https://opensource.org/license/BSD-3-Clause
   :alt: BSD 3 Clause


What is Alabaster?
==================

Alabaster is a visually (c)lean, responsive, configurable theme for the `Sphinx
<https://www.sphinx-doc.org>`_ documentation system.
It requires Python 3.10 or newer and Sphinx 6.2 or newer.

It began as a third-party theme, and is still maintained separately, but as of
Sphinx 1.3, Alabaster is an install-time dependency of Sphinx and is selected
as the default theme.

Live examples of this theme can be seen on `this project's own website
<https://alabaster.readthedocs.io/>`_, `paramiko.org <https://www.paramiko.org>`_,
`fabfile.org <https://www.fabfile.org>`_ and `pyinvoke.org <https://www.pyinvoke.org>`_.

For more documentation, please see https://alabaster.readthedocs.io/.

