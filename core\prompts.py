"""
System Prompt Management for Reverie Code Studio
Handles loading and formatting of system prompts for different modes
"""

from pathlib import Path
from typing import Dict, Optional
from core.logging import logger

class PromptManager:
    """Manages system prompts for different modes and providers"""
    
    def __init__(self):
        self.prompts_dir = Path(__file__).parent.parent / "resources" / "prompts"
        self._cached_prompts: Dict[str, str] = {}
    
    def load_prompt(self, prompt_name: str) -> str:
        """Load a prompt from file with caching"""
        
        if prompt_name in self._cached_prompts:
            return self._cached_prompts[prompt_name]
        
        prompt_file = self.prompts_dir / f"{prompt_name}.txt"
        
        if not prompt_file.exists():
            logger.warning(f"Prompt file not found: {prompt_file}")
            return ""
        
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            self._cached_prompts[prompt_name] = content
            logger.info(f"Loaded prompt: {prompt_name}")
            return content
            
        except Exception as e:
            logger.error(f"Failed to load prompt {prompt_name}: {e}")
            return ""
    
    def get_chat_system_prompt(self, provider: str = "local") -> str:
        """Get system prompt for chat mode"""
        
        base_prompt = self.load_prompt("reverie_system")
        
        # Add provider-specific enhancements
        if provider == "local":
            enhancement = """

## Intelligent Web Search
I have intelligent web search capabilities that work automatically:
- I detect when you need current information and search without asking
- I seamlessly integrate search results into my responses
- I search for the latest information about technologies, libraries, and best practices
- You'll see search results formatted beautifully when I find relevant information

## Response Guidelines
- I provide complete, helpful responses with current information
- I use web search when I detect you need up-to-date information
- I maintain a conversational, friendly tone while being technically accurate
- I ensure my responses are comprehensive and actionable
"""
            return base_prompt + enhancement
        
        return base_prompt
    
    def get_agent_system_prompt(self, provider: str = "local") -> str:
        """Get system prompt for agent mode"""
        
        base_prompt = self.load_prompt("reverie_agent")
        
        # Add provider-specific enhancements
        if provider == "local":
            enhancement = """

## Intelligent Research Integration
In Agent mode, I automatically research when needed:
- I search for current best practices and solutions without asking
- I verify information and find the most up-to-date approaches
- I research libraries, frameworks, and tools as part of my planning
- I integrate search results naturally into my analysis and recommendations

## Execution Excellence
- I provide detailed progress updates as I work
- I ensure all solutions are current and follow best practices
- I verify my approaches with web research when appropriate
- I deliver complete, tested solutions with comprehensive documentation

## Quality Assurance
- All code follows current best practices and standards
- I research the latest patterns and approaches for each task
- I verify compatibility and version requirements
- I provide future-proof solutions based on current trends
"""
            return base_prompt + enhancement
        
        return base_prompt
    
    def get_provider_specific_prompt(self, provider: str, mode: str = "chat") -> str:
        """Get provider-specific system prompt"""
        
        if provider == "zhipu":
            return self.load_prompt("reverie_zhipu")
        elif mode == "agent":
            return self.get_agent_system_prompt(provider)
        else:
            return self.get_chat_system_prompt(provider)


# Global prompt manager instance
prompt_manager = PromptManager()


def get_system_prompt(mode: str = "chat", provider: str = "local") -> str:
    """Get appropriate system prompt for mode and provider"""
    
    if mode == "agent":
        return prompt_manager.get_agent_system_prompt(provider)
    else:
        return prompt_manager.get_chat_system_prompt(provider)


def format_messages_with_system_prompt(
    messages: list, 
    mode: str = "chat", 
    provider: str = "local"
) -> tuple[list, str]:
    """Format messages with appropriate system prompt"""
    
    system_prompt = get_system_prompt(mode, provider)
    
    # Check if there's already a system message
    has_system = any(msg.get("role") == "system" for msg in messages)
    
    if has_system:
        # Replace existing system message
        formatted_messages = []
        for msg in messages:
            if msg.get("role") == "system":
                formatted_messages.append({
                    "role": "system",
                    "content": system_prompt
                })
            else:
                formatted_messages.append(msg)
    else:
        # Add system message at the beginning
        formatted_messages = [
            {"role": "system", "content": system_prompt}
        ] + messages
    
    return formatted_messages, system_prompt
