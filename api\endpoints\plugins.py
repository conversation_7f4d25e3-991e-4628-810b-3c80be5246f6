"""
Plugin Management API Endpoints for Reverie Code Studio
Provides REST API for plugin discovery, loading, configuration, and management
"""

from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field

from plugins import PluginManager, PluginCategory, PluginStatus, PluginConfig
from plugins.exceptions import (
    PluginException, PluginNotFoundError, PluginLoadError,
    PluginDependencyError, PluginActivationError
)
from core.logging import logger

router = APIRouter()

# Global plugin manager instance (will be initialized in main.py)
plugin_manager: Optional[PluginManager] = None


# Request/Response Models
class PluginInfo(BaseModel):
    """Plugin information response model"""
    name: str
    version: str
    description: str
    author: str
    category: PluginCategory
    status: PluginStatus
    enabled: bool
    homepage: Optional[str] = None
    repository: Optional[str] = None
    license: Optional[str] = None
    keywords: List[str] = Field(default_factory=list)
    dependencies: List[str] = Field(default_factory=list)
    loaded_at: Optional[str] = None


class PluginConfigRequest(BaseModel):
    """Plugin configuration request model"""
    enabled: bool = True
    config: Dict[str, Any] = Field(default_factory=dict)
    priority: int = 100
    auto_load: bool = True


class PluginActionRequest(BaseModel):
    """Plugin action request model"""
    plugin_name: str
    action: str  # load, unload, activate, deactivate, reload


class PluginDiscoveryResponse(BaseModel):
    """Plugin discovery response model"""
    discovered_count: int
    registered_count: int
    plugins: List[PluginInfo]


# Dependency injection
def get_plugin_manager() -> PluginManager:
    """Get the plugin manager instance"""
    from server.main import plugin_manager
    if plugin_manager is None:
        raise HTTPException(status_code=500, detail="Plugin manager not initialized")
    return plugin_manager


@router.get("/", response_model=List[PluginInfo])
async def list_plugins(
    category: Optional[PluginCategory] = None,
    status: Optional[PluginStatus] = None,
    enabled_only: bool = False,
    manager: PluginManager = Depends(get_plugin_manager)
):
    """
    List all plugins with optional filtering
    
    Args:
        category: Filter by plugin category
        status: Filter by plugin status
        enabled_only: Only return enabled plugins
    """
    try:
        plugins_metadata = manager.registry.list_plugins(
            category=category,
            status=status,
            enabled_only=enabled_only
        )
        
        plugins_info = []
        for metadata in plugins_metadata:
            config = manager.registry.get_plugin_config(metadata.name)
            status = manager.registry.get_plugin_status(metadata.name)
            
            plugin_info = PluginInfo(
                name=metadata.name,
                version=metadata.version,
                description=metadata.description,
                author=metadata.author,
                category=metadata.category,
                status=status or PluginStatus.UNLOADED,
                enabled=config.enabled if config else True,
                homepage=metadata.homepage,
                repository=metadata.repository,
                license=metadata.license,
                keywords=metadata.keywords,
                dependencies=metadata.dependencies,
                loaded_at=metadata.loaded_at.isoformat() if metadata.loaded_at else None
            )
            plugins_info.append(plugin_info)
        
        return plugins_info
        
    except Exception as e:
        logger.error(f"Failed to list plugins: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{plugin_name}", response_model=PluginInfo)
async def get_plugin(
    plugin_name: str,
    manager: PluginManager = Depends(get_plugin_manager)
):
    """Get detailed information about a specific plugin"""
    try:
        metadata = manager.registry.get_plugin_metadata(plugin_name)
        if not metadata:
            raise HTTPException(status_code=404, detail=f"Plugin not found: {plugin_name}")
        
        config = manager.registry.get_plugin_config(plugin_name)
        status = manager.registry.get_plugin_status(plugin_name)
        
        return PluginInfo(
            name=metadata.name,
            version=metadata.version,
            description=metadata.description,
            author=metadata.author,
            category=metadata.category,
            status=status or PluginStatus.UNLOADED,
            enabled=config.enabled if config else True,
            homepage=metadata.homepage,
            repository=metadata.repository,
            license=metadata.license,
            keywords=metadata.keywords,
            dependencies=metadata.dependencies,
            loaded_at=metadata.loaded_at.isoformat() if metadata.loaded_at else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get plugin {plugin_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/discover", response_model=PluginDiscoveryResponse)
async def discover_plugins(
    background_tasks: BackgroundTasks,
    manager: PluginManager = Depends(get_plugin_manager)
):
    """Discover new plugins in the plugin directories"""
    try:
        # Run discovery in background
        discovered = await manager.discover_plugins()
        
        # Get current plugin list
        all_plugins = manager.registry.list_plugins()
        
        plugins_info = []
        for metadata in all_plugins:
            config = manager.registry.get_plugin_config(metadata.name)
            status = manager.registry.get_plugin_status(metadata.name)
            
            plugin_info = PluginInfo(
                name=metadata.name,
                version=metadata.version,
                description=metadata.description,
                author=metadata.author,
                category=metadata.category,
                status=status or PluginStatus.UNLOADED,
                enabled=config.enabled if config else True,
                homepage=metadata.homepage,
                repository=metadata.repository,
                license=metadata.license,
                keywords=metadata.keywords,
                dependencies=metadata.dependencies,
                loaded_at=metadata.loaded_at.isoformat() if metadata.loaded_at else None
            )
            plugins_info.append(plugin_info)
        
        return PluginDiscoveryResponse(
            discovered_count=len(discovered),
            registered_count=len(all_plugins),
            plugins=plugins_info
        )
        
    except Exception as e:
        logger.error(f"Failed to discover plugins: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{plugin_name}/load")
async def load_plugin(
    plugin_name: str,
    manager: PluginManager = Depends(get_plugin_manager)
):
    """Load a plugin"""
    try:
        plugin = await manager.load_plugin(plugin_name)
        return {
            "success": True,
            "message": f"Plugin {plugin_name} loaded successfully",
            "plugin": {
                "name": plugin.metadata.name,
                "version": plugin.metadata.version,
                "status": plugin.status.value
            }
        }
        
    except PluginNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except (PluginLoadError, PluginDependencyError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to load plugin {plugin_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{plugin_name}/unload")
async def unload_plugin(
    plugin_name: str,
    manager: PluginManager = Depends(get_plugin_manager)
):
    """Unload a plugin"""
    try:
        await manager.unload_plugin(plugin_name)
        return {
            "success": True,
            "message": f"Plugin {plugin_name} unloaded successfully"
        }
        
    except PluginNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PluginDependencyError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to unload plugin {plugin_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{plugin_name}/activate")
async def activate_plugin(
    plugin_name: str,
    manager: PluginManager = Depends(get_plugin_manager)
):
    """Activate a loaded plugin"""
    try:
        await manager.activate_plugin(plugin_name)
        return {
            "success": True,
            "message": f"Plugin {plugin_name} activated successfully"
        }
        
    except PluginNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except PluginActivationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to activate plugin {plugin_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{plugin_name}/deactivate")
async def deactivate_plugin(
    plugin_name: str,
    manager: PluginManager = Depends(get_plugin_manager)
):
    """Deactivate an active plugin"""
    try:
        await manager.deactivate_plugin(plugin_name)
        return {
            "success": True,
            "message": f"Plugin {plugin_name} deactivated successfully"
        }
        
    except PluginNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to deactivate plugin {plugin_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{plugin_name}/reload")
async def reload_plugin(
    plugin_name: str,
    manager: PluginManager = Depends(get_plugin_manager)
):
    """Reload a plugin (unload and load again)"""
    try:
        await manager.reload_plugin(plugin_name)
        return {
            "success": True,
            "message": f"Plugin {plugin_name} reloaded successfully"
        }
        
    except PluginNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except (PluginLoadError, PluginDependencyError, PluginActivationError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to reload plugin {plugin_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{plugin_name}/config", response_model=PluginConfigRequest)
async def get_plugin_config(
    plugin_name: str,
    manager: PluginManager = Depends(get_plugin_manager)
):
    """Get plugin configuration"""
    try:
        config = manager.registry.get_plugin_config(plugin_name)
        if not config:
            raise HTTPException(status_code=404, detail=f"Plugin config not found: {plugin_name}")
        
        return PluginConfigRequest(
            enabled=config.enabled,
            config=config.config,
            priority=config.priority,
            auto_load=config.auto_load
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get plugin config {plugin_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{plugin_name}/config")
async def update_plugin_config(
    plugin_name: str,
    config_request: PluginConfigRequest,
    manager: PluginManager = Depends(get_plugin_manager)
):
    """Update plugin configuration"""
    try:
        # Check if plugin exists
        if not manager.registry.plugin_exists(plugin_name):
            raise HTTPException(status_code=404, detail=f"Plugin not found: {plugin_name}")
        
        # Create new config
        new_config = PluginConfig(
            enabled=config_request.enabled,
            config=config_request.config,
            priority=config_request.priority,
            auto_load=config_request.auto_load
        )
        
        # Update config
        await manager.registry.update_plugin_config(plugin_name, new_config)
        
        return {
            "success": True,
            "message": f"Plugin {plugin_name} configuration updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update plugin config {plugin_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/categories", response_model=List[str])
async def list_plugin_categories():
    """List all available plugin categories"""
    return [category.value for category in PluginCategory]


@router.get("/status", response_model=Dict[str, Any])
async def get_plugin_system_status(
    manager: PluginManager = Depends(get_plugin_manager)
):
    """Get overall plugin system status"""
    try:
        all_plugins = manager.registry.list_plugins()
        loaded_plugins = manager.list_loaded_plugins()
        active_plugins = manager.list_active_plugins()
        
        status_counts = {}
        for status in PluginStatus:
            status_counts[status.value] = len([
                p for p in all_plugins 
                if manager.registry.get_plugin_status(p.name) == status
            ])
        
        category_counts = {}
        for category in PluginCategory:
            category_counts[category.value] = len([
                p for p in all_plugins if p.category == category
            ])
        
        return {
            "total_plugins": len(all_plugins),
            "loaded_plugins": len(loaded_plugins),
            "active_plugins": len(active_plugins),
            "status_breakdown": status_counts,
            "category_breakdown": category_counts,
            "plugin_directories": [str(d) for d in manager.plugin_dirs]
        }
        
    except Exception as e:
        logger.error(f"Failed to get plugin system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))
