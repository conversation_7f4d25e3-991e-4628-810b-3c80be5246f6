# This file was auto-generated by Fern from our API Definition.

from ..core.unchecked_base_model import UncheckedBaseModel
import typing
import pydantic
from .api_meta import ApiMeta
from ..core.pydantic_utilities import IS_PYDANTIC_V2


class SummarizeResponse(UncheckedBaseModel):
    id: typing.Optional[str] = pydantic.Field(default=None)
    """
    Generated ID for the summary
    """

    summary: typing.Optional[str] = pydantic.Field(default=None)
    """
    Generated summary for the text
    """

    meta: typing.Optional[ApiMeta] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow")  # type: ignore # Pydantic v2
    else:

        class Config:
            smart_union = True
            extra = pydantic.Extra.allow
