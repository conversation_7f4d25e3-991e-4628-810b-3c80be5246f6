"""
Configuration management for Rilance Code Studio Server
"""

import os
from pathlib import Path
from typing import List, Optional, Union, Dict
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings, Field
import yaml


class ServerConfig(BaseSettings):
    """Server configuration"""
    host: str = "127.0.0.1"
    port: int = 8001
    debug: bool = True
    reload: bool = True
    workers: int = 1
    log_level: str = "info"


class ModelConfig(BaseSettings):
    """AI Model configuration"""
    default_model: str = "Menlo/Lucy-128k-gguf"
    default_backend: str = "gguf"  # "transformers", "vllm", "tensorflow", or "gguf"
    model_cache_dir: str = "models"
    max_context_length: int = 131072  # Lucy supports 128k context
    temperature: float = 0.7
    top_p: float = 0.9
    max_tokens: int = 131072  # Set to model's full context capacity for unlimited generation

    # Auto-loading configuration
    auto_load_default: bool = True
    fallback_models: List[str] = ["microsoft/DialoGPT-medium", "gpt2"]

    # Backend configurations
    transformers_device: str = "auto"
    transformers_torch_dtype: str = "auto"
    transformers_load_in_8bit: bool = False
    transformers_load_in_4bit: bool = True  # Enable 4-bit by default for Jan-nano-128k
    transformers_trust_remote_code: bool = True
    transformers_low_cpu_mem_usage: bool = True

    # Predefined popular models with HuggingFace URLs
    popular_models: Dict[str, Dict[str, str]] = {
        "default": {
            "url": "https://huggingface.co/Menlo/Jan-nano-128k",
            "name": "Jan-nano-128k",
            "description": "4B model with native 128k context window (recommended)",
            "size": "~4GB",
            "backend": "vllm"
        },
        "llama-7b": {
            "url": "https://huggingface.co/meta-llama/Llama-2-7b-chat-hf",
            "name": "Llama-2-7b-chat-hf",
            "description": "Meta's Llama 2 7B Chat model",
            "size": "~13GB",
            "backend": "vllm"
        },
        "mistral-7b": {
            "url": "https://huggingface.co/mistralai/Mistral-7B-Instruct-v0.2",
            "name": "Mistral-7B-Instruct-v0.2",
            "description": "Mistral 7B Instruct model",
            "size": "~14GB",
            "backend": "vllm"
        },
        "codellama-7b": {
            "url": "https://huggingface.co/codellama/CodeLlama-7b-Instruct-hf",
            "name": "CodeLlama-7b-Instruct-hf",
            "description": "Code Llama 7B for code generation",
            "size": "~13GB",
            "backend": "vllm"
        },
        "phi-3-mini": {
            "url": "https://huggingface.co/microsoft/Phi-3-mini-4k-instruct",
            "name": "Phi-3-mini-4k-instruct",
            "description": "Microsoft's Phi-3 Mini model",
            "size": "~2GB",
            "backend": "transformers"
        },
        "gemma-7b": {
            "url": "https://huggingface.co/google/gemma-7b-it",
            "name": "gemma-7b-it",
            "description": "Google's Gemma 7B Instruct model",
            "size": "~14GB",
            "backend": "vllm"
        }
    }

    # vLLM configurations
    vllm_tensor_parallel_size: int = 1
    vllm_gpu_memory_utilization: float = 0.8
    vllm_max_model_len: int = 128000
    vllm_enforce_eager: bool = False
    vllm_disable_custom_all_reduce: bool = False

    # Model-specific optimizations
    jan_nano_optimizations: bool = True
    enable_flash_attention: bool = True
    use_cache: bool = True


class AIConfig(BaseSettings):
    """AI Chat and Agent configuration"""
    chat_system_prompt_file: str = "resources/prompts/chat_system.txt"
    agent_system_prompt_file: str = "resources/prompts/agent_system.txt"
    chat_max_history: int = 50
    chat_stream_response: bool = True
    agent_max_iterations: int = 10
    agent_timeout: int = 300


class ToolsConfig(BaseSettings):
    """Tools configuration"""
    web_search_engine: str = "duckduckgo"
    web_search_max_results: int = 5
    web_search_timeout: int = 10
    
    file_allowed_extensions: List[str] = [
        ".py", ".js", ".ts", ".html", ".css", ".md", ".txt", 
        ".json", ".yaml", ".yml", ".toml", ".ini"
    ]
    file_max_size: int = 10485760  # 10MB
    file_backup_enabled: bool = True
    
    command_allowed: List[str] = [
        "git", "npm", "pip", "python", "node", "cargo", "go", "dotnet",
        "ls", "dir", "cd", "pwd", "mkdir", "rmdir", "cp", "mv", "rm",
        "cat", "echo", "grep", "find", "which", "where", "type",
        "curl", "wget", "ping", "nslookup", "ipconfig", "ifconfig",
        "ps", "top", "kill", "killall", "jobs", "bg", "fg",
        "chmod", "chown", "du", "df", "free", "uname", "whoami",
        "history", "clear", "cls", "exit", "help", "man",
        "code", "vim", "nano", "emacs", "notepad",
        "make", "cmake", "gcc", "g++", "javac", "java",
        "docker", "kubectl", "helm", "terraform", "ansible"
    ]
    command_timeout: int = 60
    command_working_directory: str = "."


class SecurityConfig(BaseSettings):
    """Security configuration"""
    api_key_required: bool = False
    api_key: Optional[str] = None
    cors_enabled: bool = True
    cors_origins: List[str] = ["http://localhost:*", "http://127.0.0.1:*"]
    rate_limiting: bool = True
    max_requests_per_minute: int = 100
    secret_key: str = "rilance-code-studio-secret-key-change-in-production"


class LoggingConfig(BaseSettings):
    """Logging configuration"""
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
    file: str = "logs/rilance.log"
    rotation: str = "10 MB"
    retention: str = "7 days"


class Settings(BaseSettings):
    """Main settings class"""
    
    # Sub-configurations
    server: ServerConfig = ServerConfig()
    models: ModelConfig = ModelConfig()
    ai: AIConfig = AIConfig()
    tools: ToolsConfig = ToolsConfig()
    security: SecurityConfig = SecurityConfig()
    logging: LoggingConfig = LoggingConfig()
    
    # Development settings
    development_mode: bool = True
    hot_reload: bool = True
    profiling: bool = False
    mock_ai_responses: bool = False
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._load_yaml_config()
    
    def _load_yaml_config(self):
        """Load configuration from YAML file if it exists"""
        config_file = Path("config.yaml")
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    yaml_config = yaml.safe_load(f)
                
                if yaml_config:
                    # Update configurations from YAML
                    if 'server' in yaml_config:
                        for key, value in yaml_config['server'].items():
                            if hasattr(self.server, key):
                                setattr(self.server, key, value)
                    
                    if 'models' in yaml_config:
                        for key, value in yaml_config['models'].items():
                            if hasattr(self.models, key):
                                setattr(self.models, key, value)
                        
                        # Handle nested backend configs
                        if 'backends' in yaml_config['models']:
                            backends = yaml_config['models']['backends']
                            if 'transformers' in backends:
                                tf_config = backends['transformers']
                                self.models.transformers_device = tf_config.get('device', self.models.transformers_device)
                                self.models.transformers_torch_dtype = tf_config.get('torch_dtype', self.models.transformers_torch_dtype)
                                self.models.transformers_load_in_8bit = tf_config.get('load_in_8bit', self.models.transformers_load_in_8bit)
                                self.models.transformers_load_in_4bit = tf_config.get('load_in_4bit', self.models.transformers_load_in_4bit)
                            
                            if 'vllm' in backends:
                                vllm_config = backends['vllm']
                                self.models.vllm_tensor_parallel_size = vllm_config.get('tensor_parallel_size', self.models.vllm_tensor_parallel_size)
                                self.models.vllm_gpu_memory_utilization = vllm_config.get('gpu_memory_utilization', self.models.vllm_gpu_memory_utilization)
                                self.models.vllm_max_model_len = vllm_config.get('max_model_len', self.models.vllm_max_model_len)
                    
                    # Update other configurations similarly
                    for section in ['ai', 'tools', 'security', 'logging']:
                        if section in yaml_config:
                            section_obj = getattr(self, section)
                            for key, value in yaml_config[section].items():
                                if hasattr(section_obj, key):
                                    setattr(section_obj, key, value)
                    
                    # Update development settings
                    if 'development' in yaml_config:
                        dev_config = yaml_config['development']
                        self.development_mode = dev_config.get('debug_mode', self.development_mode)
                        self.hot_reload = dev_config.get('hot_reload', self.hot_reload)
                        self.profiling = dev_config.get('profiling', self.profiling)
                        self.mock_ai_responses = dev_config.get('mock_ai_responses', self.mock_ai_responses)
                        
            except Exception as e:
                print(f"Warning: Failed to load config.yaml: {e}")
    
    @property
    def project_root(self) -> Path:
        """Get server root directory (server folder as root)"""
        return Path(__file__).parent.parent
    
    @property
    def models_cache_path(self) -> Path:
        """Get models cache directory path"""
        return self.project_root / self.models.model_cache_dir
    
    @property
    def logs_path(self) -> Path:
        """Get logs directory path"""
        return self.project_root / "logs"


# Global settings instance
settings = Settings()
